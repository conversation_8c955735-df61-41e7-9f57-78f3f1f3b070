<template>
  <PageWrapper>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" v-auth="'system:menus:add'" preIcon="ant-design:plus-outlined" @click="handleCreate"> 新增菜单</a-button>
        <a-button type="primary" preIcon="ic:round-expand" @click="expandAll">展开全部</a-button>
        <a-button type="primary" preIcon="ic:round-compress" @click="collapseAll">折叠全部</a-button>

        <a-dropdown v-if="checkedKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined" />
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>批量操作
            <Icon icon="ant-design:down-outlined" />
          </a-button>
        </a-dropdown>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>
    <MenuDrawer :showFooter="showFooter" @register="registerDrawer" @success="handleSuccess" />
    <DataRuleList @register="registerDrawerReuse" />
  </PageWrapper>
</template>
<script lang="ts" name="system-menu" setup>
  import { ref } from 'vue';
  import { ActionItem, BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useDrawer } from '/@/components/Drawer';
  import MenuDrawer from './MenuDrawer.vue';
  import DataRuleList from './DataRuleList.vue';
  import { columns } from './menu.data';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { list, deleteMenu, batchDeleteMenu } from '/@/api/sys/menu';
  const checkedKeys = ref<Array<string | number>>([]);
  const showFooter = ref(true);
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerDrawerReuse] = useDrawer();
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    tableProps: {
      title: '菜单列表',
      api: list,
      columns: columns,
      size: 'small',
      pagination: false,
      isTreeTable: true,
      striped: true,
      showTableSetting: true,
      bordered: true,
      showIndexColumn: false,
      tableSetting: { fullScreen: true },
      useSearchForm: false,
      actionColumn: {
        width: 120,
      },
    },
  });
  //注册table数据
  const [registerTable, { reload, expandAll, collapseAll }] = tableContext;
  //新增
  function handleCreate() {
    showFooter.value = true;
    openDrawer(true, {
      isUpdate: false,
    });
  }
  // 编辑
  function handleEdit(record) {
    showFooter.value = true;
    openDrawer(true, {
      record,
      isUpdate: true,
    });
  }
  // 添加下级
  function handleAddSub(record) {
    openDrawer(true, {
      record: { parentId: record.id, menuType: 1 },
      isUpdate: false,
    });
  }
  // 删除
  async function handleDelete(record) {
    await deleteMenu({ id: record.id }, reload);
  }
  // 批量删除事件
  async function batchHandleDelete() {
    await batchDeleteMenu({ ids: checkedKeys.value }, reload);
  }
  //  成功回调
  function handleSuccess() {
    reload();
  }
  // 操作栏
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth:'system:menus:edit'
      },
    ];
  }
  // 下拉操作栏
  function getDropDownAction(record): ActionItem[] {
    return [
      {
        label: '添加下级',
        onClick: handleAddSub.bind(null, record),
        auth:'system:menus:addSuborde'
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
        auth:'system:menus:delete'
      },
    ];
  }
</script>
