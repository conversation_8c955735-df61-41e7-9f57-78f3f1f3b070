enum Api {
  login = '/sys/login',
  logout = '/sys/logout',
  getUserInfo = '/sys/user/getUserInfo',
  // 获取系统权限
  // 2、所有权限
  // 3、系统安全模式

  getUserPermByToken = '/sys/user/getPermissionByToken',
  //新加的获取图形验证码的接口
  getInputCode = '/sys/randomImage',
  //注册接口
  registerApi = '/sys/user/register',
  //校验用户接口
  checkOnlyUser = '/sys/user/checkOnlyUser',
  //校验手机号
  phoneVerify = '/sys/user/phoneVerification',
  //修改密码
  passwordChange = '/sys/user/passwordChange',
  listNoCareTenant = '/sys/user/listAll',
  list = '/sys/user/list',
  save = '/sys/user/add',
  edit = '/sys/user/edit',
  agentSave = '/sys/sysUserAgent/add',
  agentEdit = '/sys/sysUserAgent/edit',
  getUserRole = '/sys/user/queryUserRole',
  duplicateCheck = '/sys/duplicate/check',
  deleteUser = '/sys/user/delete',
  deleteBatch = '/sys/user/deleteBatch',
  importExcel = '/sys/user/importExcel',
  exportXls = '/sys/user/exportXls',
  putRecycleBin = '/sys/user/putRecycleBin',
  deleteRecycleBin = '/sys/user/deleteRecycleBin',
  allRolesList = '/sys/role/queryall',
  allRolesListNoByTenant = '/sys/role/queryallNoByTenant',
  allTenantList = '/sys/tenant/queryList',
  allPostList = '/sys/position/list',
  userDepartList = '/sys/user/userDepartList',
  changePassword = '/sys/user/changePassword',
  frozenBatch = '/sys/user/frozenBatch',
  getDictItemsByCode = '/sys/dict/getDictItems',
  //第三方4a登录
  thirdFourLogin = '/sys/login/4a',
  //第三方cam登录
  thirdCamLogin = '/sys/login/cam',

  // 获取RSA公钥
  getRsaPublicKey = `/sys/getRsaPublicKey`,

  departs = '/sys/sysDepart/queryTreeList',
  resetPassword = '/sys/user/resetRandomPassword',
  view = '/sys/user/getPersonalInfo',
}
export default Api;
