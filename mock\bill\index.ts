import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, baseUrl } from '../_util';
import API_URL from '@/api/url';
const billList = (() => {
  const result: BillItemModel[] = [];
  for (let index = 0; index < 20; index++) {
    result.push({
      id: '@guid',
      billTime: '@date',
      settleName: '河北移动',
      projectName: '移动公司-大数据',
      status: '1',
    });
  }
  return result;
})();

export default [
  {
    url: `${baseUrl}${API_URL.bill.billList}`,
    timeout: 200,
    method: 'post',
    response: () => {
      return resultSuccess({
        records: billList,
        total: 100,
      });
    },
  },
] as MockMethod[];
