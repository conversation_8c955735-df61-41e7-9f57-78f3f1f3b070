<template>
  <PageWrapper>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" 
        preIcon="ant-design:plus-outlined" 
        v-auth="'system:annount:add'"  
        @click="handleCreate"> 新增
        </a-button>
      </template>
      <template #showTime="{ record }">
         <span>{{record.startTime}} - {{record.endTime}}</span>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
    </BasicTable>
    <!--新增编辑弹窗-->
    <AnnountModal @register="registerPasswordModal" @success="handleSuccess" />
  </PageWrapper>
</template>
<script lang="ts" name="system-user" setup>
  import { Modal } from 'ant-design-vue';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import AnnountModal from './annountModal.vue';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { columns, searchFormSchema } from './annount.data';
  import { getAnnountList, annountWithdraw, getAnnountEnable } from '/@/api/annount/annount';

  const { createMessage } = useMessage();
  //model
  const [registerPasswordModal, { openModal }] = useModal();
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'annount-list',
    tableProps: {
      title: '公告列表',
      api: getAnnountList,
      rowSelection: {},
      columns: columns,
      size: 'small',
      // showIndexColumn: true,
      formConfig: {
        showAdvancedButton: false,
        labelWidth: 100,
        schemas: searchFormSchema,
        actionColOptions: {
          xs: 24, // <576px
          sm: 24, // ≥576px
          md: 24, // ≥768px
          lg: 24, // ≥992px
          xl: 24, // ≥1200px
          xxl: 24,
          style: { textAlign: 'right' },
        },
      },
      actionColumn: {
        width: 120,
      },
      beforeFetch: (params) => {
        delete params.column;
        delete params.order;
       
        let createTimes = params.dateRangeSelect && params.dateRangeSelect.includes(',') ?  params.dateRangeSelect.split(',') : ''
        console.log(createTimes)
        if(createTimes.length > 0){
           let startDate = createTimes[0]
           let endDate = createTimes[1]
           params.startTime = startDate
           params.endTime = endDate
        }
        delete params.dateRangeSelect
        console.log(params,'params')
        //return Object.assign({ column: 'createTime', order: 'desc' }, params);
      },
    },
  });

  //注册table数据
  const [registerTable, { reload }] = tableContext;
  // 新增事件
  function handleCreate() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }
  //编辑事件
  async function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  // 详情
  async function handleDetail(record: Recordable) {
    console.log(record,'record')
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  // 撤回
  async function handleDelete(record) {
      annountWithdraw({ id: record.id}).then(() => {
          handleSuccess();
      });
  }
  // 成功回调
  function handleSuccess() {
    reload();
  }
  // 激活
  async function handleEnable(record) {
    getAnnountEnable({ id: record.id}).then(() => {
        handleSuccess();
    });
  }
  // 操作栏
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '生效',
        popConfirm: {
          title: '是否确认生效',
          confirm: handleEnable.bind(null, record),
        },
        ifShow: record.status == 0,
        auth:"system:annount:takeEffect"
      },
      {
        label: '撤回',
        popConfirm: {
          title: '是否确认撤回',
          confirm: handleDelete.bind(null, record),
        },
        ifShow: record.status == 1,
        auth:"system:annount:widthDrew"
      },
    ];
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth:"system:annount:edit" 
      },
       {
        label: '查看',
        onClick: handleDetail.bind(null, record),
        auth:"system:annount:detail" 
      }
    ];
  }
</script>
