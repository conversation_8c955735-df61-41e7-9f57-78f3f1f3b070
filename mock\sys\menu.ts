import { resultSuccess, resultError, getRequestToken, requestParams, baseUrl } from '../_util';
import { MockMethod } from 'vite-plugin-mock';
//import { createFakeUserList } from './user';
import menuData from './menuData';

// // single
// const dashboardRoute = {
//   path: '/dashboard',
//   name: 'Dashboard',
//   component: 'LAYOUT',
//   redirect: '/dashboard/analysis',
//   meta: {
//     title: 'routes.dashboard.dashboard',
//     hideChildrenInMenu: true,
//     icon: 'bx:bx-home',
//   },
//   children: [
//     {
//       path: 'analysis',
//       name: 'Analysis',
//       component: '/dashboard/Analysis/index',
//       meta: {
//         hideMenu: true,
//         hideBreadcrumb: true,
//         title: 'routes.dashboard.analysis',
//         currentActiveMenu: '/dashboard',
//         icon: 'bx:bx-home',
//       },
//     },
//     {
//       path: 'workbench',
//       name: 'Workbench',
//       component: '/dashboard/workbench/index',
//       meta: {
//         hideMenu: true,
//         hideBreadcrumb: true,
//         title: 'routes.dashboard.workbench',
//         currentActiveMenu: '/dashboard',
//         icon: 'bx:bx-home',
//       },
//     },
//   ],
// };

// const backRoute = {
//   path: 'back',
//   name: 'PermissionBackDemo',
//   meta: {
//     title: 'routes.demo.permission.back',
//   },

//   children: [
//     {
//       path: 'page',
//       name: 'BackAuthPage',
//       component: '/demo/permission/back/index',
//       meta: {
//         title: 'routes.demo.permission.backPage',
//       },
//     },
//     {
//       path: 'btn',
//       name: 'BackAuthBtn',
//       component: '/demo/permission/back/Btn',
//       meta: {
//         title: 'routes.demo.permission.backBtn',
//       },
//     },
//   ],
// };

// const authRoute = {
//   path: '/permission',
//   name: 'Permission',
//   component: 'LAYOUT',
//   redirect: '/permission/front/page',
//   meta: {
//     icon: 'carbon:user-role',
//     title: 'routes.demo.permission.permission',
//   },
//   children: [backRoute],
// };

// const sysRoute = {
//   path: '/system',
//   name: 'System',
//   component: 'LAYOUT',
//   redirect: '/system/account',
//   meta: {
//     icon: 'ion:settings-outline',
//     title: 'routes.demo.system.moduleName',
//   },
//   children: [
//     {
//       path: 'account',
//       name: 'AccountManagement',
//       meta: {
//         title: 'routes.demo.system.account',
//         ignoreKeepAlive: true,
//       },
//       component: '/demo/system/account/index',
//     },
//     {
//       path: 'account_detail/:id',
//       name: 'AccountDetail',
//       meta: {
//         hideMenu: true,
//         title: 'routes.demo.system.account_detail',
//         ignoreKeepAlive: true,
//         showMenu: false,
//         currentActiveMenu: '/system/account',
//       },
//       component: '/demo/system/account/AccountDetail',
//     },
//     {
//       path: 'role',
//       name: 'RoleManagement',
//       meta: {
//         title: 'routes.demo.system.role',
//         ignoreKeepAlive: true,
//       },
//       component: '/demo/system/role/index',
//     },

//     {
//       path: 'menu',
//       name: 'MenuManagement',
//       meta: {
//         title: 'routes.demo.system.menu',
//         ignoreKeepAlive: true,
//       },
//       component: '/demo/system/menu/index',
//     },
//     {
//       path: 'dept',
//       name: 'DeptManagement',
//       meta: {
//         title: 'routes.demo.system.dept',
//         ignoreKeepAlive: true,
//       },
//       component: '/demo/system/dept/index',
//     },
//     {
//       path: 'changePassword',
//       name: 'ChangePassword',
//       meta: {
//         title: 'routes.demo.system.password',
//         ignoreKeepAlive: true,
//       },
//       component: '/demo/system/password/index',
//     },
//   ],
// };

export default [
  {
    url: `${baseUrl}/sys/permission/getUserPermissionByToken`,
    timeout: 1000,
    method: 'get',
    response: () => {
      //const token = getRequestToken(request);
      // if (!token) {
      //   return resultError('Invalid token!');
      // }
      // const checkUser = createFakeUserList().find((item) => item.token === token);
      // if (!checkUser) {
      //   return resultError('Invalid user token!');
      // }
      // const id = checkUser.userId;
      // let menu: Object[];
      // switch (id) {
      //   case '1':
      //     dashboardRoute.redirect = dashboardRoute.path + '/' + dashboardRoute.children[0].path;
      //     menu = [dashboardRoute, authRoute, levelRoute, sysRoute, linkRoute];
      //     break;
      //   case '2':
      //     dashboardRoute.redirect = dashboardRoute.path + '/' + dashboardRoute.children[1].path;
      //     menu = [dashboardRoute, authRoute, levelRoute, linkRoute];
      //     break;
      //   default:

      //}
      //const menu = [dashboardRoute, authRoute, sysRoute];

      return resultSuccess(menuData);
    },
  },
] as unknown as MockMethod[];
