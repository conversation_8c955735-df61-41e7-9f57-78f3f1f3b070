import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';

enum Api {
  llyxOrderList = '/biz/llyxOrder/list',
  llyxOrderExcel = '/biz/llyxOrder/export',
}

// export const getDictCheckList = (params) => {
//   return defHttp.post({ url: Api.dictCheckList, params });
// };

export const llyxOrderList = (params) => {
  return defHttp.post({ url: Api.llyxOrderList, params });
};

export const llyxOrderExcel = (params = {}) =>
  defHttp.post(
    {
      url: Api.llyxOrderExcel,
      params,
      responseType: 'blob', // 设置响应类型为blob
    },
    {
      isReturnNativeResponse: true, // 返回原始响应以获取headers
    }
  );
