<template>
  <div :id="selectId">
    <Select
      v-bind="computedAttrs"
      v-model:value="state"
      :options="getOptions"
      :open="open"
      @click="handleOpen"
      @select="handleSelect"
      @change="handleChange"
    >
      <template #dropdownRender="{ menuNode: menu }">
        <v-nodes :vnodes="menu" />
        <a-divider class="divider" />
        <a-space :id="dropdownId" class="space">
          <a-input v-model:value="footerModel.prev" placeholder="请输入" :suffix="inputSuffix" @change="handlePrevInputChange" />
          <span>-</span>
          <a-input v-model:value="footerModel.next" placeholder="请输入" :suffix="inputSuffix" @change="handleNextInputChange" />
          <a-button type="primary" @click="handleClick"> 确定 </a-button>
        </a-space>
      </template>
    </Select>
  </div>
</template>
<script lang="ts" setup>
  type OptionsItem = { label: string; value: string; disabled?: boolean };
  interface FooterConfig {
    inputSuffix?: string | Slot;
  }
  interface Props {
    value?: SelectValue;
    numberToString?: boolean;
    labelField?: string;
    valueField?: string;
    options?: DefaultOptionType[];
    footer?: FooterConfig;
  }
  import { defineProps, ref, computed, unref, reactive, toRaw, Slot, withDefaults, onMounted, onUnmounted } from 'vue';
  import { Select } from 'ant-design-vue';
  import { useRuleFormItem } from '/@/hooks/component/useFormItem';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { debounce, omit } from 'lodash-es';
  import { buildShortUUID } from '/@/utils/uuid';
  import { DefaultOptionType, SelectValue } from 'ant-design-vue/lib/select';
  const VNodes = (_, { attrs }) => {
    return attrs.vnodes;
  };
  const PREFIX = 'COMP_';
  const props = withDefaults(defineProps<Props>(), {
    value: '',
    numberToString: false,
    labelField: 'label',
    valueField: 'value',
    options: () => [],
    footer: () => ({}),
  });
  defineEmits(['options-change', 'change']);
  const open = ref<boolean>(false);
  const emitData = ref<any[]>([]);
  const selectId = ref(buildShortUUID(PREFIX));
  const dropdownId = ref(buildShortUUID(PREFIX));
  const attrs = useAttrs();
  const { footer } = props;
  const { inputSuffix } = footer;
  const footerModel = reactive({
    prev: '',
    next: '',
  });
  const [state, setState] = useRuleFormItem(props, 'value', 'change', emitData);
  let vModalValue;
  const computedAttrs = computed(() => {
    let obj: Recordable = unref(attrs) || {};
    if (obj && obj['onUpdate:value']) {
      vModalValue = obj['onUpdate:value'];
      delete obj['onUpdate:value'];
    }
    if (obj['filterOption'] === undefined) {
      obj['filterOption'] = (inputValue, option) => {
        if (typeof option['label'] === 'string') {
          return option['label'].toLowerCase().indexOf(inputValue.toLowerCase()) !== -1;
        } else {
          return true;
        }
      };
    }
    return obj;
  });
  const getOptions = computed(() => {
    const { labelField, valueField, numberToString } = props;
    const res = unref(props.options).reduce((prev, next: Recordable) => {
      if (next) {
        const value = next[valueField];
        (prev as Recordable[]).push({
          ...omit(next, [labelField, valueField]),
          label: next[labelField],
          value: numberToString ? `${value}` : value,
        });
      }
      return prev;
    }, [] as OptionsItem[]);
    return res;
  });
  onMounted(() => {
    window.addEventListener('click', clickOtherArea);
  });
  onUnmounted(() => {
    window.removeEventListener('click', clickOtherArea);
  });
  initValue();
  // 点击下拉区域外关闭下拉
  function clickOtherArea(e: HTMLElementEvent<HTMLElement>) {
    const { target } = e;
    let current: Nullable<HTMLElement> = target;
    while (current) {
      if (current.id === unref(selectId) || current.id === unref(dropdownId)) {
        return;
      }
      current = current.parentNode as Nullable<HTMLElement>;
    }
    open.value = false;
  }
  // prev input change 事件
  const handlePrevInputChange = debounce((e: ChangeEvent) => {
    const { target } = e;
    const value = target.value;
    footerModel.prev = value.replace(/[^0-9]/gi, '');
  }, 100);
  // next input change 事件
  const handleNextInputChange = debounce((e: ChangeEvent) => {
    const { target } = e;
    const value = target.value;
    footerModel.next = value.replace(/[^0-9]/gi, '');
  }, 100);
  // 初始化
  function initValue() {
    let value = props.value;
    if (value && typeof value === 'string' && value !== 'null' && value !== 'undefined') {
      setState(value.split(','));
    }
  }
  // change事件
  function handleChange(_, ...args) {
    vModalValue && vModalValue(_);
    emitData.value = args;
  }
  // 下拉显示
  function handleOpen() {
    open.value = true;
  }
  // 选中事件
  function handleSelect() {
    setTimeout(() => {
      open.value = false;
    }, 10);
  }
  // 确定点击事件
  function handleClick() {
    const { prev, next } = toRaw(footerModel);
    if (!prev && !next) {
      setState('');
    } else if (Number(prev) > Number(next)) {
      footerModel.prev = next;
      footerModel.next = prev;
      setState(`${footerModel.prev}-${footerModel.next}`);
    } else {
      setState(`${footerModel.prev}-${footerModel.next}`);
    }

    setTimeout(() => {
      open.value = false;
    }, 10);
  }
</script>
<style lang="less" scoped>
  .divider {
    margin: 4px 0;
  }
  .space {
    padding: 4px 8px;
  }
  .input-wrapper {
    position: relative;
  }
  .ant-input-suffix {
    position: absolute;
    top: 50%;
    z-index: 2;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.65);
    line-height: 0;
    transform: translateY(-50%);
    right: 0px;
  }
  /deep/ .ant-input-number-handler-wrap {
    display: none;
  }
</style>
