import { defHttp } from '/@/utils/http/axios';

enum Api {
  equityPoolList = '/biz/equityConfigPool/list',
  getAddRightsAndCards = '/biz/equityConfigPool/getAddRightsAndCards',
  getById = '/biz/equityConfigPool/getById',
  equityPoolAdd = '/biz/equityConfigPool/add',
  equityPoolAddCard = '/biz/equityConfigPool/addCard',
}

// export const getDictCheckList = (params) => {
//   return defHttp.post({ url: Api.dictCheckList, params });
// };

export const equityPoolList = (params) => {
  return defHttp.get({ url: Api.equityPoolList, params });
};

export const getAddRightsAndCards = (params) => {
  return defHttp.get({ url: Api.getAddRightsAndCards, params });
};

export const getById = (params) => {
  return defHttp.get({ url: Api.getById, params });
};

export const equityPoolAdd = (params) => {
  return defHttp.post({ url: Api.equityPoolAdd, params });
};

export const equityPoolAddCard = (params) => {
  return defHttp.post({ url: Api.equityPoolAddCard, params });
};

// export const fullEquityQuery = (params) => {
//   return defHttp.get({ url: Api.fullEquityQuery, params });
// };

// export const fullEquitySet = (params) => {
//   return defHttp.post({ url: Api.fullEquitySet, params });
// };

// export const srcOrderExcel = (params = {}) =>
//   defHttp.get(
//     {
//       url: Api.srcOrderExcel,
//       params,
//       responseType: 'blob', // 设置响应类型为blob
//     },
//     {
//       isReturnNativeResponse: true, // 返回原始响应以获取headers
//     }
//   );
