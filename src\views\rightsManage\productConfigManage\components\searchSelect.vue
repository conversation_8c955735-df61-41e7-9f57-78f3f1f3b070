<template>
  <!-- 添加ref到最外层容器 -->
  <div class="search-select-container" ref="containerRef">
    <a-space>
      <div class="custom-search-select">
        <a-input
          v-model:value="inputKeyword"
          placeholder="请输入关键词搜索"
          @pressEnter="handleSearch"
          style="width: 400px"
          allow-clear
          @click.stop=""
        />

        <div v-show="showDropdown && filteredResults.length > 0" class="custom-dropdown" @click.stop="">
          <div
            v-for="item in filteredResults"
            :key="item.commodityId"
            class="custom-option"
            @mouseenter="hoverItem = item.commodityId"
            @mouseleave="hoverItem = null"
          >
            <div>{{ item.commodityName }} &nbsp;&nbsp;&nbsp;&nbsp;{{ item.commodityCode }}</div>
            <a-button v-show="hoverItem === item.commodityId" type="link" size="small" @click.stop="handleAdd(item)">
              <template #icon><PlusOutlined /></template>
              添加
            </a-button>
          </div>
        </div>
      </div>

      <a-button type="primary" @click="handleSearch" :loading="loading" :disabled="!inputKeyword.trim()"> 查询 </a-button>
    </a-space>
  </div>
</template>

<script lang="ts">
  export default { name: 'SearchSelect' };
</script>

<script setup lang="ts">
  import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { getCommodityListList } from '/@/api/productconfig/productconfig';

  interface SearchItem {
    commodityId: number;
    commodityCode: string;
    commodityName: string;
    [key: string]: any;
  }

  const props = defineProps<{
    excludedValues?: number[];
  }>();

  const emit = defineEmits<{
    (e: 'add', item: SearchItem): void;
  }>();

  const inputKeyword = ref('');
  const rawResults = ref<SearchItem[]>([]);
  const loading = ref(false);
  const hoverItem = ref<number | null>(null);
  const showDropdown = ref(false);
  const containerRef = ref<HTMLElement | null>(null);

  // 过滤结果
  const filteredResults = computed(() => {
    return rawResults.value.filter((item) => !props.excludedValues?.includes(item.commodityId));
  });

  const handleAdd = (item: SearchItem) => {
    emit('add', item);
    showDropdown.value = true;
  };

  const handleSearch = async () => {
    if (!inputKeyword.value.trim()) return;

    try {
      loading.value = true;
      const res = await getCommodityListList({
        pageNo: 1,
        pageSize: 10000,
        commodityCategory: 0,
        status: 1,
        mainProductStatus: 1,
        mainProductServiceCat: 2,
        commodityName: inputKeyword.value,
      });
      console.log('res', res);
      rawResults.value = res.records || [];
      showDropdown.value = true;
    } finally {
      loading.value = false;
    }
  };

  // 点击外部关闭下拉框
  const handleClickOutside = (event: MouseEvent) => {
    if (containerRef.value && !containerRef.value.contains(event.target as Node)) {
      showDropdown.value = false;
    }
  };

  // ESC键关闭
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      showDropdown.value = false;
    }
  };

  onMounted(() => {
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);
  });

  onBeforeUnmount(() => {
    inputKeyword.value = '';
    document.removeEventListener('mousedown', handleClickOutside);
    document.removeEventListener('keydown', handleKeyDown);
  });

  watch(inputKeyword, () => {
    if (!inputKeyword.value.trim()) {
      showDropdown.value = false;
    }
  });
</script>

<style scoped>
  /* 原有样式保持不变 */
  .custom-search-select {
    position: relative;
    display: inline-block;
  }

  .custom-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 400px;
    max-height: 250px;
    overflow-y: auto;
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    margin-top: 4px;
  }

  .custom-option {
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
  }

  .custom-option:hover {
    background-color: #f5f5f5;
  }
</style>
