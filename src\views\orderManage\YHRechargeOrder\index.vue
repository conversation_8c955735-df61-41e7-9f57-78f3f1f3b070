<template>
  <PageWrapper>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'order:YHRecharge:excel'" @click="excelOrder"> 导出</a-button>
      </template>
    </BasicTable>
    <!-- 验收弹窗 -->
    <!-- <CheckModel @register="registerCheckModal" @success="handleSuccess" /> -->
    <!-- <transferModel @register="registerPropertyModal" @success="handleSuccess" /> -->
  </PageWrapper>
</template>

<script lang="ts" name="orderManage-transfer" setup>
  import { reactive } from 'vue';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction, ActionItem, TableImg } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { llyxOrderList, llyxOrderExcel } from '/@/api/order/YHrecharge';
  import { columns, searchFormSchema } from './YHOrder.data';
  import { useModal } from '/@/components/Modal';
  import { Modal } from 'ant-design-vue';
  import { exportExcel } from '/@/utils/common/compUtils';
  import { func } from 'vue-types';
  // import transferModel from './components/transferModel.vue';
  const [registerPropertyModal, { openModal }] = useModal();
  // 列表页面公共参数、方法
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'jsUser-list',
    tableProps: {
      api: llyxOrderList,
      rowSelection: {},
      columns: columns,
      size: 'small',
      showIndexColumn: false,
      showActionColumn: false,
      formConfig: {
        showAdvancedButton: false,
        labelWidth: 100,
        schemas: searchFormSchema,
        actionColOptions: {
          xs: 24, // <576px
          sm: 24, // ≥576px
          md: 24, // ≥768px
          lg: 24, // ≥992px
          xl: 24, // ≥1200px
          xxl: 24,
          style: { textAlign: 'right' },
        },
      }, // 是否使用搜索项
      // actionColumn: {
      //   width: 120,
      // },
      beforeFetch: (params) => {
        delete params.column;
        delete params.order;
        let createTimes = params.dateOp && params.dateOp.includes(',') ? params.dateOp.split(',') : '';
        if (createTimes.length > 0) {
          let startDate = createTimes[0] + ' 00:00:00';
          let endDate = createTimes[1] + ' 23:59:59';
          params.orderStartTime = startDate;
          params.orderEndTime = endDate;
        }
        let createTimes2 = params.dateOp2 && params.dateOp2.includes(',') ? params.dateOp2.split(',') : '';
        if (createTimes2.length > 0) {
          let startDate = createTimes2[0] + ' 00:00:00';
          let endDate = createTimes2[1] + ' 23:59:59';
          params.resultStartTime = startDate;
          params.resultEndTime = endDate;
        }
        delete params.dateOp;
        delete params.dateOp2;
        console.log(params, 'params');
      },
    },
  });

  //注册table数据
  const [registerTable, { reload, getForm }] = tableContext;
  let recordData = reactive({});
  function handleDetail(record: Recordable) {
    Object.assign(recordData, record);
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  function excelOrder() {
    // 导出excel
    Modal.confirm({
      title: '是否导出洋河话费充值订单？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        let { getFieldsValue } = getForm();
        let params = Object.assign({}, getFieldsValue());
        let createTimes = params.dateOp && params.dateOp.includes(',') ? params.dateOp.split(',') : '';
        if (createTimes.length > 0) {
          let startDate = createTimes[0] + ' 00:00:00';
          let endDate = createTimes[1] + ' 23:59:59';
          params.orderStartTime = startDate;
          params.orderEndTime = endDate;
        }
        let createTimes2 = params.dateOp2 && params.dateOp2.includes(',') ? params.dateOp2.split(',') : '';
        if (createTimes2.length > 0) {
          let startDate = createTimes2[0] + ' 00:00:00';
          let endDate = createTimes2[1] + ' 23:59:59';
          params.resultStartTime = startDate;
          params.resultEndTime = endDate;
        }
        delete params.dateOp;
        delete params.dateOp2;
        const res = await llyxOrderExcel(params);
        exportExcel(res);
      },
    });
  }
  function getTableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '查看',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '转移',
        onClick: () => {
          openModal(true, {
            record,
            isUpdate: false,
            showFooter: false,
          });
        },
      },
    ];
  }
  function handleSuccess() {
    reload();
  }
</script>
