<template>
  <!-- 报告列表 -->
  <PageWrapper contentFullHeight>
    <h3>{{ route.query.title }}报告</h3>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-select v-model:value="selected" :options="options" placeholder="请选择排序方式" @change="selectedChange" class="tableSelect" />
      </template>
      <template #toolbar>
        <div class="my-2">
          <a-button v-auth="'file:add'" type="primary" @click="uploadVisible = true">上传</a-button>
          <a-button class="ml-2" type="primary" @click="goBack">返回</a-button>
        </div>
      </template>
      <template #action="{ record }">
        <TableAction :stopButtonPropagation="true" :actions="getActions(record)" />
      </template>
    </BasicTable>
  </PageWrapper>
  <!-- 上传报告模态框 -->
  <a-modal v-model:visible="uploadVisible" title="上传报告" :afterClose="() => (fileList = [])">
    <div class="text-center p-5">
      <span>选择文件：</span>
      <a-upload v-model:file-list="fileList" :max-count="1" :before-upload="beforeUpload" @remove="handleRemove">
        <a-button>
          <upload-outlined />
          点击上传本地文件
        </a-button>
      </a-upload>
    </div>
    <template #footer>
      <a-button @click="toCancelUpload">取消</a-button>
      <a-button type="primary" :disabled="!fileList.length" :loading="uploading" @click="toUpload">确认上传</a-button>
    </template>
  </a-modal>
</template>

<script>
  import { defineComponent, ref } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { message, Upload, Modal } from 'ant-design-vue';
  import { UploadOutlined } from '@ant-design/icons-vue';
  import { BasicTable, TableAction, useTable } from '/@/components/Table';
  import { getReportListApi, deleteReport, uploadReport } from '/@/api/list';
  import { columns, options } from './columns/data';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { downloadByScript } from '/@/utils/common/compUtils';

  export default defineComponent({
    components: { BasicTable, TableAction, PageWrapper, UploadOutlined },
    setup() {
      const router = useRouter();
      const route = useRoute();

      // 默认选中时间逆序
      const selected = ref('2');
      const uploadVisible = ref(false);
      // 上传文件列表
      const fileList = ref([]);
      const uploading = ref(false);

      const [registerTable, { reload }] = useTable({
        api: getReportListApi,
        columns,
        bordered: true,
        showIndexColumn: true,
        actionColumn: {
          width: 300,
          title: '操作',
          dataIndex: 'action',
          slots: { customRender: 'action' },
          fixed: 'right',
        },
        // 发送请求前添加一些参数
        beforeFetch: (params) => {
          return Object.assign(params, { sortType: selected.value, reportType: route.query.reportType });
        },
      });

      // 查看报告
      function handleView(record) {
        router.push({
          path: '/workbench/pdf',
          query: {
            id: record.id,
            ...route.query,
          },
        });
      }

      // 删除报告
      function handleDelete(record) {
        Modal.confirm({
          title: '确认删除此数据?',
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            deleteReport({ id: record.id }).then((res) => {
              message.success(res || '删除成功');
              reload();
            });
          },
        });
      }

      // 导出报告
      function handleExport(record) {
        Modal.confirm({
          title: '确认导出此数据?',
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            downloadByScript(record.id);
          },
        });
      }

      // 上传报告前校验文件类型
      function beforeUpload(file) {
        let isPdf = file.type === 'application/pdf';
        if (!isPdf) {
          message.error('只能上传 PDF 格式的文件!');
        } else {
          fileList.value = [file];
        }
        return false || Upload.LIST_IGNORE;
      }

      // 从文件列表中移除文件
      function handleRemove(file) {
        const index = fileList.value.indexOf(file);
        const newFileList = fileList.value.slice();
        newFileList.splice(index, 1);
        fileList.value = newFileList;
      }

      // 上传报告
      function toUpload() {
        uploading.value = true;
        const formData = new FormData();
        formData.append('file', fileList.value[0]);
        uploadReport(formData, route.query.reportType)
          .then(() => {
            message.success('上传成功');
            uploadVisible.value = false;
            reload();
          })
          .finally(() => {
            uploading.value = false;
            fileList.value = [];
          });
      }

      // 取消上传
      function toCancelUpload() {
        fileList.value = [];
        uploadVisible.value = false;
      }

      function goBack() {
        router.replace({
          path: '/workbench',
        });
      }

      // 改变排序的时候刷新
      function selectedChange() {
        reload();
      }

      const getActions = (record) => [
        {
          label: '查看',
          onClick: handleView.bind(null, record),
        },
        {
          label: '删除',
          onClick: handleDelete.bind(null, record),
          auth: 'file:delete',
        },
        {
          label: '导出',
          onClick: handleExport.bind(null, record),
        },
      ];

      return {
        registerTable,
        handleView,
        handleDelete,
        handleExport,
        beforeUpload,
        handleRemove,
        toUpload,
        toCancelUpload,
        goBack,
        selectedChange,
        options,
        selected,
        uploadVisible,
        fileList,
        route,
        uploading,
        getActions,
      };
    },
  });
</script>

<style lang="less" scoped>
  :deep(.jeecg-basic-table-header__toolbar) {
    width: 200px;

    & > * {
      margin-right: 0;
    }
  }
  .tableSelect {
    width: 200px;
  }
</style>
