<template>
  <BasicDrawer :title="title" :width="600" v-bind="$attrs" :showFooter="true" @register="registerDrawer" @ok="handleOk">
    <BasicForm ref="basform" @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup>
  import { watch, computed, ref, unref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { saveOrUpdateDepart } from '/@/api/sys/depart';
  import { useBasicFormSchema, orgCategoryOptions } from '../depart.data';
  const emit = defineEmits(['success', 'register']);
  const props = defineProps({
    rootTreeData: { type: Array, default: () => [] },
  });
  let record: Recordable = {};
  // 当前是否是更新模式
  const isUpdate = ref<boolean>(false);
  // 当前的弹窗数据
  const model = ref<object>({});
  const title = computed(() => (isUpdate.value ? '编辑' : '新增'));

  //注册表单
  const [registerForm, { resetFields, setFieldsValue, validate, updateSchema }] = useForm({
    schemas: useBasicFormSchema().basicFormSchema,
    showActionButtonGroup: false,
  });

  // 注册弹窗
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    await resetFields();
    isUpdate.value = unref(data?.isUpdate);
    // 当前是否为添加子级
    let isChild = unref(data?.isChild);
    let categoryOptions = isChild ? orgCategoryOptions.child : orgCategoryOptions.root;
    
    
    // 隐藏不需要展示的字段
    updateSchema([
      {
        field: 'parentId',
        show: isChild,
        componentProps: {
          // 如果是添加子部门，就禁用该字段
          disabled: isChild,
          treeData: props.rootTreeData,
        },
      },
      {
        field: 'orgCode',
        dynamicDisabled: () => unref(isUpdate),
      },
    ]);

    record = unref(data?.record);
    if (typeof record !== 'object') {
      record = {};
    }
    // 赋默认值
    record = Object.assign(
      {
        departOrder: 0,
        orgCategory: categoryOptions[0]?.value,
      },
      record
    );
    model.value = record;
    await setFieldsValue({ ...record });
  });

  const basformRef = ref();
  watch(
    () => props.rootTreeData,
    (value) => {
      if (basformRef.value) {
        updateSchema({
          field: 'parentId',
          componentProps: {
            treeData: value,
          },
        });
      }
    }
  );
  // 提交事件
  async function handleOk() {
    try {
      setDrawerProps({ confirmLoading: true });
      let values = await validate();
      //提交表单
      isUpdate.value === true && (values.id = record.id);
      await saveOrUpdateDepart(values, isUpdate.value);
      //关闭弹窗
      closeDrawer();
      //刷新列表
      emit('success');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
