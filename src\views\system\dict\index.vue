<template>
  <!--引用表格-->
  <PageWrapper>
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'system:dict:add'" preIcon="ant-design:plus-outlined" @click="handleCreate"> 新增</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>

    <!--字典弹窗-->
    <DictModal @register="registerModal" @success="handleSuccess" />
    <!--字典配置抽屉-->
    <DictItemList @register="registerDrawer" />
  </PageWrapper>
</template>

<script lang="ts" name="system-dict" setup>
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useDrawer } from '/@/components/Drawer';
  import { useModal } from '/@/components/Modal';
  import DictItemList from './components/DictItemList.vue';
  import DictModal from './components/DictModal.vue';
  import { columns, searchFormSchema } from './dict.data';
  import { list, deleteDict } from '/@/api/sys/dict';

  //字典model
  const [registerModal, { openModal }] = useModal();
  //字典配置drawer
  const [registerDrawer, { openDrawer }] = useDrawer();
  import { useListPage } from '/@/hooks/system/useListPage';

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'dict-template',
    tableProps: {
      title: '数据字典',
      rowSelection: {},
      api: list,
      size: 'small',
      showIndexColumn: false,
      columns: columns,
      formConfig: {
        labelWidth: 100,
        schemas: searchFormSchema,
      },
      actionColumn: {
        width: 240,
        fixed: 'right',
      },
    },
  });

  //注册table数据
  const [registerTable, { reload, updateTableDataRecord }] = tableContext;

  /**
   * 新增事件
   */
  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }
  /**
   * 编辑事件
   */
  async function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteDict({ id: record.id }, reload);
  }
  /**
   * 成功回调
   */
  function handleSuccess({ isUpdate, values }) {
    if (isUpdate) {
      updateTableDataRecord(values.id, values);
    } else {
      reload();
    }
  }
  /**
   * 字典配置
   */
  function handleItem(record) {
    openDrawer(true, {
      id: record.id,
    });
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'system:dict:edit'
      },
      {
        label: '字典配置',
        onClick: handleItem.bind(null, record),
        auth: 'system:dict:config'
      },
      {
        label: '删除',
        popConfirm: {
          title: '确定删除吗?',
          confirm: handleDelete.bind(null, record),
        },
        auth: 'system:dict:delete'
      },
    ];
  }
</script>
