<template>
  <BasicModal
    v-bind="$attrs"
    title="转赠记录"
    :defaultFullscreen="false"
    width="300px"
    @register="registerModal"
    :showCancelBtn="false"
    :show-ok-btn="false"
  >
    <div v-if="stepsList.length">
      <div style="margin-bottom: 16px">当前归属手机号：{{ stepPhone }}</div>
      <a-steps progress-dot :current="1" direction="vertical">
        <a-step v-for="item in stepsList" :key="item.id">
          <template #description>
            <div class="custom-desc">
              <div>转赠时间：{{ item.createTime }}</div>
              <div>转赠手机号：{{ item.giver }}</div>
              <div>被转赠手机号：{{ item.receiver }}</div>
            </div>
          </template>
        </a-step>
      </a-steps>
    </div>
    <div v-else style="color: #999; text-align: center; margin-top: 48px">暂无记录</div>
  </BasicModal>
</template>
<script lang="ts">
  // 单独添加一个script块用于导出
  export default {
    name: 'orderManage-transferModel', // 保持与你原来的name一致
  };
</script>
<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { giveList } from '/@/api/order/group-issue';
  const emit = defineEmits(['success', 'register']);
  let stepPhone = ref<string>('');
  let stepsList = reactive<any>([]);
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    let res = await giveList({ transId: data.record.transId });
    console.log(res, '===>data');
    stepsList.length = 0;
    stepsList.push(...res.records);
    stepPhone = res.nowHolder;
    console.log(closeModal);
  });
</script>
<style scoped>
  .ml8 {
    margin-left: 8px;
  }
  .tips {
    color: #999;
  }
  .custom-desc > div {
    margin-bottom: 8px;
    line-height: 16px;
  }
</style>
