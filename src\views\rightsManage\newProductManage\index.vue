<template>
  <PageWrapper>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:import-outlined" v-auth="'rightsManage:product:import'" @click="uploadClick"
          >批量上传修改
        </a-button>
        <a-button type="primary" v-auth="'rightsManage:product:makeCard'" preIcon="ant-design:export-outlined" @click="madeCardClick"
          >批量制卡
        </a-button>
        <a-button type="primary" v-auth="'rightsManage:product:offShelf'" preIcon="ant-design:export-outlined" @click="handleUpshelf('2', null)"
          >批量下架
        </a-button>
        <a-button type="primary" v-auth="'rightsManage:product:upShelf'" preIcon="ant-design:export-outlined" @click="handleUpshelf('1', null)"
          >批量上架
        </a-button>
        <a-button type="primary" v-auth="'rightsManage:product:add'" preIcon="ant-design:export-outlined" @click="handleCreate">新增 </a-button>
        <a-button type="primary" v-auth="'rightsManage:product:export'" preIcon="ant-design:export-outlined" @click="exportClick">导出 </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <product-modal @register="registerPropertyModal" @success="handleSuccess" />
    <detailModel @register="registerAdjustModel" @success="handleSuccess" />
    <a-modal
      v-model:visible="openShow"
      title="文件上传确认"
      ok-text="确认上传"
      cancel-text="取消"
      :closable="false"
      :maskClosable="false"
      @ok="handleUpload"
      @cancel="handleCancel"
    >
      <div style="padding: 20px; margin: 0 auto">
        <p>请确认文件为最新导出文件，防止与实际存在误差,商品可修改字段【产品名称，产品面值】</p>
        <a-upload accept=".xls,.xlsx" name="file" :max-count="1" @change="handleChange" :before-upload="beforeUpload">
          <a-button type="primary">选择文件</a-button>
        </a-upload>
      </div>
    </a-modal>
  </PageWrapper>
</template>

<script lang="ts" name="basic-table-demo" setup>
  import { ref, unref } from 'vue';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicColumn, BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { columns, searchFormSchema } from './product.data';
  import {
    getCommodityListList,
    commodityCreateCard,
    commoditySetStatus,
    getExportExcel,
    customUpload,
    getProvinceList,
  } from '/@/api/productconfig/productconfig';
  import { exportExcel } from '/@/utils/common/compUtils';
  import { Modal } from 'ant-design-vue';
  import productModal from './productModal.vue';
  import detailModel from './detailModel.vue';
  const { createMessage } = useMessage();
  let searchParams = {};
  const openShow = ref(false);
  const fileList = ref<any[]>([]);
  const provinceList = ref([]);
  //model
  const [registerPropertyModal, { openModal }] = useModal();
  const [registerAdjustModel, { openModal: openDetailModal }] = useModal();
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'basic-table-demo',
    tableProps: {
      columns: columns,
      rowKey: 'commodityId',
      size: 'small',
      api: getCommodityListList,
      rowSelection: { type: 'checkbox' }, //默认是 checkbox 多选，可以设置成 radio 单选
      formConfig: {
        showAdvancedButton: false,
        labelWidth: 100,
        schemas: searchFormSchema,
        actionColOptions: {
          xs: 24, // <576px
          sm: 24, // ≥576px
          md: 24, // ≥768px
          lg: 24, // ≥992px
          xl: 24, // ≥1200px
          xxl: 24,
          style: { textAlign: 'right' },
        },
      },
      actionColumn: {
        width: 150,
        fixed: 'right',
        title: '操作',
        dataIndex: 'action',
      },
      beforeFetch: (params) => {
        delete params.column;
        delete params.order;
        console.log(params, '1233');
        searchParams = params;
        // return Object.assign({ column: 'createTime', order: 'desc' }, params);
      },
    },
  });
  //注册table数据
  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;
  function handleCreate(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: false,
      showFooter: true,
    });
  }
  //查看详情
  function handleDetail(record: Recordable) {
    openDetailModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  //编辑事件
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  function uploadClick() {
    openShow.value = true;
  }
  //上传前处理
  function beforeUpload(file) {
    //console.log(file,'file')
    fileList.value = [file];
    //console.log(fileList.value,'file')
    return false;
  }

  function handleChange({ file, fileList }) {
    // 处理上传好的文件
    if (file.status != 'uploading') {
      console.log(file, fileList);
    }
  }

  async function handleUpload() {
    if (fileList.value.length === 0) {
      createMessage.warning('请先选择文件');
      return;
    }
    let file = fileList.value[0];
    console.log(fileList.value[0]);
    // const file = fileList.value[0];
    // createMessage.success(`开始上传: ${file.name}`);

    // 实际的上传代码
    // uploadFile(file);
    const formData = new FormData();
    formData.append('file', fileList.value[0]);
    formData.append('type', 'adjustmentVoucherFile');
    const res = await customUpload({ file });
    if (res.data?.code === 200) {
      createMessage.warning('操作成功');
      openShow.value = false;
      fileList.value = [];
      handleSuccess();
    } else {
      createMessage.warning(res.data?.message || '上传失败');
    }
  }
  function handleCancel() {
    openShow.value = false;
    fileList.value = [];
  }
  //一键制卡
  function madeCardClick() {
    // console.log(selectedRowKeys.value,'checkedKeys.value',selectedRows.value)
    Modal.confirm({
      title: '您确定一键制卡吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        console.log(selectedRowKeys.value, 'checkedKeys.value', selectedRows.value);
        if (selectedRowKeys.value.length == 0) {
          Modal.warning({
            title: '请勾选要一键制卡的商品',
            okText: '确定',
            onOk: () => {},
          });
        } else {
          console.log(selectedRowKeys.value, 'checkedKeys.value');
          commodityCreateCard({ idList: selectedRowKeys.value }).then(() => {
            handleSuccess();
          });
          selectedRowKeys.value = [];
        }
      },
    });
  }
  //上下架
  function handleUpshelf(type, record) {
    console.log(type, record);
    if (record) {
      let params = {
        commodityIds: [record.commodityId],
        status: type,
      };
      commoditySetStatus(params).then(() => {
        handleSuccess();
        type == 1 ? createMessage.success('上架成功') : createMessage.success('下架成功');
      });
    } else {
      if (selectedRowKeys.value.length == 0) {
        Modal.confirm({
          title: type == 1 ? '请勾选要上架的商品' : '请勾选要下架的商品',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {},
        });
      } else {
        let params = {
          commodityIds: selectedRowKeys.value,
          status: type,
        };
        commoditySetStatus(params).then(() => {
          type == 1 ? createMessage.success('上架成功') : createMessage.success('下架成功');
          handleSuccess();
          selectedRowKeys.value = [];
        });
      }
    }
  }
  // 成功回调
  function handleSuccess() {
    reload();
  }
  // 操作栏
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '浏览',
        onClick: handleDetail.bind(null, record),
        auth: 'rightsManage:product:detail',
      },
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'rightsManage:product:edit',
      },
      {
        label: '下架',
        onClick: () => handleUpshelf('2', record),
        ifShow: record.status == 1,
        auth: 'rightsManage:product:offShelf',
      },
      {
        label: '上架',
        onClick: () => handleUpshelf('1', record),
        ifShow: record.status == 2 || record.status == 0,
        auth: 'rightsManage:product:upShelf',
      },
    ];
  }
  async function exportClick() {
    console.log(searchParams, 'searchParams');
    try {
      const res = await getExportExcel(searchParams);
      exportExcel(res);
      createMessage.success('导出成功');
    } catch (error: any) {
      createMessage.error('导出失败：' + error.message);
    }
  }
</script>

<style scoped></style>
