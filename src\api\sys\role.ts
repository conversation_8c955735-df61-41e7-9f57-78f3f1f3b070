import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';
import URL from '/@/api/url';

/**
 * 导出api
 */
export const getExportUrl = URL.role.exportXls;
/**
 * 导入api
 */
export const getImportUrl = URL.role.importExcel;
/**
 * 系统角色列表
 * @param params
 */
export const list = (params) => defHttp.post({ url: URL.role.list, params });
/**
 * 租户角色列表
 * @param params
 */
export const listByTenant = (params) => defHttp.post({ url: URL.role.listByTenant, params });

/**
 * 删除角色
 */
export const deleteRole = (params, handleSuccess) => {
  return defHttp.post({ url: URL.role.deleteRole, params }).then(() => {
    handleSuccess();
  });
};
/**
 * 批量删除角色
 * @param params
 */
export const batchDeleteRole = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.post({ url: URL.role.deleteBatch, data: params }).then(() => {
        handleSuccess();
      });
    },
  });
};
/**
 * 保存或者更新角色
 * @param params
 */
export const saveOrUpdateRole = (params, isUpdate) => {
  const url = isUpdate ? URL.role.edit : URL.role.save;
  return defHttp.post({ url: url, params });
};
/**
 * 编码校验
 * @param params
 */
export const isRoleExist = (params) => defHttp.post({ url: URL.role.isRoleExist, params }, { isTransformResponse: false });
/**
 * 根据角色查询树信息
 */
export const queryTreeListForRole = () => defHttp.post({ url: URL.role.queryTreeListForRole });
/**
 * 查询角色权限
 */
export const queryRolePermission = (params) => defHttp.post({ url: URL.role.queryRolePermission, params });
/**
 * 保存角色权限
 */
export const saveRolePermission = (params) => defHttp.post({ url: URL.role.saveRolePermission, params });
/**
 * 查询角色数据规则
 */
export const queryDataRule = (params) =>
  defHttp.post({ url: `${URL.role.queryDataRule}/${params.functionId}/${params.roleId}` }, { isTransformResponse: false });
/**
 * 保存角色数据规则
 */
export const saveDataRule = (params) => defHttp.post({ url: URL.role.queryDataRule, params });
/**
 * 获取表单数据
 * @return List<Map>
 */
export const getParentDesignList = () => defHttp.post({ url: URL.role.getParentDesignList });
/**
 * 获取角色表单数据
 * @return List<Map>
 */
export const getRoleDegisnList = (params) => defHttp.post({ url: URL.role.getRoleDegisnList, params });
/**
 * 提交角色工单信息
 */
export const saveRoleDesign = (params) => defHttp.post({ url: URL.role.saveRoleDesign, params });
/**
 * 角色列表接口
 * @param params
 */
export const userList = (params) => defHttp.post({ url: URL.role.userList, params });
/**
 * 删除角色用户
 */
export const deleteUserRole = (params, handleSuccess) => {
  return defHttp.post({ url: URL.role.deleteUserRole, params }).then(() => {
    handleSuccess();
  });
};
/**
 * 批量删除角色用户
 * @param params
 */
export const batchDeleteUserRole = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.post({ url: URL.role.batchDeleteUserRole, params }).then(() => {
        handleSuccess();
      });
    },
  });
};
/**
 * 添加已有用户
 */
export const addUserRole = (params, handleSuccess) => {
  return defHttp.post({ url: URL.role.addUserRole, params }).then(() => {
    handleSuccess();
  });
};
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdateRoleIndex = (params, isUpdate) => {
  const url = isUpdate ? URL.role.editRoleIndex : URL.role.saveRoleIndex;
  return defHttp.post({ url: url, params });
};
/**
 * 根据code查询首页配置
 * @param params
 */
export const queryIndexByCode = (params) => defHttp.post({ url: URL.role.queryIndexByCode, params }, { isTransformResponse: false });
