import { defHttp } from '/@/utils/http/axios';

enum Api {
  pageList = '/biz/banner/pageList',
  statusCheck = '/biz/banner/status',
  bannerSave = '/biz/banner/save',
  bannerUpdate = '/biz/banner/update',
  getCheckDatail = '/biz/dictAcceptance/acceptance',
}
// banner列表
export const postBannerList = (params) => {
  return defHttp.post({ url: Api.pageList, params });
};

// banner启用/禁用
export const postStatusCheck = (params) => {
  return defHttp.post({ url: Api.statusCheck, params });
};

// banner新增
export const postBannerSave = (params) => {
  return defHttp.post({ url: Api.bannerSave, params });
};

// banner编辑
export const postBannerUpdata = (params) => {
  return defHttp.post({ url: Api.bannerUpdate, params });
};
