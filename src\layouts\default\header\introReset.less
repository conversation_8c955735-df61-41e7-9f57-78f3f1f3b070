.intro-container {
  width: 600px;
  max-width: 600px;
  .intro-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f6f6f6;
    padding: 10px 15px;
    margin: 10px 0;
    border-radius: 6px;
    .intro-icon {
      width: 32px;
      height: 32px;
      margin-right: 10px;
    }
    .intro-text {
      flex: 1;
    }
    .intro-btn {
      color: @primary-color;
      padding: 10px;
      cursor: pointer;
    }
  }
  .introjs-button {
    background-color: @primary-color;
    border-color: @primary-color;
    text-shadow: none;
    color: #fff;
    padding: 0.25rem 1rem;
  }
}
