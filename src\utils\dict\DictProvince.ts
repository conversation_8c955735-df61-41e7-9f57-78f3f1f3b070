export enum Province {
  BEI_JING = '北京市',
  TIAN_JIN = '天津市',
  HE_BEI = '河北省',
  SHAN_XI = '山西省',
  NEI_MENG_GU = '内蒙古自治区',
  LIAO_NIN = '辽宁省',
  JI_LIN = '吉林省',
  HEI_LONG_JIANG = '黑龙江省',
  SHANG_HAI = '上海市',
  JIANG_SU = '江苏省',
  ZHE_JIANG = '浙江省',
  AN_HUI = '安徽省',
  FU_JIAN = '福建省',
  JIANG_XI = '江西省',
  SHAN_DONG = '山东省',
  HE_NAN = '河南省',
  HU_BEI = '湖北省',
  HU_NAN = '湖南省',
  GUANG_DONG = '广东省',
  GUANG_XI = '广西壮族自治区',
  HAI_NAN = '海南省',
  CHONG_QING = '重庆市',
  SI_CHUAN = '四川省',
  GUI_ZHOU = '贵州省',
  YUN_NAN = '云南省',
  XI_ZANG = '西藏自治区',
  SHAAN_XI = '陕西省',
  GAN_SU = '甘肃省',
  QING_HAI = '青海省',
  NING_XIA = '宁夏回族自治区',
  XIN_JIANG = '新疆维吾尔自治区',
  TAI_WAN = '台湾省',
  XIANG_GANG = '香港特别行政区',
  AO_MEN = '澳门特别行政区',
}
export const provinceOptions = (() => {
  return Object.keys(Province).map((v) => ({ label: Province[v], value: Province[v] }));
})();
