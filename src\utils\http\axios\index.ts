// axios配置  可自行根据项目进行更改，只需更改该文件即可，其他文件可以不动
// The axios configuration can be changed according to the project, just change the file, other files can be left unchanged

import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import type { RequestOptions, Result } from '/#/axios';
import type { AxiosTransform, CreateAxiosOptions } from './axiosTransform';
import { VAxios } from './Axios';
import { checkStatus } from './checkStatus';
import { useGlobSetting } from '/@/hooks/setting';
import { useMessage } from '/@/hooks/web/useMessage';
import { RequestEnum, ResultEnum, ContentTypeEnum, ConfigEnum } from '/@/enums/httpEnum';
import { isString } from '/@/utils/is';
import { getToken } from '/@/utils/auth';
import { setObjToUrlParams, deepMerge } from '/@/utils';
import signUtils from '/@/utils/encryption/signUtils';
import { useErrorLogStoreWithOut } from '/@/store/modules/errorLog';
import { useI18n } from '/@/hooks/web/useI18n';
import { joinTimestamp, formatRequestDate, tryParseJson } from './helper';
import { useUserStoreWithOut } from '/@/store/modules/user';
import encryption from '@/utils/encryption/hashUtils';
import HashUtils from '@/utils/encryption/hashUtils';
const globSetting = useGlobSetting();
const urlPrefix = globSetting.urlPrefix;
const { createMessage, createErrorModal } = useMessage();
/**
 * @description: 数据处理，方便区分多种处理方式
 */
const transform: AxiosTransform = {
  /**
   * @description: 处理请求数据。如果数据不是预期格式，可直接抛出错误
   */
  transformRequestHook: (res: AxiosResponse<Result>, options: RequestOptions) => {
    const { t } = useI18n();
    const { isTransformResponse, isReturnNativeResponse } = options;
    const { data } = res;
    //是否需要解密
    //console.log(options,'options')
    if (options.needDecrypto !== false && __NEED_CRYPTO__) {
      if (data && typeof data.result === 'string') {
        try {
          const json = HashUtils.rsaDecrypt(res.data.result);
          res.data.result = tryParseJson(json);
          console.log(res.data.result, 'res.data.result');
        } catch (error) {
          console.warn(error);
        }
      }
    }
    // 是否返回原生响应头 比如：需要获取响应头时使用该属性
    if (isReturnNativeResponse) {
      return res;
    }
    // 不进行任何处理，直接返回
    // 用于页面代码可能需要直接获取code，data，message这些信息时开启
    if (!isTransformResponse) {
      return res.data;
    }
    // 错误的时候返回

    if (!data) {
      // return '[HTTP] Request has no return value';
      throw new Error(t('sys.api.apiRequestFailed'));
    }
    //  这里 code，result，message为 后台统一的字段，需要在 types.ts内修改为项目自己的接口返回格式
    const { code, result, message, success } = data;
    // 这里逻辑可以根据项目进行修改
    const hasSuccess = data && Reflect.has(data, 'code') && (code === ResultEnum.SUCCESS || code === 200);
    if (hasSuccess) {
      if (success && message && options.successMessageMode === 'success') {
        //信息成功提示
        createMessage.success(message);
      }
      return result;
    }

    // 在此处根据自己项目的实际情况对不同的code执行不同的操作
    // 如果不希望中断当前请求，请return数据，否则直接抛出异常即可
    let timeoutMsg = '';
    const userStore = useUserStoreWithOut();
    console.log(code, 'cede', message);
    switch (code) {
      case ResultEnum.TOKENEXPIRED:
      case ResultEnum.TIMEOUT:
        timeoutMsg = message || t('sys.api.timeoutMessage');
        userStore.setToken(undefined);
        userStore.logout(false);
        break;
      default:
        if (message) {
          timeoutMsg = message;
          if (message == 'Token失效，请重新登录!') {
            userStore.setToken(undefined);
            userStore.logout(false);
          }
        }
    }
    // errorMessageMode=‘modal’的时候会显示modal错误弹窗，而不是消息提示，用于一些比较重要的错误
    // errorMessageMode='none' 一般是调用时明确表示不希望自动弹出错误提示
    if (options.errorMessageMode === 'modal') {
      createErrorModal({ title: t('sys.api.errorTip'), content: timeoutMsg });
    } else if (options.errorMessageMode === 'message') {
      createMessage.error(timeoutMsg);
    }
    throw new Error(timeoutMsg || t('sys.api.apiRequestFailed'));
  },

  // 请求之前处理config
  beforeRequestHook: (config, options) => {
    const { apiUrl, joinPrefix, joinParamsToUrl, formatDate, joinTime = true, urlPrefix } = options;

    if (joinPrefix) {
      config.url = `${urlPrefix}${config.url}`;
    }
    if (apiUrl && isString(apiUrl)) {
      config.url = `${apiUrl}${config.url}`;
    }
    const params = config.params || {};
    const data = config.data || false;
    formatDate && data && !isString(data) && formatRequestDate(data);
    if (config.method?.toUpperCase() === RequestEnum.GET) {
      if (!isString(params)) {
        // 给 get 请求加上时间戳参数，避免从缓存中拿数据。
        config.params = Object.assign(params || {}, joinTimestamp(joinTime, false));
      } else {
        // 兼容restful风格
        config.url = config.url + params + `${joinTimestamp(joinTime, true)}`;
        config.params = undefined;
      }
    } else {
      if (!isString(params)) {
        formatDate && formatRequestDate(params);
        if (Reflect.has(config, 'data') && config.data && Object.keys(config.data).length > 0) {
          config.data = data;
          config.params = params;
        } else {
          // 非GET请求如果没有提供data，则将params视为data
          config.data = params;
          config.params = undefined;
        }
        if (joinParamsToUrl) {
          config.url = setObjToUrlParams(config.url as string, Object.assign({}, config.params, config.data));
        }
      } else {
        // 兼容restful风格
        config.url = config.url + params;
        config.params = undefined;
      }
    }
    return config;
  },

  /**
   * @description: 请求拦截器处理
   */
  requestInterceptors: (config: AxiosRequestConfig<any>, options) => {
    // 请求之前处理config
    const token = getToken();
    const now = signUtils.getTimestamp();
    const reqId = signUtils.genUUID();
    config.headers = {};
    config.headers[ConfigEnum.TIMESTAMP] = now;
    config.headers[ConfigEnum.REQUEST_ID] = reqId;
    if (typeof config.data !== 'undefined') {
      // if (__NEED_CRYPTO__ && !(config as Recordable)?.requestOptions.decrypt) {
      //   console.log(config.data,'config')
      //  // console.log(encryption.rsaDecrypt(),'encryption.rsaDecrypt()')
      // let rsaEncryptData = HashUtils.rsaEncrypt(JSON.stringify(config.data))

      //  console.log(rsaEncryptData,'rsa')
      //   config.data = {
      //     data: rsaEncryptData,
      //   };
      //   console.log(config.data.data,'config.data')
      //  // let rsaDecryptData = HashUtils.rsaDecrypt(config.data.data)
      //  // console.log(rsaDecryptData,'config.data')
      // }
      console.log(JSON.stringify(config.data), 'config');
      const rsaUrlList = [
        '/zqqy-manager/sys/login',
        '/zqqy-manager/sys/user/updatePassword',
        '/zqqy-manager/sys/user/add',
        // '/zqqy-manager/sys/user/rsatest'
      ];
      if (rsaUrlList.includes(config.url)) {
        let rsaEncryptData = HashUtils.rsaEncrypt(JSON.stringify(config.data));
        // console.log(typeof(rsaEncryptData),'rsaEncryptData')
        config.data = {
          data: rsaEncryptData,
        };
        let rsaDecryptData = HashUtils.rsaDecrypt(config.data.data);
        console.log(rsaDecryptData, 'config.data');
      }
    }
    // let cinfigData = {
    //   "username":"djl","password":"Aa111111.","realname":"定价两2","phone":"17611111111","companyId":"999","selectedroles":"1891677676294115330","email":"<EMAIL>","dataAuthType":"1","systemType":"3"
    // }

    // console.log(JSON.stringify(cinfigData),'JSON.stringify(cinfigData)')
    // let EncryptData = HashUtils.rsaEncrypt(JSON.stringify(cinfigData))
    // console.log(EncryptData,'EncryptData')
    // let encryptedData = 'b3GI8t4VbvkF24Fx4mLBlCU8rxW3oA4swDDTe+CHzKAPOWq1+U1AxOJc/0qRHS3W5enTf8a9pvPbNh5eFlKvdVSRq3ZHW19ezweMsBB8LsXsED6w/eCVdnxupEpH5NzUiZjNhtOjW0/M/42ueB4XDQGz9GHWMWNMfpTj83Ixw9s=|EWr2F9HtE46xUstye87zN32Y4JyODax9BisGjR7JI0jDIyGpDQHCTewI0QmrDz2/Rm+jxsYXg1cTJlD6cnHfSCDJsaujsl87MLkvMVUnlv7QypEE/vfXmqJyleAuPCfAFv40q0no54zGouRnb/U4OhV8QF5ixUwZsED419tKqaM='

    if (token && (config as Recordable)?.requestOptions?.withToken !== false) {
      // jwt token
      config.headers.Authorization = options.authenticationScheme ? `${options.authenticationScheme} ${token}` : token;
      config.headers[ConfigEnum.TOKEN] = token;
    }
    config.headers[ConfigEnum.SIGN] = signUtils.getSign(config.url, config.data, now, reqId);
    config.headers[ConfigEnum.VERSION] = 'v3';
    config.headers[ConfigEnum.XA] = encryption.rsaPrivateKey;

    return config;
  },

  /**
   * @description: 响应拦截器处理
   */
  responseInterceptors: (res: AxiosResponse<any>) => {
    return res;
  },

  /**
   * @description: 响应错误处理
   */
  responseInterceptorsCatch: (error: any) => {
    const { t } = useI18n();
    const errorLogStore = useErrorLogStoreWithOut();
    errorLogStore.addAjaxErrorInfo(error);
    const { response, code, message, config } = error || {};
    const errorMessageMode = config?.requestOptions?.errorMessageMode || 'none';
    //scott 20211022 token失效提示信息
    //const msg: string = response?.data?.error?.message ?? '';
    const msg: string = response?.data?.message ?? '';
    const err: string = error?.toString?.() ?? '';
    let errMessage = '';
    try {
      if (code === 'ECONNABORTED' && message.indexOf('timeout') !== -1) {
        errMessage = t('sys.api.apiTimeoutMessage');
      }
      if (err?.includes('Network Error')) {
        errMessage = t('sys.api.networkExceptionMsg');
      }
      if (errMessage) {
        if (errorMessageMode === 'modal') {
          createErrorModal({ title: t('sys.api.errorTip'), content: errMessage });
        } else if (errorMessageMode === 'message') {
          createMessage.error(errMessage);
        }
        return Promise.reject(error);
      }
    } catch (error) {
      throw new Error(error as string);
    }

    checkStatus(error?.response?.status, msg, errorMessageMode);
    return Promise.reject(error);
  },
};

function createAxios(opt?: Partial<CreateAxiosOptions>) {
  return new VAxios(
    deepMerge(
      {
        // See https://developer.mozilla.org/en-US/docs/Web/HTTP/Authentication#authentication_schemes
        // authentication schemes，e.g: Bearer
        // authenticationScheme: 'Bearer',
        authenticationScheme: '',
        timeout: 120 * 1000,
        // 基础接口地址
        // baseURL: globSetting.apiUrl,
        headers: { 'Content-Type': ContentTypeEnum.JSON },
        // 如果是form-data格式
        // headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
        // 数据处理方式
        transform,
        // 配置项，下面的选项都可以在独立的接口请求中覆盖
        requestOptions: {
          // 默认将prefix 添加到url
          joinPrefix: true,
          // 是否返回原生响应头 比如：需要获取响应头时使用该属性
          isReturnNativeResponse: false,
          // 需要对返回数据进行处理
          isTransformResponse: true,
          // post请求的时候添加参数到url
          joinParamsToUrl: false,
          // 格式化提交参数时间
          formatDate: true,
          // 异常消息提示类型
          errorMessageMode: 'message',
          // 成功消息提示类型
          successMessageMode: 'none',
          // 接口地址
          apiUrl: globSetting.apiUrl,
          // 接口拼接地址
          urlPrefix: urlPrefix,
          //  是否加入时间戳
          joinTime: true,
          // 忽略重复请求
          ignoreCancelToken: true,
          // 是否携带token
          withToken: true,
        },
      },
      opt || {}
    )
  );
}
export const defHttp = createAxios();
