<template>
  <BasicModal v-bind="$attrs" :title="'详情'" width="800px" @register="registerModal" :showOkBtn="false">
    <h4>充值信息</h4>
    <a-row>
      <a-col :span="4" class="tr">集团客户名称：</a-col>
      <a-col :span="16" class="tl">324532142323</a-col>
    </a-row>
    <a-row class="mt8">
      <a-col :span="4" class="tr">充值金额：</a-col>
      <a-col :span="16" class="tl">324532142323</a-col>
    </a-row>
    <a-row class="mt8">
      <a-col :span="4" class="tr">充值类型：</a-col>
      <a-col :span="16" class="tl">324532142323</a-col>
    </a-row>
    <a-row class="mt8">
      <a-col :span="4" class="tr">提单说明：</a-col>
      <a-col :span="16" class="tl">324532142323</a-col>
    </a-row>
    <a-row class="mt8">
      <a-col :span="4" class="tr">资金入账截图：</a-col>
      <a-col :span="16" class="tl">324532142323</a-col>
    </a-row>
    <h4 class="mt8">审批流程</h4>
    <a-table :columns="tableColumns" :data-source="tableData"></a-table>
  </BasicModal>
</template>
<script lang="ts">
  // 单独添加一个script块用于导出
  export default {
    name: 'system-bannerModel', // 保持与你原来的name一致
  };
</script>
<script lang="ts" setup>
  import { ref, computed, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { postBannerSave, postBannerUpdata } from '/@/api/sys/banner';
  import { tableColumns } from './recharge.data';
  import { useMessage } from '/@/hooks/web/useMessage';
  const emit = defineEmits(['success', 'register']);
  let modelType = ref('add');
  let tableData = reactive([]);
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    console.log(closeModal, postBannerSave, data);
  });
</script>
<style scoped>
  .ml8 {
    margin-left: 8px;
  }
  .mt8 {
    margin-top: 8px;
  }
  .tr {
    text-align: right;
  }
  .tl {
    text-align: left;
  }
  .tips {
    color: #999;
  }
</style>
