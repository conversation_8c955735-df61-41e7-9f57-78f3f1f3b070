<template>
  <div class="platform-container">
    <div class="flex justify-center enter-x">
      <a-button class="btn" type="primary" size="large" shape="round" @click="loginCam4A">点击登录！</a-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
  const loginCam4A = () => {
    if (__BUILD_PLATFORM__ === '4a') {
      window.open(__LOGIN_4A__, '_self');
    } else {
      window.open(__LOGIN_CAM__, '_self');
    }
  };
</script>
<style scoped lang="less">
  .icon {
    font-size: 14px;
    text-align: center;
    background-color: #3f0fdc;
    color: #fff;
    margin: 0 20px;
    cursor: pointer;
    &.platform-4a {
      background-color: #160505;
    }
  }
  .btn {
    height: 66px !important;
    width: 70%;
    padding: 16px !important;
    font-size: 22px !important;
  }
</style>
