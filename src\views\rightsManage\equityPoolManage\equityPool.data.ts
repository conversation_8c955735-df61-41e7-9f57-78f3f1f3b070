import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '权益池名称',
    dataIndex: 'name',
  },
  {
    title: '关联权益数',
    dataIndex: 'equityNum',
  },
  {
    title: '关联卡券数',
    dataIndex: 'commodityNum',
  },
  {
    title: '创建日期',
    dataIndex: 'createTime',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '权益池名称',
    field: 'name',
    component: 'Input',
    colProps: { span: 6 },
  },
];

export const equityPoolSchema: FormSchema[] = [
  {
    label: '权益商品名称',
    field: 'searchName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '权益商品分类',
    field: 'category',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'product_class',
    },
    colProps: { span: 6 },
  },
];

export const cardColumnsLeft: BasicColumn[] = [
  {
    title: '卡券商品ID',
    dataIndex: 'commodityCode',
  },
  {
    title: '卡券商品名称',
    dataIndex: 'commodityName',
  },
  {
    title: '卡券分类',
    dataIndex: 'productClassName',
  },
];
export const cardColumnsRight: BasicColumn[] = [
  {
    title: '卡券商品ID',
    dataIndex: 'commodityCode',
  },
  {
    title: '卡券商品名称',
    dataIndex: 'commodityName',
  },
  {
    title: '卡券分类',
    dataIndex: 'productClassName',
  },
  {
    title: '状态',
    dataIndex: 'selectable',
    customRender: ({ text }) => (+text === 1 ? '未订购' : '已订购'),
  },
];
export const rightColumnsLeft: BasicColumn[] = [
  {
    title: '权益商品ID',
    dataIndex: 'commodityCode',
  },
  {
    title: '权益商品名称',
    dataIndex: 'commodityName',
  },
  {
    title: '权益分类',
    dataIndex: 'productClassName',
  },
  {
    title: '权益商品价格',
    dataIndex: 'price',
  },
];
export const rightColumnsRight: BasicColumn[] = [
  {
    title: '权益商品ID',
    dataIndex: 'commodityCode',
  },
  {
    title: '权益商品名称',
    dataIndex: 'commodityName',
  },
  {
    title: '权益分类',
    dataIndex: 'productClassName',
  },
  {
    title: '权益商品价格',
    dataIndex: 'price',
  },
  {
    title: '权益商品排序',
    dataIndex: 'sort',
    key: 'sort',
    width: 100,
  },
];
