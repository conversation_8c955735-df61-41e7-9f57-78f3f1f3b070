<template>
  <PageWrapper>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'order:exchange:excel'" @click="goRechargeModal">充值</a-button>
        <a-button v-auth="'order:exchange:excel'" @click="excelOrder">导出</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 验收弹窗 -->
    <rechargeModal @register="registerPropertyModal" @success="handleSuccess"></rechargeModal>
    <rechargeDetail @register="rechargeDetailModal" @success="handleSuccess"></rechargeDetail>
    <!-- <CheckModel @register="registerCheckModal" @success="handleSuccess" /> -->
    <!-- <exchangeDetail @register="registerPropertyModal" @success="handleSuccess" /> -->
  </PageWrapper>
</template>

<script lang="ts" name="customerRecharge-transfer" setup>
  import { reactive } from 'vue';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { getExchangeOrderList, getExChangeExcel } from '/@/api/order/exchange-order';
  import { columns, searchFormSchema } from './recharge.data';
  import { useModal } from '/@/components/Modal';
  import { Modal } from 'ant-design-vue';
  import { exportExcel } from '/@/utils/common/compUtils';
  import rechargeModal from './recharge.vue';
  import rechargeDetail from './rechargeDetail.vue';
  import { func } from 'vue-types';
  // import transferModel from './components/transferModel.vue';
  const [registerPropertyModal, { openModal }] = useModal();
  const [rechargeDetailModal, { openModal: openRechargeDetailModal }] = useModal();
  // 列表页面公共参数、方法
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'customerRecharge-list',
    tableProps: {
      api: getExchangeOrderList,
      rowSelection: {},
      columns: columns,
      size: 'small',
      showIndexColumn: false,
      // showActionColumn: false,
      formConfig: {
        showAdvancedButton: false,
        labelWidth: 100,
        schemas: searchFormSchema,
        actionColOptions: {
          xs: 24, // <576px
          sm: 24, // ≥576px
          md: 24, // ≥768px
          lg: 24, // ≥992px
          xl: 24, // ≥1200px
          xxl: 24,
          style: { textAlign: 'right' },
        },
      }, // 是否使用搜索项
      // actionColumn: {
      //   width: 120,
      // },
      beforeFetch: (params) => {
        delete params.column;
        delete params.order;
        let createTimes = params.dateOp && params.dateOp.includes(',') ? params.dateOp.split(',') : '';
        if (createTimes.length > 0) {
          let startDate = createTimes[0];
          let endDate = createTimes[1];
          params.beginPayTime = startDate;
          params.endPayTime = endDate;
        }
        delete params.dateOp;
        console.log(params, 'params');
      },
    },
  });

  //注册table数据
  const [registerTable, { reload, getForm }] = tableContext;
  let recordData = reactive({});
  function excelOrder() {
    // 导出excel
    Modal.confirm({
      title: '是否导出C端用户权益兑换订单？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        let { getFieldsValue } = getForm();
        let params = Object.assign({}, getFieldsValue());
        let createTimes = params.dateOp && params.dateOp.includes(',') ? params.dateOp.split(',') : '';
        if (createTimes.length > 0) {
          let startDate = createTimes[0];
          let endDate = createTimes[1];
          params.beginPayTime = startDate;
          params.endPayTime = endDate;
        }
        delete params.dateOp;
        console.log(params, 'params');
        const res = await getExChangeExcel(params);
        exportExcel(res);
      },
    });
  }

  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '详情',
        onClick: lookDetail.bind(null, record),
        auth: 'system:banner:edit',
      },
    ];
  }

  function lookDetail(record: Recordable) {
    console.log(record, 'record');
    Object.assign(recordData, record);
    openRechargeDetailModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  function goRechargeModal(record = {}) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  function handleSuccess() {
    reload();
  }
</script>
