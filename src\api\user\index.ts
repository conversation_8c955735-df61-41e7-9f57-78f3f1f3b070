import { defHttp } from '/@/utils/http/axios';
import URL from '/@/api/url';

export const getUserListApi = (params = {}) => defHttp.post({ url: URL.user.listNoCareTenant, params });
export const getDepartList = (params = {}) => defHttp.post({ url: URL.user.departs, params });
export const addUser = (params = {}) => defHttp.post({ url: URL.user.save, params });
export const editUser = (params = {}) => defHttp.post({ url: URL.user.edit, params });
export const deleteUser = (params = {}) => defHttp.post({ url: URL.user.deleteUser, params });
export const resetPassword = (params = {}) => defHttp.post({ url: URL.user.resetPassword, params });
export const queryUserInfo = (params = {}) => defHttp.post({ url: URL.user.view, params });
export const frozeUser = (params = {}) => defHttp.post({ url: URL.user.frozenBatch, params });
