import { defHttp } from '/@/utils/http/axios';
import { LoginParams, LoginResultModel, GetUserInfoModel } from './model/userModel';
import { Modal } from 'ant-design-vue';
import { ErrorMessageMode } from '/#/axios';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { setAuthCache } from '/@/utils/auth';
import { TOKEN_KEY } from '/@/enums/cacheEnum';
import { router } from '/@/router';
import { PageEnum } from '/@/enums/pageEnum';
import URL from '/@/api/url';
/**
 *
 * @param code string 字典key值
 * @returns Promise<T>
 */
export function getDictItemsByCode(code: string) {
  return defHttp.post({
    url: `${URL.user.getDictItemsByCode}/${code}`,
  });
}
/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>(
    {
      url: URL.user.login,
      params,
    },
    {
      errorMessageMode: mode,
    }
  );
}
/**
 * @description: getUserInfo
 */
export function getUserInfo() {
  return defHttp.post<GetUserInfoModel>({ url: URL.user.getUserInfo }, { errorMessageMode: 'none' }).catch((e) => {
    // update-begin--author:zyf---date:20220425---for:【VUEN-76】捕获接口超时异常,跳转到登录界面
    if (e && (e.message.includes('timeout') || e.message.includes('401'))) {
      //接口不通时跳转到登录界面
      const userStore = useUserStoreWithOut();
      userStore.setToken('');
      setAuthCache(TOKEN_KEY, null);
      router.push({
        path: PageEnum.BASE_LOGIN,
        query: {
          redirect: router.currentRoute.value.fullPath,
        },
      });
    }
  });
}

export function doLogout() {
  return defHttp.post({ url: URL.user.logout });
}

export function getCodeInfo(currdatetime) {
  const url = URL.user.getInputCode + `/${currdatetime}`;
  return defHttp.get({ url: url }, { needDecrypto: false });
}

/**
 * @description: 注册接口
 */
export function register(params) {
  return defHttp.post({ url: URL.user.registerApi, params }, { isReturnNativeResponse: true });
}

/**
 *校验用户是否存在
 * @param params
 */
export const checkOnlyUser = (params) => defHttp.post({ url: URL.user.checkOnlyUser, params }, { isTransformResponse: false });
/**
 *校验手机号码
 * @param params
 */
export const phoneVerify = (params) => defHttp.post({ url: URL.user.phoneVerify, params }, { isTransformResponse: false });
/**
 *密码修改
 * @param params
 */
export const passwordChange = (params) => defHttp.post({ url: URL.user.passwordChange, params }, { isTransformResponse: false });
export const getPermissionByToken = () => {
  return defHttp.post({ url: URL.user.getUserPermByToken });
};

// 系统设置用户相关api
/**
 * 导出api
 * @param params
 */
export const getExportUrl = URL.user.exportXls;
/**
 * 导入api
 */
export const getImportUrl = URL.user.importExcel;
/**
 * 列表接口(查询用户，通过租户隔离)
 * @param params
 */
export const list = (params) => defHttp.post({ url: URL.user.list, params });

/**
 * 列表接口(查询全部用户，不通过租户隔离)
 * @param params
 */
export const listNoCareTenant = (params) => defHttp.post({ url: URL.user.listNoCareTenant, params });

/**
 * 用户角色接口
 * @param params
 */
export const getUserRoles = (params) => defHttp.post({ url: URL.user.getUserRole, params }, { errorMessageMode: 'none' });

/**
 * 删除用户
 */
export const deleteUser = (params, handleSuccess) => {
  return defHttp.post({ url: URL.user.deleteUser, params }).then(() => {
    handleSuccess();
  });
};
/**
 * 批量删除用户
 * @param params
 */
export const batchDeleteUser = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.post({ url: URL.user.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};
/**
 * 保存或者更新用户
 * @param params
 */
export const saveOrUpdateUser = (params, isUpdate) => {
  const url = isUpdate ? URL.user.edit : URL.user.save;
  return defHttp.post({ url: url, params });
};
/**
 * 唯一校验
 * @param params
 */
export const duplicateCheck = (params) => defHttp.post({ url: URL.user.duplicateCheck, params }, { isTransformResponse: false });
/**
 * 获取全部角色（租户隔离）
 * @param params
 */
export const getAllRolesList = (params) => defHttp.post({ url: URL.user.allRolesList, params });
/**
 * 获取全部角色（不租户隔离）
 * @param params
 */
export const getAllRolesListNoByTenant = (params) => defHttp.post({ url: URL.user.allRolesListNoByTenant, params });
/**
 * 获取全部租户
 */
export const getAllTenantList = (params) => defHttp.post({ url: URL.user.allTenantList, params });
/**
 * 获取指定用户负责部门
 */
export const getUserDepartList = (params) => defHttp.post({ url: URL.user.userDepartList, params }, { successMessageMode: 'none' });
/**
 * 获取全部职务
 */
export const getAllPostList = (params) => {
  return new Promise((resolve) => {
    defHttp.post({ url: URL.user.allPostList, params }).then((res) => {
      resolve(res.records);
    });
  });
};
/**
 * 回收站列表
 * @param params
 */
export const getRecycleBinList = (params) => defHttp.post({ url: URL.user.recycleBinList, params });
/**
 * 回收站还原
 * @param params
 */
export const putRecycleBin = (params, handleSuccess) => {
  return defHttp.post({ url: URL.user.putRecycleBin, params }).then(() => {
    handleSuccess();
  });
};
/**
 * 回收站删除
 * @param params
 */
export const deleteRecycleBin = (params, handleSuccess) => {
  return defHttp.post({ url: URL.user.deleteRecycleBin, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 修改密码
 * @param params
 */
export const changePassword = (params) => {
  return defHttp.post({ url: URL.user.changePassword, params });
};
/**
 * 冻结解冻
 * @param params
 */
export const frozenBatch = (params, handleSuccess) => {
  return defHttp.post({ url: URL.user.frozenBatch, params }).then(() => {
    handleSuccess();
  });
};
/**
 * @description: 第三方4A登录
 */
export function thirdFourLogin(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>(
    {
      url: `${URL.user.thirdFourLogin}`,
      params,
    },
    {
      errorMessageMode: mode,
    }
  );
}
/**
 * @description: 第三方CAM登录
 */
export function thirdCamLogin(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>(
    {
      url: `${URL.user.thirdCamLogin}`,
      params,
    },
    {
      errorMessageMode: mode,
    }
  );
}

// 获取RSA公钥
export const getRsaPublicKey = () => defHttp.post({ url: URL.user.getRsaPublicKey }, { needDecrypto: false });
