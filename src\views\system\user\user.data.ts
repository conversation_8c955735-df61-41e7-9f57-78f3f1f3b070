import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getAllRolesListNoByTenant } from '/@/api/sys/user';
import { rules } from '/@/utils/helper/validator';
export const columns: BasicColumn[] = [
  {
    title: '用户名',
    dataIndex: 'username',
    width: 120,
  },
  {
    title: '真实姓名',
    dataIndex: 'realname',
    width: 100,
  },
  {
    title: '所属公司',
    width: 150,
    dataIndex: 'companyName',
  },
  {
    title: '角色',
    dataIndex: 'roleName',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => {
      return text === 1 ? ' 正常' : '冻结';
    },
  },
];
// 查询
export const searchFormSchema: FormSchema[] = [
  {
    label: '用户名',
    field: 'username',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 20,
    },
    rules: [
      {
        pattern: /^[^\u4e00-\u9fa5]+$/,
        message: '用户名不支持汉字',
        trigger: 'blur',
      },
    ],
  },
  {
    label: '真实姓名',
    field: 'realname',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 50,
    },
  },
  // {
  //   label: '手机号码',
  //   field: 'phone',
  //   component: 'Input',
  //   colProps: { span: 6 },
  //   dynamicRules: () => {
  //     return [{ pattern: /^1[3456789]\d{9}$/, message: '手机号码格式有误', trigger: 'blur' }];
  //   },
  //   componentProps: {
  //     maxLength: 11,
  //   },
  // },
  {
    label: '状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'user_status',
      placeholder: '请选择状态',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
];

// 新增  编辑
export const formSchema: FormSchema[] = [
  {
    label: '用户名',
    field: 'username',
    required: true,
    component: 'Input',
    componentProps: {
      maxLength: 20,
    },
    rules: [
      {
        required: true,
        message: '请输入用户名',
        trigger: 'blur',
      },
      {
        pattern: /^[^\u4e00-\u9fa5]+$/,
        message: '用户名不支持汉字',
        trigger: 'blur',
      },
    ],
  },
  {
    label: '密码',
    field: 'password',
    component: 'StrengthMeter',
    componentProps: {
      maxLength: 30,
    },
    rules: [
      {
        required: true,
        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{10,30}$/,
        message: '密码由10到30位数字、大小写字母和特殊符号组成!',
        trigger: 'blur',
      },
    ],
  },
  {
    label: '真实姓名',
    field: 'realname',
    required: true,
    component: 'Input',
    componentProps: {
      maxLength: 50,
    },
  },
  // {
  //   label: '手机号',
  //   field: 'phone',
  //   component: 'Input',
  //   componentProps: {
  //     maxLength: 11,
  //   },
  //   dynamicRules: () => {
  //     return [
  //       { message: '请输入手机号', trigger: 'blur', required: true },
  //       { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式有误', trigger: 'blur' },
  //     ];
  //   },
  // },
  {
    label: '所属公司',
    field: 'companyId',
    required: true,
    component: 'TreeSelect',
    componentProps: {
      treeData: [],
      placeholder: '无',
      dropdownStyle: { maxHeight: '200px', overflow: 'auto' },
    },
  },
  {
    label: '角色',
    field: 'selectedroles',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      mode: 'multiple',
      api: getAllRolesListNoByTenant,
      labelField: 'roleName',
      valueField: 'id',
      immediate: false,
    },
  },
  // {
  //   label: '所属分类',
  //   field: 'systemType',
  //   component: 'Select',
  //   required: true,
  //   componentProps: {
  //     options: [
  //       {
  //         label: 'CAM',
  //         value: 'cam',
  //       },
  //       {
  //         label: '4A',
  //         value: '4a',
  //       },
  //     ],
  //   },
  // },
  {
    label: '邮箱',
    field: 'email',
    component: 'Input',
    componentProps: {
      maxLength: 30,
    },
    dynamicRules: () => {
      return [
        {
          required: true,
          message: '请输入邮箱',
          trigger: 'blur',
        },
        { ...rules.rule('email', false)[0], trigger: 'blur' },
      ];
    },
  },
  {
    label: '数据权限',
    field: 'dataAuthType',
    component: 'Select',
    required: true,
    componentProps: ({ formActionType }) => {
      return {
        options: [
          {
            label: '全部',
            value: '1',
          },
          // {
          //   label: '本组织及以下',
          //   value: '2',
          // },
          {
            label: '本公司',
            value: '3',
          },
          {
            label: '自定义（按照公司）',
            value: '4',
          },
          {
            label: '本账号',
            value: '5',
          },
        ],
        onSelect: (value) => {
          const b = value === '4' ? true : false;
          formActionType.updateSchema({
            field: 'orgCodes',
            required: b,
            show: b,
          });
        },
      };
    },
  },
  {
    label: '自定义',
    field: 'orgCodes',
    component: 'TreeSelect',
    required: true,
    show: false,
    componentProps: {
      multiple: true,
      treeData: [],
      treeCheckStrictly: false,
      fieldNames: {
        label: 'title',
        key: 'orgCode',
        value: 'orgCode',
      },
      treeCheckable: true,
      showCheckedStrategy: 'TreeSelect.SHOW_PARENT',
    },
  },
  // {
  //   label: '固定电话',
  //   field: 'telephone',
  //   component: 'Input',
  //   componentProps: {
  //     maxLength: 11,
  //   },
  //   dynamicRules: () => {
  //     return [{ ...rules.telphone(false)[0], trigger: 'blur' }];
  //   },
  // },
];

export const formPasswordSchema: FormSchema[] = [
  {
    label: '用户账号',
    field: 'username',
    component: 'Input',
    componentProps: { readOnly: true },
  },
  {
    label: '登录密码',
    field: 'password',
    component: 'StrengthMeter',
    componentProps: {
      placeholder: '请输入登录密码',
    },
    rules: [
      {
        required: true,
        message: '请输入登录密码',
      },
    ],
  },
  {
    label: '确认密码',
    field: 'confirmPassword',
    component: 'InputPassword',
    dynamicRules: ({ values }) => rules.confirmPassword(values, true),
  },
];
