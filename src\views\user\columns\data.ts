import dayjs from 'dayjs';

export const columns = [
  {
    title: '登录账号',
    dataIndex: 'username',
  },
  {
    title: '用户姓名',
    dataIndex: 'realname',
  },
  {
    title: '所属租户',
    dataIndex: 'companyName',
  },
  {
    title: '用户角色',
    dataIndex: 'roleName',
  },
  {
    title: '当前状态',
    dataIndex: 'status',
    slots: { customRender: 'status' },
  },
  {
    title: '注册时间',
    dataIndex: 'createTime',
    customRender: ({ text }) => {
      return text ? dayjs(new Date(text)).format('YYYY-MM-DD HH:mm:ss') : '';
    },
  },
];

// 搜索表单配置
export const searchFormSchema = [
  {
    field: 'status',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: '1' },
        { label: '禁用', value: '2' },
      ],
      placeholder: '请选择状态',
      style: { width: '200px' },
    },
  },
  {
    field: 'realname',
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户姓名',
      style: { width: '300px' },
    },
  },
];
