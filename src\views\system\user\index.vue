<template>
  <PageWrapper>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'system:user:add'" preIcon="ant-design:plus-outlined" @click="handleCreate"> 新增</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>
    <!--用户抽屉-->
    <UserDrawer @register="registerDrawer" @success="handleSuccess" />
    <!--修改密码-->
    <PasswordModal @register="registerPasswordModal" @success="reload" />
  </PageWrapper>
</template>
<script lang="ts" name="system-user" setup>
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import UserDrawer from './UserDrawer.vue';
  import PasswordModal from './PasswordModal.vue';
  import { useDrawer } from '/@/components/Drawer';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { columns, searchFormSchema } from './user.data';
  import { listNoCareTenant, deleteUser, frozenBatch } from '/@/api/sys/user';

  const { createMessage } = useMessage();
  //注册drawer
  const [registerDrawer, { openDrawer }] = useDrawer();
  //密码model
  const [registerPasswordModal] = useModal();
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'user-list',
    tableProps: {
      title: '用户列表',
      api: listNoCareTenant,
      rowSelection: {},
      columns: columns,
      size: 'small',
      showIndexColumn: true,
      formConfig: {
        showAdvancedButton: false,
        labelWidth: 100,
        schemas: searchFormSchema,
        actionColOptions: {
          xs: 24, // <576px
          sm: 24, // ≥576px
          md: 24, // ≥768px
          lg: 24, // ≥992px
          xl: 24, // ≥1200px
          xxl: 24,
          style: { textAlign: 'right' },
        },
      },
      actionColumn: {
        width: 120,
      },
      beforeFetch: (params) => {
        return Object.assign({ column: 'createTime', order: 'desc' }, params);
      },
    },
  });

  //注册table数据
  const [registerTable, { reload }] = tableContext;
  // 新增事件
  function handleCreate() {
    openDrawer(true, {
      isUpdate: false,
      showFooter: true,
    });
  }
  //编辑事件
  async function handleEdit(record: Recordable) {
    console.log(record,'recordrecord')
    openDrawer(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  // 详情
  async function handleDetail(record: Recordable) {
    console.log(record,'record')
    openDrawer(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  // 删除事件
  async function handleDelete(record) {
    if ('admin' === record.username) {
      createMessage.warning('管理员账号不允许此操作！');
      return;
    }
    await deleteUser({ id: record.id }, reload);
  }
  // 成功回调
  function handleSuccess() {
    reload();
  }
  // 冻结解冻
  async function handleFrozen(record, status) {
    if ('admin' === record.username) {
      createMessage.warning('管理员账号不允许此操作！');
      return;
    }
    await frozenBatch({ ids: record.id, status: status }, reload);
  }
  // 操作栏
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth:'system:user:edit'
      },
    ];
  }
  // 下拉操作栏
  function getDropDownAction(record): ActionItem[] {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
        auth:'system:user:edit'
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
        auth:'system:user:delete'
      },
      {
        label: '冻结',
        ifShow: record.status === 1,
        popConfirm: {
          title: '确定冻结吗?',
          confirm: handleFrozen.bind(null, record, 2),
        },
        auth:'system:user:freeze'
      },
      {
        label: '解冻',
        ifShow: record.status === 2,
        popConfirm: {
          title: '确定解冻吗?',
          confirm: handleFrozen.bind(null, record, 1),
        },
        auth:'system:user:freeze'
      },
    ];
  }
</script>
