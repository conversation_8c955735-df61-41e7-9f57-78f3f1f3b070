import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';

enum Api {
  dictCheckList = '/biz/dictAcceptance/list',
  dictCheckAdd = '/biz/dictAcceptance/add',
  dictTaskDone = '/biz/dictAcceptance/taskDone',
  getTaskPrice = '/biz/dictAcceptance/getTaskPrice',
  getCheckDatail = '/biz/dictAcceptance/acceptance'
}

/**
 * 项目验收列表
 * @param params
 */
export const getDictCheckList = (params) => {
  return defHttp.post({ url: Api.dictCheckList, params });
};
/**
 * 项目验收
 * @param params
 */
export const dictCheckAdd = (params = {}) => defHttp.post({ url: Api.dictCheckAdd, params });
/**
 * 项目终止
 * @param params
 */

export const dictTaskDone = (params = {}) => defHttp.get({ url: Api.dictTaskDone, params });
/**
 * 项目价格
 * @param params
 */
export const getTaskPrice = (params = {}) => defHttp.get({ url: Api.getTaskPrice, params });
/**
 * 项目详情
 * @param params
 */
export const getCheckDatail = (params = {}) => defHttp.get({ url: Api.getCheckDatail, params });