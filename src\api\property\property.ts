
import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';
import axios from "axios";
import { getToken } from '/@/utils/auth';
enum Api {
  queryChannelList = '/biz/channelAccount/channel/queryChannelAccount',
  channelUpload = '/biz/channelAccountAdjustment/upload',
  channelExport = '/biz/channelAccount/channel/getChannelAccountExcel',
  adjustSave = '/biz/channelAccountAdjustment/save',
  adjustList = '/biz/channelAccountAdjustment/list',
  fileDownload = '/sys/file/download',
  rsatest = '/sys/user/rsatest'
}

/**
 * 资产列表
 * @param params
 */
export const getQueryChannelList = (params) => {
  return defHttp.post({ url: Api.queryChannelList, params });
};

export const getrsatest = (params) => {
  return defHttp.post({ url: Api.rsatest, params });
};
/**
 * 导出
 * @param params
 */
//export const getChannelExport = (params = {}) => defHttp.post({ url: Api.channelExport, params });
//export const getChannelExport = Api.channelExport;
export const getChannelExport = (params) =>{
  return defHttp.post(
    {
      url: Api.channelExport,
      params,
      responseType: 'blob',  // 设置响应类型为blob
    },
    {
      isReturnNativeResponse: true,  // 返回原始响应以获取headers
    }
  );
}
/**
 * 调账
 * @param params
 */

export const  adjustSave= (params = {}) => defHttp.post({ url: Api.adjustSave, params });
/**
 * 调账记录
 * @param params
 */
export const getAdjustList = (params = {}) => defHttp.post({ url: Api.adjustList, params });
/**
 * 自定义上传
 * @param customUpload
 */
// export const customUpload = (params) => {
//   defHttp.uploadFile({ url: Api.channelUpload }, params);
// };
// 文件上传 api
export const customUpload = ({ file }) => {
  const formData = new FormData();
  formData.append('file', file);
  const token = getToken();
  return axios({
    url: '/zqqy-manager'+ Api.channelUpload,
    method: 'POST',
    data: formData,
    headers: {
      'Content-type': 'multipart/form-data',
      Authorization:token,
      'X-Access-Token':token
    },
  });
};
/**
 * 下载
 * @param params
 */

export const  getfileDownload= (params = {}) => defHttp.post({ url: Api.fileDownload, params});