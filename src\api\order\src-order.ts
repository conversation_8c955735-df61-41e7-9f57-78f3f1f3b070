import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';

enum Api {
  srcOrderList = '/biz/srcOrder/list',
  srcOrderExcel = '/biz/srcOrder/excel',
}

// export const getDictCheckList = (params) => {
//   return defHttp.post({ url: Api.dictCheckList, params });
// };

export const getSrcOrderList = (params) => {
  return defHttp.get({ url: Api.srcOrderList, params });
};

export const srcOrderExcel = (params = {}) =>
  defHttp.get(
    {
      url: Api.srcOrderExcel,
      params,
      responseType: 'blob', // 设置响应类型为blob
    },
    {
      isReturnNativeResponse: true, // 返回原始响应以获取headers
    }
  );
