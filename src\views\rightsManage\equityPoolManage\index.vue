<template>
  <PageWrapper>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" v-auth="'rightsManage:equityPool:add'" @click="handleDetail({}, 0)"> 新增</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 验收弹窗 -->
    <!-- <CheckModel @register="registerCheckModal" @success="handleSuccess" /> -->
    <accountModal @register="registerPropertyModal" @success="handleSuccess" />
  </PageWrapper>
</template>

<script lang="ts" name="memberManage-jdUser" setup>
  import { reactive } from 'vue';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { equityPoolList } from '/@/api/productconfig/equity-pool';
  import { columns, searchFormSchema } from './equityPool.data';
  import { useModal } from '/@/components/Modal';
  import accountModal from './accountModal.vue';
  const [registerPropertyModal, { openModal }] = useModal();
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'jsUser-list',
    tableProps: {
      api: equityPoolList,
      rowSelection: {},
      columns: columns,
      size: 'small',
      showIndexColumn: false,
      // showActionColumn: false,
      formConfig: {
        showAdvancedButton: false,
        labelWidth: 100,
        schemas: searchFormSchema,
        actionColOptions: {
          xs: 24, // <576px
          sm: 24, // ≥576px
          md: 24, // ≥768px
          lg: 24, // ≥992px
          xl: 24, // ≥1200px
          xxl: 24,
          style: { textAlign: 'right' },
        },
      }, // 是否使用搜索项
      actionColumn: {
        width: 180,
      },
      beforeFetch: (params) => {
        delete params.column;
        delete params.order;
        // return Object.assign({ }, params);
      },
    },
  });

  //注册table数据
  const [registerTable, { reload }] = tableContext;
  let recordData = reactive({});

  function handleDetail(record, status) {
    Object.assign(recordData, record);
    openModal(true, {
      record,
      status, //0-1-2 新增-编辑权益-编辑卡券
      showFooter: false,
    });
  }
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '编辑权益',
        onClick: handleDetail.bind(null, record, 1),
        auth: 'rightsManage:equityPool:edit',
      },
      {
        label: '编辑卡券',
        onClick: handleDetail.bind(null, record, 2),
        auth: 'rightsManage:equityPool:edit',
      },
    ];
  }
  function handleSuccess() {
    reload();
  }
</script>
