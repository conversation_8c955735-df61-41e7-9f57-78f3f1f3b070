import { defHttp } from '@/utils/http/axios';
import URL from '/@/api/url';

export const getReportListApi = (params = {}) => defHttp.post({ url: URL.list.reportList, params });
export const deleteReport = (params = {}) => defHttp.post({ url: URL.list.delete, params });
export const getScoreByPhone = (params = {}) => defHttp.post({ url: URL.list.getScore, params });
export const uploadReport = (params = {}, type) => {
  return defHttp.post({ url: `${URL.list.upload}?type=${type}`, params }, { decrypt: true });
};
