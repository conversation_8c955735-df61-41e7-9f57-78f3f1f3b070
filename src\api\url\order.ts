enum Api {
  orderList = '/biz/order/list', //订单列表查询
  orderLogs = '/biz/orderLog/queryById', //订单操作日志
  orderReport = '/biz/report/generateReport', //获取订单报告
  orderReject = '/biz/order/rejectOrder', //驳回订单
  orderTaking = '/biz/order/acceptOrder', //接单
  orderSubmit = '/biz/order/submit', //提交或重提订单
  orderDiscard = '/biz/order/deleteOrder', // 废弃订单
  orderAddOrUpdate = '/biz/order/addOrUpdate', //保存或更新订单
  updateAfterOpen = '/biz/order/updateAfterOpen',
  updateBeforeOpen = '/biz/order/updateBeforeOpen',
  orderDetail = '/biz/order/queryById', //获取订单详情
  getSettleMain = '/biz/order/settleMain',
  getUserOrgInfo = '/biz/order/getUserOrgInfo',
  unsubscribe = '/biz/order/unsubscribe',
  exports = '/biz/order/exportList',
}
export default Api;
