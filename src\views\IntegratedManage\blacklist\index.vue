<template>
  <PageWrapper>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'integrated:black:read'" @click="handleRead()"> 黑名单读取</a-button>
        <a-button type="primary" v-auth="'integrated:black:batchRemove'" @click="removeUser({})"> 批量移除</a-button>
        <a-button type="primary" v-auth="'integrated:black:importTemp'" @click="downTemplate()"> 批量导入模板下载</a-button>
        <a-button type="primary" v-auth="'integrated:black:batchImport'" @click="importBatch()"> 批量导入</a-button>
        <a-button type="primary" v-auth="'integrated:black:add'" @click="handleCreate()"> 新增</a-button>
      </template>
      <template #status="{ text }">
        <a-tag :color="+text === 0 ? 'error' : 'success'">{{ +text === 0 ? '已停用' : '启用中' }}</a-tag>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 验收弹窗 -->
    <!-- <CheckModel @register="registerCheckModal" @success="handleSuccess" /> -->
    <batchImport @register="registerPropertyModal" @success="handleSuccess" />
  </PageWrapper>
</template>

<script lang="ts" name="system-banner" setup>
  import { reactive } from 'vue';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { pageList, blackDelete, batchSftp, getBlacklistTemplate } from '/@/api/Integrated/black';
  import { columns, searchFormSchema } from './blackList.data';
  import { Modal } from 'ant-design-vue';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import batchImport from './batchImport.vue';
  import { exportExcel } from '/@/utils/common/compUtils';
  import { func } from 'vue-types';
  import { get } from 'sortablejs';
  const [registerPropertyModal, { openModal }] = useModal();
  const { createMessage, createConfirm } = useMessage();
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'banner-list',
    tableProps: {
      api: pageList,
      columns: columns,
      size: 'small',
      showIndexColumn: false,
      rowSelection: { type: 'checkbox' }, //默认是 checkbox 多选，可以设置成 radio 单选
      formConfig: {
        showAdvancedButton: false,
        labelWidth: 100,
        schemas: searchFormSchema,
        actionColOptions: {
          xs: 24, // <576px
          sm: 24, // ≥576px
          md: 24, // ≥768px
          lg: 24, // ≥992px
          xl: 24, // ≥1200px
          xxl: 24,
          style: { textAlign: 'right' },
        },
      },
      actionColumn: {
        width: 120,
      },
      beforeFetch: (params) => {
        delete params.column;
        delete params.order;
      },
    },
  });

  //注册table数据
  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  //新增
  function handleCreate() {
    openModal(true, {
      isAdd: 1,
      isUpdate: true,
      showFooter: true,
    });
  }
  // 黑名单读取
  async function handleRead() {
    Modal.confirm({
      title: '是否读取远端的发黑名单？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        await batchSftp();
      },
    });
  }
  // 批量移除
  function removeUser(record) {
    Modal.confirm({
      title: +record.id ? '确定要将这个用户移除发放黑名单吗?' : '确定要将已经选中的用户移除发放黑名单吗?',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        const params = record.id ? [record.id] : selectedRowKeys.value;
        blackDelete(params).then((res) => {
          selectedRowKeys.value = [];
          createMessage.success('操作成功');
          reload();
        });
      },
    });
  }
  // 批量导入模板下载
  function downTemplate() {
    Modal.confirm({
      title: '是否下载批量导入模板？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const res = await getBlacklistTemplate();
          exportExcel(res);
        } catch (error) {
          createMessage.error('下载模板失败！');
        }
      },
    });
  }
  // 批量导入
  function importBatch() {
    openModal(true, {
      isAdd: 0,
      isUpdate: true,
      showFooter: true,
    });
  }

  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '移除',
        onClick: removeUser.bind(null, record),
        auth: 'integrated:black:remove',
      },
    ];
  }
  function handleSuccess() {
    reload();
  }
</script>
