export enum DictsKey {
  /********************  system start ************************/
  UserStatus = 'user_status',
  DictItemStatus = 'dict_item_status',
  /********************  system end ************************/
  BillStatus = 'bill_status',
  ProjectOwner = 'project_owner',
  OrderSource = 'order_source',
  OrderStatus = 'order_status',
  Sex = 'sex',
}
export enum DictsName {
  /********************  system start ************************/
  UserStatus = '用户状态',
  /********************  system end ************************/
  ProjectOwner = '项目归属',
  Sex = '性别',
  BillStatus = '账单状态',
  OrderSource = '订单来源',
  DictItemStatus = '是否启用',
}
