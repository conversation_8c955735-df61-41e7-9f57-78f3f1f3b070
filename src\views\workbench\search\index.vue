<template>
  <div class="flex flex-col p-5">
    <!-- 标题 -->
    <div class="pb-3">
      <h3 class="float-left">语音投诉风险融合评分服务</h3>
      <a-button class="float-right" type="primary" @click="goBack">返回</a-button>
    </div>
    <!-- 介绍卡片 -->
    <div class="px-20 py-5 shadow-md bg-[#FFF] rounded-10px flex justify-center items-center">
      <div class="flex flex-2 flex-basis-300 justify-end">
        <img :src="cardImg" alt="" class="w-300px" />
      </div>
      <div class="flex-3">
        <h3 class="font-bold">语音投诉风险融合评分服务</h3>
        <p><span class="font-bold">产品能力：</span>实时评估人群投诉风险，满足语音投诉业务中监测及预警的目的。</p>
        <p><span class="font-bold">产品亮点：</span>实时优质接口；权威数据来源；方便快捷调用；隐私保护；高效稳定。</p>
        <p
          ><span class="font-bold">产品简介：</span
          >该产品由卓望敏感投诉人群识别模型与东信北邮高投诉风险用户预测发现模型融合生成，更新频率为15分钟。将高投诉风险用户预测发现模型分作为入模变量，训练综合模型，提升模型效果，最终形成融合APP产品对外提供服务。</p
        >
      </div>
    </div>
    <!-- 查询 -->
    <div class="w-1/2 m-16 mx-auto">
      <a-input-search
        v-model:value="phone"
        placeholder="请输入手机号码查询"
        enter-button="确认查询"
        size="large"
        :maxlength="11"
        @search="onSearch"
      />
    </div>
    <!-- 示例展示 -->
    <a-table class="w-1/2 mx-auto" :dataSource="dataSource" bordered :pagination="false" size="small">
      <a-table-column key="op" title="操作示例" :colSpan="3" width="100px" align="center" data-index="op" />
      <a-table-column key="phone" :colSpan="0" width="100px" align="center" data-index="phone" />
      <a-table-column key="result" :colSpan="0" width="200px" align="center" data-index="result">
        <template #default="{ record }">
          <div v-for="(item, index) in record.result" :key="index">{{ item }}</div>
        </template>
      </a-table-column>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
  import { ref, h } from 'vue';
  import { Modal } from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import cardImg from '@/assets/images/card.png';
  import { getScoreByPhone } from '/@/api/list';
  const router = useRouter();
  let phone = ref('');
  // 查询
  function onSearch() {
    getScoreByPhone({ phoneNum: phone.value }).then((res) => {
      Modal.info({
        title: '查询结果',
        closable: true,
        content: h('div', {}, [
          h('h4', `风险融合评分：${res.score}`),
          h('h4', `风险级别：${res.riskType}`),
          h('h4', `敏感级别：${res.sensitiveType}`),
        ]),
        okText: '确定',
      });
    });
  }

  function goBack() {
    router.replace({
      path: '/workbench',
    });
  }

  // 静态数据，仅展示
  let dataSource = [
    {
      key: '1',
      op: '操作',
      phone: '输入',
      result: ['返回'],
    },
    {
      key: '2',
      op: '字段',
      phone: '手机号',
      result: ['非黑名单用户 /\n', '语音投诉风险融合评分：90分\n', '风险级别：高风险', '敏感级别：高敏感'],
    },
  ];
</script>
