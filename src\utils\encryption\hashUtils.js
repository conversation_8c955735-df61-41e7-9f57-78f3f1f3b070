import CryptoJS from 'crypto-js';
import JSEncrypt from 'jsencrypt';
import { randomString } from '../index';
const iv = CryptoJS.enc.Utf8.parse('c1d6RiU7yMVIR8zA');
class HashUtils {
  // 公钥
  static rsaPublishKey;
  // 私钥（对称密匙）
  static rsaPrivateKey;
  static aesKey;
  static setPublishKey(key) {
    this.rsaPublishKey = key;
  }
  static async updatePrivateKey() {
    const random = randomString(16);
    this.aesKey = random;
   // this.rsaPrivateKey = random;
    this.rsaPrivateKey = this.rsaEncrypt(random);
    return this.rsaPrivateKey;
  }

  static md5HexHash(input) {
    const hash = CryptoJS.MD5(input);
    return hash.toString(CryptoJS.enc.Hex);
  }

  // 计算MD5-base64
  static md5Base64Hash(input) {
    // 对字符串进行MD5加密
    const md5Hash = CryptoJS.MD5(input);
    // 将MD5结果转换为Base64格式
    return md5Hash.toString(CryptoJS.enc.Base64);
  }

  // 计算sha1-十六进制
  static sha1HexHash(input) {
    const sha1Hash = CryptoJS.SHA1(input);
    // 输出SHA-1加密后的十六进制字符串
    return sha1Hash.toString(CryptoJS.enc.Hex);
  }

  // 计算sha1-base64
  static sha1Base64Hash(input) {
    const sha1Hash = CryptoJS.SHA1(input);
    // 输出SHA-1加密后的十六进制字符串
    return sha1Hash.toString(CryptoJS.enc.Base64);
  }

  // 计算sha256-十六进制
  static sha256HexHash(input) {
    const sha1Hash = CryptoJS.SHA256(input);
    return sha1Hash.toString(CryptoJS.enc.Hex);
  }

  // 计算sha256-base64
  static sha256Base64Hash(input) {
    const sha1Hash = CryptoJS.SHA256(input);
    return sha1Hash.toString(CryptoJS.enc.Base64);
  }

  // 计算sha384-十六进制
  static sha384HexHash(input) {
    const sha1Hash = CryptoJS.SHA384(input);
    return sha1Hash.toString(CryptoJS.enc.Hex);
  }

  // 计算sha384-base64
  static sha384Base64Hash(input) {
    const sha1Hash = CryptoJS.SHA384(input);
    return sha1Hash.toString(CryptoJS.enc.Base64);
  }

  // 计算sha512-十六进制
  static sha512HexHash(input) {
    const sha1Hash = CryptoJS.SHA512(input);
    return sha1Hash.toString(CryptoJS.enc.Hex);
  }

  // 计算sha512-base64
  static sha512Base64Hash(input) {
    const sha1Hash = CryptoJS.SHA512(input);
    return sha1Hash.toString(CryptoJS.enc.Base64);
  }

  // 计算sha3-十六进制
  static sha3HexHash(input) {
    const sha1Hash = CryptoJS.SHA3(input);
    return sha1Hash.toString(CryptoJS.enc.Hex);
  }

  // 计算sha3-base64
  static sha3Base64Hash(input) {
    const sha1Hash = CryptoJS.SHA3(input);
    return sha1Hash.toString(CryptoJS.enc.Base64);
  }

  // 计算sha224-十六进制
  static sha224HexHash(input) {
    const sha1Hash = CryptoJS.SHA224(input);
    return sha1Hash.toString(CryptoJS.enc.Hex);
  }

  // 计算sha224-base64
  static sha224Base64Hash(input) {
    const sha1Hash = CryptoJS.SHA224(input);
    return sha1Hash.toString(CryptoJS.enc.Base64);
  }
  static hexToBinary(hexString) {
    let binaryString = '';
    for (let i = 0; i < hexString.length; i++) {
      const binaryValue = parseInt(hexString[i], 16).toString(2).padStart(4, '0');
      binaryString += binaryValue;
    }
    return binaryString;
  }
  //rsa加密
  static rsaEncrypt(str) {
    console.log(str,'str')
    const encryptor = new JSEncrypt();
    encryptor.setPublicKey(this.rsaPublishKey);
    const maxLength = 110; // 1024 位密钥的最大加密长度
    let encryptedChunks = [];
    for (let i = 0; i < str.length; i += maxLength) {
      const chunk = str.slice(i, i + maxLength);
      const encryptedChunk = encryptor.encrypt(chunk)
      encryptedChunks.push(encryptedChunk);
     // console.log(encryptedChunks,'encryptedChunks')
    }
    let encrypted = encryptedChunks.join('|');
    console.log(encrypted,'encrypted')
    return encrypted;
  }
  static rsaDecrypt(encryptedData) {
    let privateKey = 'MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAIYGFR3k8nNok4wxoUlFUNRa0BsGK3Ft0g9MXeitctj/+UaopAhqDk63axK7Mpao3STyxd/L2fQUB1gRXBZQwrU+eCgSKLctFt1sYgBs0fjb7OEYkSCPp6XcBA2ZDaaRbX4a0uHMFhHnb89n9z67l2gc0p6OpfQVqCOD8xpHU/exAgMBAAECgYABWp2LKjAJL/Dbp/hd6IwcP8LzSZuV6gWG/4nBqxeVBHhUzn6CZ5XiQDKd+L5WV3mNIRcUKiJMd7OqSq9Fj3EkG+HwrVEwc7nX1ZnxCGr5qCGgbiJlda/gt9jR+d3oyKN0Ey+VmY0LzbksiaDcOCN1vcpuaOjv37Uldxc4HKe0cQJBAIxUIJvSydN95zyo7FcvEasJEDW/NDqq6ZWvaKUOqUovr6eWkX2TXbC0kP+mz+yfN3pKbLCPzcABxLh9O0/VNskCQQD0f4PmIbBKb5keszHIXbHSucZ5ZNEjExHEqAmjMlMQ0wGYpcsM8qIhR9Jx9KkQwtAp0+86+dif8eUp3NZIieWpAkA2FbqXtC9blxwHkie9FoQdwpl/zZjov7TVn0yayULQN/7gfbTHjIPAtmdoUTe0QE1WkdbAWjx3s3bJkGLKyyeZAkAvbKyA7vfq+EqTEd8OJl5fqXk+ArguPXys0ItOBGzoToV4vm/sSwP3wJ6pS/OfNKp78ofswJmsLJduz08hMt5BAkBrd2kdjF7Qk4hbl3xqcVkPcGicKkXFlfN3/V1+JHum7fybXdTBD9lAY9X1aSrnICWQfKil68cqEWrCSo/mjqcF'
    const decryptor = new JSEncrypt()
    decryptor.setPrivateKey(privateKey)
    // 拆分加密块（兼容可能存在的空块）
    console.log(encryptedData,'encryptedData')
    const chunks = encryptedData.split('|').filter(chunk => chunk.trim().length > 0)
    console.log(chunks,'chunks')
    let decryptedText = ''
    for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i]
        const decryptedChunk = decryptor.decrypt(chunk)
        console.log(decryptedChunk,'decryptedChunk')
        decryptedText += decryptedChunk
      }
      console.log(decryptedText,'decryptedText')
      return decryptedText
  }
  // static rsaEncrypt(str) {
  //   const encryptor = new JSEncrypt();
  //   encryptor.setPublicKey(this.rsaPublishKey);
  //   const encrypted = encryptor.encrypt(str);
  //   return encrypted;
  // }
  
  // static rsaDecrypt(encryptedStr) {
  //   const decryptor = new JSEncrypt();
  //   decryptor.setPrivateKey(this.rsaPrivateKey);
  //   return decryptor.decrypt(encryptedStr);
  // }

  //AES解密方法
  static aesDecrypt(word) {
    const aesKey = this.aesKey || '';
    const keyUTF8 = CryptoJS.enc.Utf8.parse(aesKey);
    const wordHex = CryptoJS.enc.Hex.parse(word);
    const wordBase64 = CryptoJS.enc.Base64.stringify(wordHex);
    const decrypt = CryptoJS.AES.decrypt(wordBase64, keyUTF8, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });
    let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
    return decryptedStr.toString();
  }
  //AES加密方法
  static aesEncrypt(word) {
    const aesKey = this.aesKey || '';
    const wordUTF8 = CryptoJS.enc.Utf8.parse(word);
    const keyUTF8 = CryptoJS.enc.Utf8.parse(aesKey);
    const encrypted = CryptoJS.AES.encrypt(wordUTF8, keyUTF8, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });
    return encrypted.ciphertext.toString();
  }
}
export default HashUtils;
