import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';

enum Api {
  jdOrderList = '/biz/extAccountRecharge/pageList',
  jdOrderExcel = '/biz/extAccountRecharge/excel',
}

// export const getDictCheckList = (params) => {
//   return defHttp.post({ url: Api.dictCheckList, params });
// };

export const jdOrderList = (params) => {
  return defHttp.post({ url: Api.jdOrderList, params });
};

export const jdOrderExcel = (params = {}) =>
  defHttp.post(
    {
      url: Api.jdOrderExcel,
      params,
      responseType: 'blob', // 设置响应类型为blob
    },
    {
      isReturnNativeResponse: true, // 返回原始响应以获取headers
    }
  );
