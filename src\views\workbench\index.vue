<template>
  <div>
    <h3 class="text-right mt-2 font-bold pt-5 pr-5%"
      ><span class="cursor-pointer hover:underline" @click="toSearch">语音投诉风险融合评分查询入口</span></h3
    >
    <div class="p-5 pr-0 flex flex-wrap justify-start">
      <!-- 动态报告类型卡片 -->
      <div
        v-for="card in cardList"
        :key="card.id"
        class="p-5 mr-3% mb-5 bg-[#FFF] w-30% rounded-10px relative flex flex-col cursor-pointer hover:shadow-lg"
        @click="toList(card.id, card.title)"
      >
        <div class="mx-auto mt-4">
          <img class="img" :src="card.cardImg" />
        </div>
        <h3 class="text-center my-2 font-bold">{{ card.title }}</h3>
      </div>
      <!-- 静态卡片 -->
      <div class="p-5 mr-3% mb-5 bg-[#FFF] w-30% rounded-10px relative flex flex-col cursor-pointer hover:shadow-lg">
        <div class="mx-auto mt-4">
          <img class="img" :src="moreImg" />
        </div>
        <h3 class="text-center my-2 font-bold">更多人群洞察，敬请期待...</h3>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import sensitiveImg from '@/assets/images/card/sensitive.png';
  import gangImg from '@/assets/images/card/gang.png';
  import incomeImg from '@/assets/images/card/income.png';
  import unusualImg from '@/assets/images/card/unusual.png';
  import abetImg from '@/assets/images/card/abet.png';
  import moreImg from '@/assets/images/card/more.png';

  let router = useRouter();

  // 报告类型数据
  let cardList = [
    {
      id: 1,
      title: '敏感投诉类人群洞察',
      cardImg: sensitiveImg,
    },
    {
      id: 2,
      title: '团伙投诉类人群洞察',
      cardImg: gangImg,
    },
    {
      id: 3,
      title: '被催收易诉类人群洞察',
      cardImg: incomeImg,
    },
    {
      id: 4,
      title: '账号异常类人群洞察',
      cardImg: unusualImg,
    },
    {
      id: 5,
      title: '教唆投诉类人群洞察',
      cardImg: abetImg,
    },
  ];

  function toSearch() {
    router.push({
      path: '/workbench/search',
    });
  }

  function toList(reportType, title) {
    router.push({
      path: '/workbench/list',
      query: {
        reportType,
        title,
      },
    });
  }
</script>

<style lang="less" scoped>
  .img {
    width: 400px;
    height: 200px;
    object-fit: contain;
  }
</style>
