import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import dayjs from 'dayjs';

export const columns: BasicColumn[] = [
  {
    title: 'Banner图片',
    dataIndex: 'imageUrl',
    width: 120,
    slots: { customRender: 'imageUrl' },
  },
  {
    title: '跳转链接',
    width: 150,
    dataIndex: 'jumpLink',
    customRender: ({ text }) => text || '—',
  },
  {
    title: '排序值',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '展示时间',
    dataIndex: 'startTime',
    customRender: ({ record }: { record: any }) => record.startTime + ' ～ ' + record.endTime,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    // customRender:({ text }) => +text === 1 ? '启用中' : '已停用',
    slots: { customRender: 'status' },
  },
  {
    title: '最新修改时间',
    dataIndex: 'updateTime',
  },
];

export const formSchema: FormSchema[] = [
  {
    label: 'banner图片',
    field: 'imageUrl',
    component: 'JImageUpload',
    componentProps: {
      fileSize: 1,
      promptText: '仅支持上传jpg/jpeg/png文件，图片尺寸比例为200:30，文件大小不超过1mb！',
    },
    dynamicRules: ({ values }) => {
      //需要return
      return [
        {
          //默认开启表单检验
          required: true,
          validator: (_, value) => {
            //需要return 一个Promise对象
            return new Promise((resolve, reject) => {
              if (!value) {
                reject('请上传正确格式/大小的banner图片！');
              }
              resolve();
            });
          },
        },
      ];
    },
    colProps: { span: 24 },
  },
  {
    label: '标题',
    field: 'title',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    componentProps: {
      maxLength: 50,
    },
  },
  {
    label: '跳转链接',
    field: 'jumpLink',
    component: 'Input',
    colProps: { span: 24 },
  },
  {
    label: '排序值',
    field: 'sortOrder',
    component: 'InputNumber',
    slot: 'sortOrderSlot',
    // componentProps: {
    //   min: 1,
    //   precision: 0,
    //   step: 1,
    // },
    required: true,
    colProps: { span: 24 },
  },
  {
    label: '展示终端',
    field: 'terminal',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_announcement_terminal',
      placeholder: '请选择展示终端',
      stringToNumber: true,
    },
    required: true,
  },
  {
    label: '展示时间',
    field: 'startTime',
    required: true,
    component: 'RangeDate',
    colProps: { span: 24 },
    componentProps: {
      //是否显示时间
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      //日期格式化
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      //范围文本描述用集合
      placeholder: ['请选择开始日期时间', '请选择结束日期时间'],
      disabledDate: (currentDate) => {
        return currentDate < dayjs().startOf('day');
      },
    },
  },
];
