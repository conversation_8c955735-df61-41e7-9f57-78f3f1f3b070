<template>
  <div class="p-10 flex w-full h-full bg-[#F0F0FF] relative">
    <div class="flex-1 flex flex-col p-5">
      <div class="flex items-center mb-5">
        <img :src="appLogo" />
        <h1 class="text-5xl font-bold ml-5 mb-0">政企权益管理中台</h1>
      </div>
      <!-- <h2 class="my-auto">提供投诉风险查询接口，定期更新不同投诉人群的洞察报告，辅助制定决策降低投诉。</h2> -->
      <img :alt="title" :src="loginBoxPng" class="w-4/5 mx-auto" />
    </div>
    <div class="flex-1 flex">
      <div class="m-auto p-10 bg-[#FFF]">
        <LoginForm />
      </div>
    </div>
    <!-- <div class="absolute bottom-10px left-1/2 -translate-x-1/2">在使用中遇到问题请联系：19831006575</div> -->
  </div>
</template>
<script lang="ts" setup>
  import { computed } from 'vue';
  import LoginForm from './LoginForm.vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import { useLoginState } from './useLogin';
  import loginBoxPng from '/@/assets/images/card/login-bg.png';
  import appLogo from '/@/assets/images/logo-dark.png';
  defineProps({
    sessionTimeout: {
      type: Boolean,
    },
  });
  const globSetting = useGlobSetting();
  const title = computed(() => globSetting?.title ?? '');
  const { handleBackLogin } = useLoginState();
  handleBackLogin();
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-login';
  @logo-prefix-cls: ~'@{namespace}-app-logo';
  @countdown-prefix-cls: ~'@{namespace}-countdown-input';
  @dark-bg: #293146;

  html[data-theme='dark'] {
    .@{prefix-cls} {
      background-color: @dark-bg;

      &::before {
        background-image: url(/@/assets/svg/login-bg-dark.svg);
      }

      .ant-input,
      .ant-input-password {
        background-color: #232a3b;
      }

      .ant-btn:not(.ant-btn-link):not(.ant-btn-primary) {
        border: 1px solid #4a5569;
      }

      &-form {
        background: transparent !important;
      }

      .app-iconify {
        color: #fff;
      }
    }

    input.fix-auto-fill,
    .fix-auto-fill input {
      -webkit-text-fill-color: #c9d1d9 !important;
      box-shadow: inherit !important;
    }
  }

  .@{prefix-cls} {
    min-height: 100%;
    overflow: hidden;

    @media (max-width: @screen-xl) {
      background-color: #293146;

      .@{prefix-cls}-form {
        background-color: #fff;
      }
    }

    &::before {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      margin-left: -48%;
      background: url(/@/assets/svg/login-bg.svg) right bottom / auto 100% no-repeat;
      content: '';

      @media (max-width: @screen-xl) {
        display: none;
      }
    }

    .@{logo-prefix-cls} {
      position: absolute;
      top: 12px;
      height: 30px;

      &-title {
        font-size: 16px;
        color: #fff;
      }

      img {
        width: 32px;
      }
    }

    .container {
      .@{logo-prefix-cls} {
        display: flex;
        width: 60%;
        height: 80px;

        &-title {
          font-size: 24px;
          color: #fff;
        }

        img {
          width: 48px;
        }
      }
    }

    &-sign-in-way {
      .anticon {
        font-size: 22px;
        color: #888;
        cursor: pointer;

        &:hover {
          color: @primary-color;
        }
      }
    }

    input:not([type='checkbox']) {
      min-width: 360px;

      @media (max-width: @screen-xl) {
        min-width: 320px;
      }

      @media (max-width: @screen-lg) {
        min-width: 260px;
      }

      @media (max-width: @screen-md) {
        min-width: 240px;
      }

      @media (max-width: @screen-sm) {
        min-width: 160px;
      }
    }

    .@{countdown-prefix-cls} input {
      min-width: unset;
    }

    .ant-divider-inner-text {
      font-size: 12px;
      color: @text-color-secondary;
    }
  }

  .titleDesc {
    font-size: 24px;
    text-align: center;
    margin: 24px 0 180px;
  }
</style>
