import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import dayjs from 'dayjs';

let srcOrderType = [];
// window.$getDicOptions('src_order_send_type').then((data) => {
//   srcOrderType = data;
// });

export const columns: BasicColumn[] = [
  {
    title: '订单号',
    dataIndex: 'orderNumber',
    width: 300,
  },
  {
    title: '用户手机号',
    dataIndex: 'msisdn',
  },
  {
    title: '兑换明细',
    dataIndex: 'exchangeDetails',
    slots: { customRender: 'exchangeDetails' },
  },
  {
    title: '订单状态',
    dataIndex: 'status',
  },
  {
    title: '订单创建时间',
    dataIndex: 'cteTime',
    // customRender: ({ text }) => atob(text),
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '订单号',
    field: 'orderNumber',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '手机号码',
    field: 'msisdn',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '订单状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '处理中', value: '处理中' },
        { label: '兑换成功', value: '兑换成功' },
        { label: '部分失败', value: '部分失败' },
        { label: '全部失败', value: '全部失败' },
      ],
      placeholder: '请选择',
    },
    colProps: { span: 8 },
  },
  {
    label: '创建时间',
    field: 'dateOp',
    component: 'RangeDate',
    componentProps: {
      //是否显示时间
      // showTime: true,
      format: 'YYYY-MM-DD',
      //日期格式化
      valueFormat: 'YYYY-MM-DD',
      //范围文本描述用集合
      placeholder: ['开始时间', '结束时间'],
      // disabledDate: (currentDate) => {
      //   return currentDate < dayjs().startOf('day');
      // },
    },
    colProps: { span: 8 },
  },
];

export const exchangeColumns: BasicColumn[] = [
  {
    title: '序号',
    dataIndex: 'index',
  },
  {
    title: '兑换权益商品ID',
    dataIndex: 'commodityId',
  },
  {
    title: '兑换权益商品名称',
    dataIndex: 'productName',
  },
  {
    title: '兑换价格',
    dataIndex: 'commodityPrice',
  },
  {
    title: '兑换结果',
    dataIndex: 'status',
    customRender: ({ text }) => (text === null ? '处理中' : +text === 0 ? '兑换失败' : '兑换成功'),
  },
  {
    title: '兑换失败原因',
    dataIndex: 'errorMsg',
  },
  {
    title: '兑换结果反馈时间',
    dataIndex: 'resultTime',
  },
];

export const paymentColumns: BasicColumn[] = [
  {
    title: '序号',
    dataIndex: 'index',
  },
  {
    title: '卡券ID',
    dataIndex: 'commodityTransId',
  },
  {
    title: '有效期截止日期',
    dataIndex: 'expiringDate',
  },
  {
    title: '卡券商品ID',
    dataIndex: 'commodityId',
  },
  {
    title: '卡券商品名称',
    dataIndex: 'productName',
  },
  {
    title: '卡券面额',
    dataIndex: 'commodityPrice',
  },
  {
    title: '支付金额',
    dataIndex: 'paymentPrice',
  },
  {
    title: '发放集团客户编码',
    dataIndex: 'groupId',
  },
  {
    title: '发放集团账户ID',
    dataIndex: 'channelId',
  },
  {
    title: '发放集团客户名称',
    dataIndex: 'channelName',
  },
];
//
