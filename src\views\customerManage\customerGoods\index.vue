<template>
  <PageWrapper>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'system:banner:add'" @click="handleRead()"> 订购折扣样例文件</a-button>
        <a-button type="primary" v-auth="'system:banner:add'" @click="importBatch()"> 订购折扣文件上传</a-button>
        <a-button type="primary" v-auth="'system:banner:add'" @click="downTemplate()"> 样例文件下载</a-button>
        <!-- <a-button type="primary" v-auth="'system:banner:add'" @click="removeUser({})"> 批量编辑</a-button> -->
        <a-button type="primary" v-auth="'system:banner:add'" @click="removeUser({})"> 批量删除</a-button>
        <a-button type="primary" v-auth="'system:banner:add'" @click="handleCreate()"> 新增</a-button>
        <a-button type="" v-auth="'system:banner:add'" @click="exportList()"> 导出</a-button>
      </template>
      <template #status="{ text }">
        <a-tag :color="+text === 0 ? 'error' : 'success'">{{ +text === 0 ? '已停用' : '启用中' }}</a-tag>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 验收弹窗 -->
    <!-- <CheckModel @register="registerCheckModal" @success="handleSuccess" /> -->
    <importData @register="registerPropertyModal" @success="handleSuccess" />
  </PageWrapper>
</template>

<script lang="ts" name="system-banner" setup>
  import { reactive } from 'vue';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { pageList, blackDelete, getBlacklistTemplate } from '/@/api/Integrated/black';
  import { columns, searchFormSchema } from './goods.data';
  import { Modal } from 'ant-design-vue';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import importData from './importData.vue';
  import { exportExcel } from '/@/utils/common/compUtils';
  import { func } from 'vue-types';
  import { get } from 'sortablejs';
  const [registerPropertyModal, { openModal }] = useModal();
  const { createMessage, createConfirm } = useMessage();
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'banner-list',
    tableProps: {
      api: pageList,
      columns: columns,
      size: 'small',
      showIndexColumn: false,
      // useSearchForm: false, // 是否使用搜索项
      rowSelection: { type: 'checkbox' }, //默认是 checkbox 多选，可以设置成 radio 单选
      formConfig: {
        showAdvancedButton: false,
        labelWidth: 100,
        schemas: searchFormSchema,
        actionColOptions: {
          xs: 24, // <576px
          sm: 24, // ≥576px
          md: 24, // ≥768px
          lg: 24, // ≥992px
          xl: 24, // ≥1200px
          xxl: 24,
          style: { textAlign: 'right' },
        },
      },
      actionColumn: {
        width: 200,
      },
      beforeFetch: (params) => {
        delete params.column;
        delete params.order;
        // return Object.assign({ }, params);
      },
    },
  });

  //注册table数据
  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  //新增
  let recordData = reactive({});
  function handleCreate() {
    openModal(true, {
      isAdd: 1,
      isUpdate: true,
      showFooter: true,
    });
  }
  // 黑名单读取
  async function handleRead() {
    // console.log(record, 'records');
    Modal.confirm({
      title: '是否下载订购折扣样例文件？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const res = await getBlacklistTemplate();
        exportExcel(res);
      },
    });
  }
  // 批量移除
  function removeUser(record) {
    console.log(record, 'records');
    Modal.confirm({
      title: +record.id ? '确定要将这个用户移除发放黑名单吗?' : '确定要将已经选中的用户移除发放黑名单吗?',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        let params = record.id ? [record.id] : selectedRowKeys.value;
        blackDelete(params).then((res) => {
          console.log(res, '===>res');
          createMessage.success('操作成功');
          reload();
        });
      },
    });
  }
  // 批量导入模板下载
  function downTemplate() {
    Modal.confirm({
      title: '是否要进行样例文件下载？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const res = await getBlacklistTemplate();
        exportExcel(res);
      },
    });
  }

  //导出列表
  function exportList() {
    Modal.confirm({
      title: '是否要导出列表？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const res = await getBlacklistTemplate();
        exportExcel(res);
      },
    });
  }

  // 批量导入
  function importBatch() {
    openModal(true, {
      isAdd: 0,
      isUpdate: true,
      showFooter: true,
    });
  }

  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: removeUser.bind(null, record),
        auth: 'system:banner:edit',
      },
      {
        label: '移除',
        onClick: removeUser.bind(null, record),
        auth: 'system:banner:edit',
      },
      {
        label: '短信配置',
        onClick: removeUser.bind(null, record),
        auth: 'system:banner:edit',
      },
    ];
  }
  function handleSuccess() {
    reload();
  }
</script>
