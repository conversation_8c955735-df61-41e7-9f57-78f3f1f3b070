import intro from 'intro.js';
import 'intro.js/minified/introjs.min.css';
import './introReset.less';
import { useDesign } from '/@/hooks/web/useDesign';
import TASKICON from '/@/assets/images/task-icon.png';
import TALENTICON from '/@/assets/images/talent-icon.png';
import MEDIATICON from '/@/assets/images/media-icon.png';
export function handleStart() {
  const { prefixCls } = useDesign('header-add-button');
  intro()
    .setOptions({
      showButtons: true,
      showStepNumbers: false,
      tooltipClass: 'intro-container',
      prevLabel: '上一步',
      nextLabel: '下一步',
      doneLabel: '完成',
      steps: [
        {
          title: '新手引导',
          intro: getStepOne(),
        },
        {
          title: '新手引导',
          element: document.getElementById(prefixCls),
          intro: '点击【提任务】后，快速进入任务创建页面~',
        },
      ],
    })
    .start();
}
function getStepOne() {
  const res = `<div class="intro-content">
                    <p>欢迎进入政企权益管理中台！</p>
                    <div class="intro-item">
                        <img class="intro-icon" src="${TALENTICON}">
                        <span class="intro-text">挑选达人或达人包、下单</span>
                        <a class="intro-btn" href="/talentSquare/list">进入</a>
                    </div>
                    <div class="intro-item">
                        <img class="intro-icon" src="${TASKICON}">
                        <span class="intro-text">指派任务或查看任务进度</span>
                        <a class="intro-btn" href="/task/index">进入</a>
                    </div>
                    <div class="intro-item">
                        <img class="intro-icon" src="${MEDIATICON}">
                        <span class="intro-text">上传或查看任务素材资源</span>
                        <a class="intro-btn" href="/material/list">进入</a>
                    </div>
                </div>`;
  return res;
}
