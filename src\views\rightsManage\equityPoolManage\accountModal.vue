<template>
  <BasicModal
    v-bind="$attrs"
    :title="'编辑权益池'"
    :defaultFullscreen="true"
    width="800px"
    @register="registerModal"
    @ok="handleSubmit"
    :loading="submitLoading"
  >
    <div class="rightName">
      <span>权益池名称：</span>
      <a-input v-model:value="rightName" :disabled="!!rightStatus" width="300" />
    </div>
    <a-tabs v-model:activeKey="rightActive" type="card" class="mt24" @change="tabsChange">
      <a-tab-pane key="1" tab="权益池权益"></a-tab-pane>
      <a-tab-pane key="2" tab="权益池卡券"></a-tab-pane>
    </a-tabs>
    <BasicForm @register="equityPoolForm" @submit="searchClick" />
    <a-transfer v-model:target-keys="currentTargetKeys" :data-source="currentDataSource" :show-select-all="false" class="table-transfer">
      <template #children="{ direction, selectedKeys, onItemSelect }">
        <a-table
          :columns="getColumns(direction)"
          :data-source="direction === 'left' ? leftData : rightData"
          :row-selection="{
            selectedRowKeys: selectedKeys,
            onChange: (keys) => handleSelectChange(keys, direction, onItemSelect),
          }"
          row-key="key"
          size="small"
          :pagination="false"
          :scroll="{ y: 600 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'sort'">
              <a-input-number v-model:value="record.sort" placeholder="请输入排序值" :min="1" :precision="0" />
            </template>
          </template>
        </a-table>
      </template>
    </a-transfer>
  </BasicModal>
</template>

<script lang="ts">
  export default {
    name: 'memberManage-accountModal',
  };
</script>

<script lang="ts" setup>
  import { ref, computed, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { cardColumnsLeft, cardColumnsRight, rightColumnsLeft, rightColumnsRight, equityPoolSchema } from './equityPool.data';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { getAddRightsAndCards, getById, equityPoolAddCard, equityPoolAdd } from '/@/api/productconfig/equity-pool';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { e } from 'unocss';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  // 响应式数据
  const rightName = ref('');
  const rightActive = ref('1');
  const rightStatus = ref(0);
  const rightsDataSource = reactive<any[]>([]); // 权益数据源
  const cardsDataSource = reactive<any[]>([]); // 卡券数据源
  const submitLoading = ref(false);
  const targetKeys = ref({
    rights: [] as string[], // 权益选中项
    cards: [] as string[], // 卡券选中项
  });

  // 计算属性
  const currentDataSource = computed(() => (+rightActive.value === 1 ? rightsDataSource : cardsDataSource));
  // 计算属性优化
  const getColumns = computed(() => {
    return (direction: string) => {
      if (+rightActive.value === 1) {
        return direction === 'left' ? rightColumnsLeft : rightColumnsRight;
      } else {
        return direction === 'left' ? cardColumnsLeft : cardColumnsRight;
      }
    };
  });
  const currentTargetKeys = computed({
    get: () => (+rightActive.value === 1 ? targetKeys.value.rights : targetKeys.value.cards),
    set: (val) => {
      if (+rightActive.value === 1) {
        targetKeys.value.rights = val;
      } else {
        targetKeys.value.cards = val;
      }
    },
  });

  const leftData = computed(() => currentDataSource.value.filter((item) => !currentTargetKeys.value.includes(item.key)));

  const rightData = computed(() => currentDataSource.value.filter((item) => currentTargetKeys.value.includes(item.key)));

  // 方法
  const handleSelectChange = (keys: string[], direction: string, onItemSelect: (key: string, selected: boolean) => void) => {
    const currentPanelData = direction === 'left' ? leftData.value : rightData.value;
    currentPanelData.forEach((item) => {
      onItemSelect(item.key, keys.includes(item.key));
    });
  };

  const tabsChange = (key: string) => {
    console.log(`切换到 ${key === '1' ? '权益' : '卡券'} Tab`);
  };

  const [equityPoolForm, equityPoolFormAction] = useForm({
    schemas: equityPoolSchema,
    showActionButtonGroup: true,
    showResetButton: true,
    labelCol: { style: { width: '150px' } },
    resetFunc: customResetFunc,
    actionColOptions: { span: 10 },
  });

  async function searchClick() {
    try {
      const values = await equityPoolFormAction.validate();
      const params = { ...values, type: +rightActive.value - 1 };
      const res = await getAddRightsAndCards(params);

      if (!res) {
        return;
      }

      // 1. 保存当前已选中的完整数据（包括原始顺序）
      const currentSelectedItems = [...rightData.value]; // 关键点：直接使用rightData保持顺序

      if (+rightActive.value === 1) {
        // 2. 处理新数据（权益）
        const newDataMap = new Map(res.map((item) => [item.commodityId.toString(), item]));

        // 3. 按原始顺序重建数据
        rightsDataSource.length = 0;

        // 先添加已选中的项（保持原顺序）
        currentSelectedItems.forEach((item) => {
          const newItem = newDataMap.get(item.key);
          if (newItem) {
            rightsDataSource.push({
              ...newItem,
              key: item.key,
              sort: item.sort, // 保留原sort值
              price: newItem.price ?? 0,
              discount: newItem.discount ?? 0,
            });
            newDataMap.delete(item.key); // 已处理的移除
          } else {
            rightsDataSource.push(item); // 保留不在新数据中的已选项
          }
        });

        // 再添加剩余的新数据（未选中的）
        newDataMap.forEach((value, key) => {
          rightsDataSource.push({
            ...value,
            key,
            sort: 0, // 新数据默认sort
            price: value.price ?? 0,
            discount: value.discount ?? 0,
          });
        });
      } else {
        // 同上处理卡券数据
        const newDataMap = new Map(res.map((item) => [item.commodityId.toString(), item]));

        cardsDataSource.length = 0;

        currentSelectedItems.forEach((item) => {
          const newItem = newDataMap.get(item.key);
          if (newItem) {
            cardsDataSource.push({
              ...newItem,
              key: item.key,
              sort: item.sort,
              price: newItem.price ?? 0,
              discount: newItem.discount ?? 0,
            });
            newDataMap.delete(item.key);
          } else {
            cardsDataSource.push(item);
          }
        });

        newDataMap.forEach((value, key) => {
          cardsDataSource.push({
            ...value,
            key,
            sort: 0,
            price: value.price ?? 0,
            discount: value.discount ?? 0,
          });
        });
      }

      // 4. 保持选中状态不变
      if (+rightActive.value === 1) {
        targetKeys.value.rights = currentSelectedItems.map((item) => item.key);
      } else {
        targetKeys.value.cards = currentSelectedItems.map((item) => item.key);
      }
    } catch (error) {
      console.error('搜索失败:', error);
      createMessage.error('搜索失败，请重试');
    }
  }
  async function customResetFunc() {
    await equityPoolFormAction.setFieldsValue({
      searchName: '',
      category: undefined,
    });
  }
  const recordId = ref('');
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    rightName.value = '';
    rightsDataSource.length = 0;
    cardsDataSource.length = 0;
    targetKeys.value = { rights: [], cards: [] };

    rightStatus.value = data.status;
    if (data.status !== 0) {
      rightActive.value = data.status.toString();
      recordId.value = data.record.id;
      await getDetails(data.record.id);
    }
  });

  const getDetails = async (id: string) => {
    try {
      const res = await getById({ id });
      if (!res) {
        createMessage.error('获取数据失败');
        return;
      }

      // 重置数据
      rightName.value = res.name || '';
      rightsDataSource.length = 0;
      cardsDataSource.length = 0;
      targetKeys.value = { rights: [], cards: [] };

      // 处理权益数据 (rights)
      if (res.rights?.length) {
        rightsDataSource.push(
          ...res.rights.map((item) => ({
            ...item,
            key: item.commodityId.toString(),
            // 确保所有必要字段都有值
            price: item.price ?? 0,
            discount: item.discount ?? 0,
            // sort: 0, // 默认排序值
          }))
        );
        targetKeys.value.rights = res.rights.map((item) => item.commodityId.toString());
      }

      // 处理卡券数据 (commodityList)
      if (res.commodityList?.length) {
        cardsDataSource.push(
          ...res.commodityList.map((item) => ({
            ...item,
            key: item.commodityId.toString(),
            price: item.price ?? 0,
            discount: item.discount ?? 0,
            sort: 0,
          }))
        );
        targetKeys.value.cards = res.commodityList.map((item) => item.commodityId.toString());
      }

      console.log('数据加载完成:', {
        rightsData: rightsDataSource,
        cardsData: cardsDataSource,
        targetKeys: targetKeys.value,
      });
    } catch (error) {
      createMessage.error('获取详情失败');
      console.error('getDetails error:', error);
    }
  };

  async function handleSubmit() {
    if (!rightName.value) {
      createMessage.error('请输入权益池名称！');
      return;
    }
    const selectedRights = rightsDataSource.filter((item) => targetKeys.value.rights.includes(item.key));
    const selectedCards = cardsDataSource.filter((item) => targetKeys.value.cards.includes(item.key));

    if (selectedRights.length === 0 || selectedCards.length === 0) {
      createMessage.error('请至少选择一项权益和卡券！');
      return;
    }
    if (submitLoading.value) return;
    submitLoading.value = true;
    const params = {
      id: '0',
      name: rightName.value,
      type: +rightActive.value - 1,
      rights: selectedRights.map((el) => {
        return {
          commodityId: el.key,
          sort: el.sort,
        };
      }),
      commodityIds: selectedCards.map((el) => el.commodityId),
    };
    try {
      if (rightStatus.value === 0) {
        await equityPoolAdd(params);
      } else {
        params.id = recordId.value;
        await equityPoolAddCard(params);
      }
      createMessage.success(rightStatus.value === 0 ? '新增成功！' : '编辑成功！');
      closeModal();
      rightsDataSource.length = 0;
      cardsDataSource.length = 0;
      submitLoading.value = false;
      emit('success');
    } catch (error) {
      submitLoading.value = false;
      createMessage.error('操作失败');
    }
  }
</script>

<style scoped lang="less">
  .ml8 {
    margin-left: 8px;
  }
  .mt24 {
    margin-top: 24px;
  }
  .tips {
    color: #999;
  }
  .rightName {
    display: flex;
    align-items: center;
    span {
      display: block;
      width: 150px;
      text-align: right;
    }
    .ant-input {
      width: 300px;
    }
  }
  .table-transfer {
    width: 100%;

    /deep/.ant-transfer-list {
      width: 48%;
    }
  }
</style>
