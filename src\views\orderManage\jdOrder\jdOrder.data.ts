import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import dayjs from 'dayjs';

let srcOrderType = [];
window.$getDicOptions('order_right_status').then((data) => {
  srcOrderType = data;
});

export const columns: BasicColumn[] = [
  // {
  //   title: '订单ID',
  //   dataIndex: 'orderId',
  //   customRender: ({ text }) => text || '—',
  //   width: 200,
  // },
  {
    title: '充值主订单号',
    dataIndex: 'id',
    width: 200,
  },
  {
    title: '充值子订单号',
    dataIndex: 'extAcctRechargeId',
    width: 200,
  },
  {
    title: '充值手机号',
    dataIndex: 'msisdn',
  },
  {
    title: '充值金额',
    dataIndex: 'rechargeAmount',
  },
  {
    title: '订单状态',
    dataIndex: 'orderRightStatus',
    customRender: ({ text }) => {
      return srcOrderType.find((item: any) => item.value === text)?.label || text || '—';
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '成功时间',
    dataIndex: 'updateTime',
    width: 200,
  },
];

export const searchFormSchema: FormSchema[] = [
  // {
  //   label: 'ID',
  //   field: 'id',
  //   component: 'Input',
  //   colProps: { span: 8 },
  // },
  {
    label: '充值主订单号',
    field: 'id',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '充值子订单号',
    field: 'extAcctRechargeId',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '手机号',
    field: 'msisdn',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '订单状态',
    field: 'orderRightStatus',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'order_right_status',
    },
    colProps: { span: 8 },
  },
  {
    label: '创建时间',
    field: 'dateOp',
    component: 'RangeDate',
    componentProps: {
      //是否显示时间
      // showTime: true,
      format: 'YYYY-MM-DD',
      //日期格式化
      valueFormat: 'YYYY-MM-DD',
      //范围文本描述用集合
      placeholder: ['开始时间', '结束时间'],
      // disabledDate: (currentDate) => {
      //   return currentDate < dayjs().startOf('day');
      // },
    },
    colProps: { span: 8 },
  },
  {
    label: '成功时间',
    field: 'dateOp2',
    component: 'RangeDate',
    componentProps: {
      //是否显示时间
      // showTime: true,
      format: 'YYYY-MM-DD',
      //日期格式化
      valueFormat: 'YYYY-MM-DD',
      //范围文本描述用集合
      placeholder: ['开始时间', '结束时间'],
      // disabledDate: (currentDate) => {
      //   return currentDate < dayjs().startOf('day');
      // },
    },
    colProps: { span: 8 },
  },
];
