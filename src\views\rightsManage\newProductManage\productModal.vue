<template>
  <BasicModal
    v-bind="$attrs"
    :title="getTitle"
    :defaultFullscreen="true"
    width="800px"
    @register="registerModal"
    @ok="handleSubmit"
    :closeFunc="handleBeforeClose"
  >
    <a-card :title="productTitle" :bordered="true">
      <BasicForm @register="registerForm">
        <template #groupsNum="{ model, field }">
          <a-input :disabled="groupDisable" v-model:value="model[field]" placeholder="0<=N<=5" @change="inputchange" />
        </template>
        <template #mainProductNum="{ model, field }">
          <a-radio-group v-model:value="model[field]" @change="radioClick" :disabled="unref(isUpdate)">
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </template>
        <template #commodityCategory="{ model, field }">
          <JDictSelectTag
            type="select"
            v-model:value="model[field]"
            dictCode="commodity_category"
            placeholder="请选择商品类别"
            :disabled="unref(isUpdate)"
            @change="changeCategory"
          />
        </template>
      </BasicForm>
    </a-card>
    <a-card title="主产品选取" :bordered="true" v-if="showMainProduct">
      <BasicForm @register="registermainProductForm" @submit="seachclick" v-if="!unref(isUpdate)"> </BasicForm>
      <a-divider style="height: 2px" />
      <BasicForm @register="registerProductForm">
        <template #productId="{ model, field }">
          <a-select v-model:value="model[field]" @change="productChange" :disabled="unref(isUpdate)">
            <template v-for="(item, index) in productOptions" :key="index">
              <a-select-option :value="item.productId">({{ item.productCode }}){{ item.productName }}</a-select-option>
            </template>
          </a-select>
        </template>
      </BasicForm>
    </a-card>
    <a-card title="小组配置" :bordered="true" v-if="groupShow">
      <a-form :model="formState" :label-col="labelCol" :wrapper-col="wrapperCol">
        <div v-for="(group, index) in formState.groups" :key="index">
          <a-row :gutter="8">
            <a-col :span="2">小组{{ index + 1 }}</a-col>
            <a-col :span="10">
              <a-form-item :label="`小组${index + 1}首订方式`">
                <a-radio-group v-model:value="group.orderType" :disabled="unref(isUpdate)">
                  <a-radio value="1">系统自动订购</a-radio>
                  <a-radio value="0">用户领取</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :label="`小组${index + 1}续订方式`">
                <a-radio-group v-model:value="group.renewType" :disabled="unref(isUpdate)">
                  <a-radio value="0">系统续订</a-radio>
                  <a-radio value="2">用户领取</a-radio>
                  <a-radio value="1">不续订</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="8">
            <a-col :span="2">(小组ID: {{ group.groupId }})</a-col>
            <a-col :span="10">
              <a-form-item :label="`小组名称`" :name="['groups', index, 'groupName']">
                <a-input v-model:value="group.groupName" :placeholder="`请输入小组名称`" :disabled="unref(isUpdate)" class="input-param" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :label="`小组可选产品数`" :name="['groups', index, 'optionNum']">
                <a-input v-model:value="group.optionNum" :placeholder="`请输入小组可选产品数`" :disabled="unref(isUpdate)" class="input-param" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :label="`小组订购次数`" :name="['groups', index, 'orderNum']">
                <a-input v-model:value="group.orderNum" :placeholder="`请输入小组订购次数`" :disabled="unref(isUpdate)" class="input-param" />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="!unref(isUpdate)">
              <a-form-item>
                <a-button type="primary" @click="configGroupProducts(index)"> 配置/修改组内产品 </a-button>
              </a-form-item>
            </a-col>
          </a-row>

          <a-divider v-if="index < formState.groups.length - 1" />
        </div>
      </a-form>
    </a-card>
    <a-card :title="'规格设置'" :bordered="true" style="margin-top: 16px" v-if="specsShow">
      <searchSelect :excluded-values="addedValues" @add="handleAddItem" style="margin-top: 16px"></searchSelect>
      <a-table :columns="productColumns" :data-source="productDataSource" style="margin-top: 16px" rowKey="commodityId">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'commodityCode'">
            <span>{{ record.commodityCode }}</span>
          </template>
          <template v-if="column.key === 'commodityName'">
            <span>{{ record.commodityName }}</span>
          </template>
          <template v-if="column.key === 'sort'">
            <a-input v-model:value="record.sort" placeholder="请输入排序值" />
          </template>
          <template v-if="column.key === 'actions'">
            <a-button type="link" @click="removeItem(record.commodityId)">删除</a-button>
          </template>
        </template>
      </a-table>
    </a-card>
  </BasicModal>
  <a-modal
    v-model:visible="productShow"
    title="组内产品配置"
    @ok="handleOk"
    width="1000px"
    :destroyOnClose="true"
    :maskClosable="true"
    wrapClassName="preview-modal"
  >
    <div style="padding: 10px">
      <BasicForm @register="registermainProductForm" @submit="seachclick"></BasicForm>
      <div style="display: flex; justify-content: center">
        <a-transfer
          v-model:target-keys="targetKeys"
          v-model:selected-keys="selectedKeys"
          :data-source="productData"
          :titles="['待选区', '已选区']"
          :render="(item) => '(' + item.description + ')' + item.title"
          @change="handleChange"
          @selectChange="handleSelectChange"
          :list-style="{
            width: '250px',
            height: '300px',
          }"
        />
      </div>
    </div>
    <a-table :dataSource="dataSource" :pagination="false" :columns="pruductColumns"> </a-table>
  </a-modal>
</template>
<script lang="ts" name="PassWordModal" setup>
  import { ref, computed, unref, watch, reactive } from 'vue';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formAnnountSchema, formMainProductSchema, formProductSchema, productColumns } from './product.data';
  import { getCommodityCode, getQueryProduct, getGroupId, addCommodity, updateCommodity, commodityDetail } from '/@/api/productconfig/productconfig';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getDicOptions } from '/@/utils/index';
  import searchSelect from './searchSelect.vue';
  import { add } from '/@/components/Form/src/componentMap';
  const isUpdate = ref(false);
  const commodityId = ref(undefined);
  const showFooter = ref(true);
  const productShow = ref<boolean>(false);
  const groupDisable = ref(false);
  const showMainProduct = ref(true);
  const departOptions = ref([]);
  let formState = reactive({
    groups: [],
  });
  let groupIndexNum = ref(0);
  const dataSource = ref([]);
  const groupList = ref([]);
  const productOptions = ref([]);
  const groupShow = ref(false);
  const groupNum = ref('');
  const groupForm = ref({});
  const productsCode = ref('');
  const productsId = ref('');
  const productDataSource = reactive<any[]>([]);
  const specsShow = ref(false);
  let service_Cat = [];
  let common_status = [];
  let renewal_order_type = [];
  const pruductColumns = [
    {
      title: '产品ID',
      dataIndex: 'productCode',
      width: 150,
      align: 'center',
    },
    {
      title: '产品名称',
      width: 150,
      dataIndex: 'productName',
      align: 'center',
    },
    {
      title: '产品类型',
      dataIndex: 'serviceCat',
      width: 150,
      align: 'center',
      customRender: ({ text }) => {
        let result = service_Cat.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
    {
      title: '订购类型',
      width: 150,
      dataIndex: 'renewalOrderType',
      customRender: ({ text }) => {
        let result = renewal_order_type.find((item) => item.value === text) || {};
        return result.label || text;
      },
      align: 'center',
    },
    // {
    //   title: '产品周期',
    //   dataIndex: 'flowCycle',
    //   width: 150,
    //   align:'center'
    // },
    {
      title: '产品状态',
      width: 150,
      dataIndex: 'status',
      align: 'center',
      customRender: ({ text }) => {
        let result = common_status.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
  ];
  interface MockData {
    key: string | number;
    title: string;
    description: string;
    productType: string | number;
    serviceCat: string | number;
    flowCycle: string | number;
    status: string | number;
    renewalOrderType: string | number;
  }
  const productData = ref<MockData[]>([]);
  //const oriTargetKeys = mockData.filter(item => +item.key % 3 > 1).map(item => item.key);
  const targetKeys = ref<string[]>([]);
  const selectedKeys = ref<string[]>([]);
  const labelCol = { style: { width: '200px' } };
  const wrapperCol = { span: 24 };
  // 表单状态
  const { createMessage } = useMessage();
  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  //表单配置
  const [registerForm, formActionType] = useForm({
    schemas: formAnnountSchema,
    showActionButtonGroup: false,
    labelCol: { style: { width: '190px' } },
    labelAlign: 'right',
    //labelCol: { style: { width: '150px' } }
  });
  //主产品表单
  const [registermainProductForm, mainProductFormActionType] = useForm({
    schemas: formMainProductSchema,
    showActionButtonGroup: true,
    showResetButton: true,
    labelCol: { style: { width: '150px' } },
    submitButtonOptions: { text: '搜索产品', preIcon: '' },
    // resetButtonOptions: { text: '重置', preIcon: '' },
    resetFunc: customResetFunc,
    actionColOptions: { span: 10 },
  });

  const [registerProductForm, productFormActionType] = useForm({
    schemas: formProductSchema,
    showActionButtonGroup: false,
    labelCol: { style: { width: '150px' } },
  });
  const validateForm = () => {
    for (const [index, group] of formState.groups.entries()) {
      if (!group.groupName) {
        return { valid: false, message: `请输入小组${index + 1}名称` };
      }
      if (!group.optionNum) {
        return { valid: false, message: `请输入小组${index + 1}可选产品数` };
      }
      if (!group.orderNum) {
        return { valid: false, message: `请输入小组${index + 1}订购次数` };
      }

      // 数字校验
      if (isNaN(group.optionNum) || Number(group.optionNum) <= 0) {
        return { valid: false, message: `小组${index + 1}可选产品数必须为正数` };
      }
      if (isNaN(group.orderNum) || Number(group.orderNum) <= 0) {
        return { valid: false, message: `小组${index + 1}订购次数必须为正数` };
      }
    }
    return { valid: true };
  };
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    console.log(data, 'data');
    //重置表单
    await formActionType.resetFields();
    await mainProductFormActionType.resetFields();
    await productFormActionType.resetFields();
    showFooter.value = data?.showFooter ?? true;
    setModalProps({ confirmLoading: false, showOkBtn: showFooter.value });
    isUpdate.value = !!data?.isUpdate;

    if (unref(isUpdate)) {
      commodityId.value = data?.record?.commodityId;
      let singleFlag = data?.record?.singleFlag;
      let mainProductServiceCat = data?.record?.mainProductServiceCat;
      getDetail(commodityId.value);
      groupDisable.value = true;
      formActionType.updateSchema([
        {
          field: 'remark',
          dynamicDisabled: () => false,
        },
        {
          field: 'payType',
          dynamicDisabled: () => true,
        },
      ]);
      productFormActionType.updateSchema([
        {
          field: 'isActive',
          dynamicDisabled: () => false,
          ifShow: mainProductServiceCat != 2,
        },
        {
          field: 'sendType',
          dynamicDisabled: () => false,
          ifShow: singleFlag == 1,
        },
        {
          field: 'isPresend',
          dynamicDisabled: () => false,
          ifShow: mainProductServiceCat != 2,
        },
      ]);
      getProduct();
    } else {
      getCommotCode();
      formActionType.setFieldsValue({
        renewType: 1,
      });
      productFormActionType.setFieldsValue({
        productOrderType: '1',
        productRenewType: '0',
        isActive: 0,
        isPresend: 0,
        sendType: 0,
        productsCode: '',
        productsId: '',
      });
      formActionType.updateSchema([
        {
          field: 'payType',
          dynamicDisabled: () => false,
        },
      ]);
      productFormActionType.updateSchema([
        {
          field: 'sendType',
          ifShow: false,
        },
      ]);
      groupDisable.value = false;
      showMainProduct.value = true;
      specsShow.value = false;
      productDataSource.length = 0;
    }
    formActionType.setProps({ disabled: unref(isUpdate) });
    productFormActionType.setProps({ disabled: unref(isUpdate) });
    formActionType.clearValidate();
    mainProductFormActionType.clearValidate();
    productFormActionType.clearValidate();
  });

  //设置标题
  const getTitle = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));
  const productTitle = computed(() => (!unref(isUpdate) ? '新增商品' : '编辑商品'));

  async function getDetail(commodityId) {
    const res = await commodityDetail({ commodityId: commodityId });
    console.log(res, 'ress');
    if (res) {
      if (res.commodityInfoRes) {
        showMainProduct.value = true;
        specsShow.value = false;
        if (res.commodityInfoRes.commodityCategory == '2') {
          formActionType.setFieldsValue({
            commodityCategory: String(res.commodityInfoRes.commodityCategory),
            commodityCode: res.commodityInfoRes.commodityCode,
            commodityName: res.commodityInfoRes.commodityName,
            province: res.commodityInfoRes.province,
          });
          showMainProduct.value = false;
          specsShow.value = true;
          productDataSource.length = 0;
          if (res.commodityGroupEntities) {
            let _centerArray = res.commodityGroupEntities.map((el) => {
              return {
                commodityId: el.slaveCommodityId,
                sort: el.sort,
              };
            });
            productDataSource.push(..._centerArray);
            addedItems.value = [...productDataSource];
          }
        } else {
          formActionType.setFieldsValue({
            ...res.commodityInfoRes,
            commodityId: commodityId,
            groupsNum: String(res.commodityInfoRes.groupsNum),
            commodityCategory: String(res.commodityInfoRes.commodityCategory),
            mainProductNum: String(res.commodityInfoRes.mainOrderType),
            paySrcprdId: res.commodityInfoRes.paySrcProdId,
            // channelId: data.record.channelId
          });
        }

        if (res.commodityInfoRes.groupsNum > 0 && res.commodityInfoRes.groupsNum <= 5) {
          groupShow.value = true;
        }
      }
      if (res.productInfoEntity) {
        productFormActionType.setFieldsValue({
          ...res.productInfoEntity,
          productRenewType: res.commodityInfoRes.renewType + '',
          productOrderNum: res.commodityInfoRes.mainOrderNum,
          isActive: res.commodityInfoRes.isActive,
          isPresend: res.commodityInfoRes.isPresent,
          sendType: res.commodityInfoRes.sendType,
          productOrderType: res.commodityInfoRes.mainOrderType,
        });
      }

      console.log(typeof String(res.commodityInfoRes.mainOrderType), 'res.commodityInfoRes.mainOrderType');
      if (res.commodityGroupInfoEntityList) {
        const GroupInfoEntityList = res.commodityGroupInfoEntityList.map((group) => ({
          ...group,
          orderType: group.orderType.toString(),
          renewType: group.renewType.toString(),
        }));
        formState.groups = GroupInfoEntityList;
      }
    }
  }
  function getOption() {
    getDicOptions('service_cat').then((res) => {
      service_Cat = res;
    });
    getDicOptions('common_status').then((res) => {
      common_status = res;
    });
    getDicOptions('renewal_order_type').then((res) => {
      renewal_order_type = res;
    });

    // getDicOptions('carrier').then(res=>{
    //   carrier = res
    // })
  }
  function radioClick(e) {
    console.log(e.target.value, 'e');
    let value = e.target.value;
    if (value == 1) {
      showMainProduct.value = true;
    } else {
      formActionType.updateSchema({
        field: 'productId',
        required: false,
      });
      showMainProduct.value = false;
    }
  }
  async function customResetFunc() {
    console.log('这是里面的重置按钮');
    // mainProductFormActionType.clearValidate();
    productOptions.value = [];
    productData.value = [];
    await productFormActionType.setFieldsValue({
      productId: undefined,
      price: undefined,
    });
  }
  async function seachclick() {
    const values = await mainProductFormActionType.validate();
    console.log(values);
    let res = await getQueryProduct(values);
    console.log(res, '000');
    if (res && res.length !== 0) {
      productOptions.value = res;
      await productFormActionType.setFieldsValue({
        productId: res[0].productId,
        price: res[0].price,
      });
      productData.value = res.map((item) => {
        return {
          key: String(item.productId),
          title: item.productName,
          description: item.productCode,
          productType: item.productType,
          serviceCat: item.serviceCat,
          renewalOrderType: item.renewalOrderType,
          flowCycle: item.flowCycle,
          status: item.status,
        };
      });
    } else {
      productOptions.value = [];
      productData.value = [];
      await productFormActionType.setFieldsValue({
        productId: undefined,
        price: undefined,
      });
    }
  }
  //表单动态获取
  async function inputchange(e) {
    console.log(e.data, 'e');
    let num = e.data;
    groupNum.value = e.data;
    if (num > 0 && num <= 5) {
      groupShow.value = true;
      if (num > formState.groups.length) {
        // 需要添加更多小组
        for (let i = formState.groups.length; i < num; i++) {
          let res = await getGroupId({});
          formState.groups.push(createGroup(res));
        }
      } else if (num < formState.groups.length) {
        // 需要减少小组
        formState.groups = formState.groups.slice(0, num);
      }
    } else {
      groupShow.value = false;
    }
  }
  // 表单状态
  const createGroup = (index) => ({
    groupId: index, // 生成唯一ID
    orderType: '1', // 默认首订方式
    renewType: 1, // 默认续订方式
    groupName: '', // 小组名称
    optionNum: '', // 可选产品数
    orderNum: '', // 订购次数
  });
  function configGroupProducts(groupIndex) {
    getOption();
    productShow.value = true;
    targetKeys.value = dataSource.value.map((item: any) => item.productId);
    selectedKeys.value = [];
    console.log('配置小组', groupIndex + 1, formState.groups[groupIndex]);
    groupForm.value = formState.groups[groupIndex];
    groupIndexNum = groupIndex + 1;
  }
  // 穿梭框选择
  const handleChange = (nextTargetKeys: string[], direction: string, moveKeys: string[]) => {
    // 获取所有已选中的完整产品
    const selectedProducts = getSelectedProducts();
    console.log('当前已选中的所有产品:', targetKeys.value, selectedKeys.value, selectedProducts);
    dataSource.value = selectedProducts.map((item) => {
      return {
        productId: item.key,
        productName: item.title,
        productCode: item.description,
        productType: item.productType,
        serviceCat: item.serviceCat,
        flowCycle: item.flowCycle,
        renewalOrderType: item.renewalOrderType,
        status: item.status,
      };
    });
    productsCode.value = selectedProducts.map((item) => item.description).join(',');
    productsId.value = selectedProducts.map((item) => item.key).join(',');
    console.log(productsCode.value, 'dataSource.value', productsId.value);
  };
  const handleSelectChange = (sourceSelectedKeys: string[], targetSelectedKeys: string[]) => {
    console.log('sourceSelectedKeys: ', sourceSelectedKeys);
    console.log('targetSelectedKeys: ', targetSelectedKeys);
  };
  const getSelectedProducts = () => {
    return productData.value.filter((item) => targetKeys.value.includes(item.key));
  };
  function handleOk() {
    if (dataSource.value.length == 0) {
      createMessage.error('请选择产品!');
    } else if (dataSource.value.length < Number(groupForm.value.optionNum)) {
      createMessage.error('小组' + groupIndexNum + '内产品数必须大于小组可选产品数!');
    } else {
      const currentGroupId = groupForm.value.groupId; // 假设产品对象中有一个groupId属性
      const targetGroupIndex = formState.groups.findIndex((group) => group.groupId === currentGroupId);
      if (targetGroupIndex !== -1) {
        // 4. 创建新的groups数组（保持不可变性）
        const updatedGroups = [...formState.groups];
        console.log(updatedGroups, 'updatedGroups');
        // 5. 更新目标组的产品信息
        updatedGroups[targetGroupIndex] = {
          ...updatedGroups[targetGroupIndex],
          productsCode: productsCode.value,
          productsId: productsId.value,
        };
        formState.groups = updatedGroups;
        console.log(formState.groups, '更新后的组信息:', updatedGroups[targetGroupIndex]);
      }
      productShow.value = false;
      mainProductFormActionType.clearValidate();
    }
    console.log(dataSource.value.length, 'dataSource.value.length');
  }
  function productChange(value) {
    console.log(value, 'value');
    const selectedProduct = productOptions.value.find((item) => item.productId === value);
    productFormActionType.setFieldsValue({
      price: selectedProduct?.price,
    });
  }
  async function getProduct() {
    let res = await getQueryProduct({});
    if (res) {
      productOptions.value = res;
    }
  }
  // 获取商品ID
  async function getCommotCode() {
    let res = await getCommodityCode({});
    if (res) {
      await formActionType.setFieldsValue({
        commodityCode: res,
      });
    }
  }
  function changeCategory(value) {
    console.log(value, 'value');
    showMainProduct.value = true;
    specsShow.value = false;
    if (value == '0') {
      formActionType.setFieldsValue({
        groupsNum: '0',
        // mainProductNum:'0'
      });
      groupShow.value = false;
      groupDisable.value = true;
    } else if (value == '1') {
      groupDisable.value = false;
    } else {
      specsShow.value = true;
      showMainProduct.value = false;
    }
  }
  //表单提交事件
  async function handleSubmit() {
    try {
      const values = await formActionType.validate();
      const mainProductValues = await productFormActionType.validate();
      if (values.price) {
        values.price = parseFloat(values.price);
      }
      if (mainProductValues && mainProductValues.price) {
        mainProductValues.price = parseFloat(mainProductValues.price);
      }
      if (values.commodityCategory == '0') {
        values.mainProductNum = '1';
      }
      if (values.commodityCategory == '1' && values.groupsNum > 0) {
        const { valid, message } = validateForm();
        if (!valid) {
          // 使用Ant Design的message提示错误
          createMessage.error(message);
          return;
        }
      }
      console.log(values, 'values');
      if (values.commodityCategory == '2') {
        if (!productDataSource.length) {
          createMessage.error('请先添加商品！');
          return;
        } else if (hasDuplicateSorts(productDataSource)) {
          createMessage.error('存在重复的商品排序值！');
          return;
        }
      }
      console.log(values, 'values');

      // 校验通过，继续提交逻辑
      console.log('表单数据:', formState.groups);
      setModalProps({ confirmLoading: true });
      let isUpdateVal = unref(isUpdate);
      //提交表单
      if (!isUpdateVal) {
        let params = {};
        if (values.commodityCategory == '2') {
          let _arr = productDataSource.sort((a, b) => a.sort - b.sort);
          params = {
            commodityInfoEntity: values,
            commodityGroupEntities: _arr.map((el, index) => {
              return {
                slaveCommodityId: el.commodityId,
                sort: index + 1,
              };
            }),
          };
        } else {
          params = {
            commodityInfoEntity: values,
            mainProduct: mainProductValues,
            commodityGroupInfoEntities: formState.groups,
          };
        }
        await addCommodity(params);
      } else {
        let params = {};
        if (values.commodityCategory == '2') {
          let _arr = productDataSource.sort((a, b) => a.sort - b.sort);
          params = {
            commodityId: commodityId.value,
            commodityInfoEntity: values,
            commodityGroupEntities: _arr.map((el, index) => {
              return {
                slaveCommodityId: el.commodityId,
                sort: index + 1,
              };
            }),
          };
        } else {
          params = {
            commodityId: commodityId.value,
            isActive: mainProductValues.isActive,
            isPresend: mainProductValues.isPresend,
            sendType: mainProductValues.sendType,
            remark: values.remark,
          };
        }
        await updateCommodity(params);
      }

      //关闭弹窗
      closeModal();
      createMessage.success('操作成功');
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
  async function handleBeforeClose(): Promise<boolean> {
    return await new Promise<boolean>((resolve) => {
      formState = Object.assign(formState, { groups: [] });
      groupShow.value = false;
      resolve(true);
    });
  }

  const hasDuplicateSorts = (data) => {
    const sortValues = data.map((item) => item.sort);
    return new Set(sortValues).size !== sortValues.length;
  };

  // 已添加的商品列表
  const addedItems = ref<any[]>([]);

  // 已添加的商品ID列表（用于排除已添加的商品）
  const addedValues = computed(() => addedItems.value.map((item) => item.commodityId));

  // 添加商品处理
  const handleAddItem = (item: any) => {
    // 检查是否已存在
    const exists = addedItems.value.some((existing) => existing.commodityId === item.commodityId);
    if (!exists) {
      addedItems.value = addedItems.value.sort((a, b) => a.sort - b.sort);
      addedItems.value = [...addedItems.value, item].map((el, i) => {
        el.sort = i + 1;
        return el;
      });
      updateProductDataSource();
    }
  };

  // 删除商品处理
  const removeItem = (commodityId: number) => {
    addedItems.value = addedItems.value.filter((item) => item.commodityId !== commodityId);
    updateProductDataSource();
  };

  // 更新表格数据源
  const updateProductDataSource = () => {
    productDataSource.length = 0;
    productDataSource.push(...addedItems.value);
  };
</script>
<style lang="less" scoped>
  .upload-tip {
    margin-top: 8px;
    color: #999;
    font-size: 12px;
  }
</style>
