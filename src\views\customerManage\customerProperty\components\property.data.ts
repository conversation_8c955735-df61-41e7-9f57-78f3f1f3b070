import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getAllRolesListNoByTenant } from '/@/api/sys/user';
import { rules } from '/@/utils/helper/validator';
export let adjustment_sub_type = []
window.$getDicOptions('adjustment_sub_type').then((data)=>{
  adjustment_sub_type  = data
})
export const columns: BasicColumn[] = [
  {
    title: '账户ID',
    dataIndex: 'channelId',
    width: 120,
  },
  {
    title: '集团客户编码',
    dataIndex: 'groupId',
    width: 100,
  },
  {
    title: '集团客户名称',
    width: 150,
    dataIndex: 'channelName',
  },
  {
    title: '资金余额',
    dataIndex: 'currBalance',
    width: 100,
   // defaultSortOrder: 'descend',
    sorter: (a: TableDataType, b: TableDataType) => a.currBalance - b.currBalance,
  },
  {
    title: '锁定资金',
    dataIndex: 'lockedMoney',
    width: 100
  },
  {
    title: '累计充值金额',
    dataIndex: 'accuMoney',
    width: 100,
    sorter: (a: TableDataType, b: TableDataType) => a.accuMoney - b.accuMoney,
  },
  {
    title: '累计订购金额',
    width: 150,
    dataIndex: 'orderedMoney',
    sorter: (a: TableDataType, b: TableDataType) => a.orderedMoney - b.orderedMoney,
  },
  
];
// 查询
export const searchFormSchema: FormSchema[] = [
  {
    label: '账户ID',
    field: 'channelId',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 20,
    }
  },
  {
    label: '集团客户编码',
    field: 'groupId',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 20,
    }
  },
  {
    label: '集团客户名称',
    field: 'channelName',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 20,
    }
  }
];


export const formAnnountSchema: FormSchema[] = [
  {
    label: '集团客户名称',
    field: 'channelName',
    component: 'Input',
    componentProps: {
      disabled:true
    }
  },
  {
    label: '资金余额',
    field: 'currBalance',
    component: 'Input',
    componentProps: {
      disabled:true
    }
  },
  {
    label: '调账类型',
    field: 'mainType',
    component: 'RadioGroup',
    componentProps: {
      //options里面由一个一个的radio组成,支持disabled
      options: [
        { label: '增加资金金额', value: 2 },
        { label: '减少资金金额', value: 1},
      ],
    },
    required: true
  },
  {
    label: '调账金额',
    field: 'adjustmentAmt',
    component: 'Input',
    suffix: '元',
    dynamicRules: ({ values }) => {
      console.log('values:', values);
      //需要return
      return [
        {
          //默认开启表单检验
          required: true,
          // value 当前手机号输入的值
          validator: (_, value) => {
            //需要return 一个Promise对象
            return new Promise((resolve, reject) => {
              if (!value) {
                reject('请输入调账金额');
              }else if(!/^\d+(\.\d{1,2})?$/.test(value)){
                reject('金额格式不正确，请输入数字并最多保留两位小数!');
              }else if(parseFloat(value) > 100000){
                reject('金额不能超过100,000元');
              }
              resolve();
            });
          },
        },
      ];
    }
  },
  {
    label: '调账原因',
    field: 'subtype',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'adjustment_sub_type',
      placeholder: '请选择调账原因',
      stringToNumber: true,
    },
    required: true
  },
  {
    label: '补充说明',
    field: 'remark',
    component: 'InputTextArea',
    ifShow: ({ values }) => {
      return values.subtype == '4';
    },
    required: ({ values }) => {
      return values.subtype == '4' ? true: false;
    },
  },
    {
      label: '调账凭证',
      field: 'adjustmentVoucherFile',
      component: 'Input',
      slot: 'adjustVoucherFileSlot',
      required: true
    }
];
export const adustColumns: BasicColumn[] = [
  {
    title: '调账时间',
    dataIndex: 'createTime',
    width: 120,
  },
  {
    title: '调账原因',
    dataIndex: 'subtype',
    width: 100,
    customRender: ({ text }) => {
      let result = adjustment_sub_type.find((item)=>item.value === text) || {}
      return result.label || text;
    },
  },
  {
    title: '调账前资金余额',
    width: 150,
    dataIndex: 'preMoneyStr',
  },
  {
    title: '调账金额',
    dataIndex: 'adjustmentAmtStr',
    width: 100,
  },
  {
    title: '调账类型',
    dataIndex: 'mainType',
    width: 100,
    customRender: ({ text }) => {
      return text == 1 ? '减少资金金额': '增加资金金额';
    },
  },
  {
    title: '调账凭证',
    dataIndex: 'adjustmentVoucherFileName',
    width: 150,
    slots: { customRender: 'adjustmentVoucherFileName' },
  },
  {
    title: '操作账号',
    dataIndex: 'createBy',
    width: 100,
  }
];
// 查询
export const searchRecordFormSchema: FormSchema[] = [
  {
    label: '调账时间',
    field: 'createTime',
    component: 'RangePicker',
    colProps: { span: 10 },
    componentProps: {
      //是否显示时间
    //  showTime: true,
      //日期格式化
      valueFormat:"YYYY-MM-DD",
      //范围文本描述用集合
      placeholder:['请选择开始日期时间','请选择结束日期时间']
    },
  },
  {
    label: '调账原因',
    field: 'subtype',
    colProps: { span: 8 },
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'adjustment_sub_type',
      placeholder: '请选择调账原因',
      stringToNumber: true,
    },
  },
];
