import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import dayjs from 'dayjs';

export const searchFormSchema: FormSchema[] = [
  {
    label: '账户ID',
    field: 'channelId',
    component: 'Input',
    colProps: { span: 6 },
    // componentProps: {
    //   maxLength: 20,
    // },
  },
  {
    label: '集团客户名称',
    field: 'channelName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '商品ID',
    field: 'commodityCode',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '商品名称',
    field: 'commodityName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '运营商',
    field: 'mainCarrier',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'carrier',
      placeholder: '请选择运营商',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '主产品分类',
    field: 'mainProductType',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
  },
  {
    label: '商品类别',
    field: 'commodityCategory',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'product_order_inquiry_category',
      placeholder: '请选择商品类别',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '主产品类型',
    field: 'mainProductServiceCat',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'service_cat',
      placeholder: '请选择主产品类型',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '流量/语音/短信值',
    field: 'flowValue',
    component: 'Input',
    colProps: { span: 6 },
  },
];

export const columns: BasicColumn[] = [
  {
    title: '账户ID',
    dataIndex: 'channelId',
  },
  {
    title: '集团客户名称',
    // dataIndex: 'channelName',
    dataIndex: 'name',
  },
  {
    title: '商品ID',
    dataIndex: 'commodityCode',
  },
  {
    title: '商品名称',
    dataIndex: 'commodityName',
  },
  {
    title: '订购折扣',
    dataIndex: 'discountRateStr',
  },
  {
    title: '流量/短信/语音值',
    dataIndex: 'flowValue',
  },
  {
    title: '运营商',
    dataIndex: 'mainCarrierStr',
  },
  {
    title: '主产品类型',
    dataIndex: 'mainProductServiceCatStr',
  },
  {
    title: '日限(单)',
    dataIndex: 'dailyLimitStr',
  },
  {
    title: '月限(单)',
    dataIndex: 'monthlyLimitStr',
  },
  {
    title: '总单(单)',
    dataIndex: 'totalLimitStr',
  },
  {
    title: '商品状态',
    dataIndex: 'statusStr',
  },
];

export const formAddSchema: FormSchema[] = [
  {
    label: '手机号码',
    field: 'msisdn',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    dynamicRules: () => {
      return [{ required: true, pattern: /^1[3456789]\d{9}$/, message: '手机号码格式有误', trigger: 'blur' }];
    },
    componentProps: {
      maxLength: 11,
    },
  },
  {
    label: '姓名',
    field: 'name',
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      maxLength: 10,
    },
  },
  {
    label: '风控原因',
    field: 'reason',
    required: true,
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      maxLength: 20,
    },
  },
];

export const formImportSchema: FormSchema[] = [
  {
    label: '批量导入',
    field: 'file',
    component: 'JUpload',
    slot: 'fileSlot',
    componentProps: {
      //是否显示选择按钮
      text: '文件上传',
      //最大上传数
      maxCount: 2,
      //是否显示下载按钮
      download: true,
      promptText: '仅支持上传xlsx格式文件',
    },
    dynamicRules: ({ values }) => {
      //需要return
      return [
        {
          //默认开启表单检验
          required: true,
          validator: (_, value) => {
            //需要return 一个Promise对象
            return new Promise((resolve, reject) => {
              if (!value) {
                reject('请上传正确格式/大小的文件！');
              }
              resolve();
            });
          },
        },
      ];
    },
    colProps: { span: 24 },
  },
];

export const previewColumns: BasicColumn[] = [
  {
    title: '账户ID',
    dataIndex: 'channelId',
  },
  {
    title: '商品编码',
    dataIndex: 'commodityCode',
  },
  {
    title: '折扣率',
    dataIndex: 'discountRate',
  },
];
