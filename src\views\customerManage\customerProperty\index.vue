<template>
  <PageWrapper>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button 
         type="primary" 
         v-auth="'customer:property:export'"  
         preIcon="ant-design:export-outlined" @click="exportClick">导出</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <propertyModal @register="registerPropertyModal" @success="handleSuccess" />
    <adjustModel @register="registerAdjustModel" @success="handleSuccess" />
  </PageWrapper>
</template>
<script lang="ts" name="system-user" setup>
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import propertyModal from './components/propertyModal.vue';
  import adjustModel from './components/adjustModel.vue';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { columns, searchFormSchema } from './components/property.data';
  import { getQueryChannelList, getChannelExport  } from '/@/api/property/property';
  import { exportExcel } from '/@/utils/common/compUtils';
  import {ref,unref} from "vue"
  //const searchParams = ref({})
  let searchParams = {}
  const { createMessage } = useMessage();
  //model
  const [registerPropertyModal, { openModal }] = useModal();
  const [registerAdjustModel, { openModal: openAdjustModal }] = useModal();
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'annount-list',
    tableProps: {
      title: '资产列表',
      api: getQueryChannelList,
      rowSelection: {},
      columns: columns,
      size: 'small',
      // showIndexColumn: true,
      formConfig: {
        showAdvancedButton: false,
        labelWidth: 100,
        schemas: searchFormSchema,
        actionColOptions: {
          xs: 24, // <576px
          sm: 24, // ≥576px
          md: 24, // ≥768px
          lg: 24, // ≥992px
          xl: 24, // ≥1200px
          xxl: 24,
          style: { textAlign: 'right' },
        },
      },
      actionColumn: {
        width: 120,
      },
      beforeFetch: (params) => {
        delete params.column;
        //delete params.order;
        console.log(params,'1233')
        searchParams = params
        // return Object.assign({ column: 'createTime', order: 'desc' }, params);
      },
    }
  });

  //注册table数据
  const [registerTable, { reload }] = tableContext;
  // 新增事件
  function handleCreate(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: false,
      showFooter: true,
    });
  }
  //编辑事件
  async function handleEdit(record: Recordable) {
    openAdjustModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  // 成功回调
  function handleSuccess() {
    reload();
  }
 
  // 操作栏
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '人工调账',
        onClick: handleCreate.bind(null, record),
        auth: 'customer:property:adjust'
      },
      {
        label: '调账记录',
        onClick: handleEdit.bind(null, record),
        auth: 'customer:property:record'
      }
    ];
  }
  async function exportClick(){
     console.log(searchParams,'searchParams')
     try {
        const res = await getChannelExport(searchParams);
        exportExcel(res)
        createMessage.success('导出成功');
      } catch (error: any) {
        createMessage.error('导出失败：' + error.message);
      }
  }
</script>
