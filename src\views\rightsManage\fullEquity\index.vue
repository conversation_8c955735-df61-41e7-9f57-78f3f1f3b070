<template>
  <PageWrapper>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button v-auth="'rightsManage:fullEquity:batchShelves'" @click="handleEnable({}, 1)"> 批量上架</a-button>
        <a-button v-auth="'rightsManage:fullEquity:batchUnShelves'" @click="handleEnable({}, 0)"> 批量下架</a-button>
        <!-- <a-button type="primary" v-auth="'integrated:black:importTemp'" @click="downTemplate()"> 批量新增模板下载</a-button>
        <a-button type="primary" v-auth="'integrated:black:batchImport'" @click="importBatch()"> 批量新增</a-button> -->
        <a-button type="primary" v-auth="'rightsManage:fullEquity:sort'" @click="handleCreate()"> C端商品排序模式设置</a-button>
        <a-button type="primary" v-auth="'rightsManage:fullEquity:add'" @click="handleEdit({})"> 新增</a-button>
      </template>
      <!-- <template #status="{ text }">
        <a-tag :color="+text === 0 ? 'error' : 'success'">{{ +text === 0 ? '已停用' : '启用中' }}</a-tag>
      </template> -->
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 验收弹窗 -->
    <handleModal @register="registerCheckModal" @success="handleSuccess" />
    <equityEdit @register="registerPropertyModal" @success="handleSuccess" />
  </PageWrapper>
</template>

<script lang="ts" name="system-banner" setup>
  import { reactive } from 'vue';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { fullEquityList, fullEquityStatus } from '/@/api/productconfig/full-equity';
  import { columns, searchFormSchema } from './fullEquity.data';
  import { Modal } from 'ant-design-vue';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import equityEdit from './equityEdit.vue';
  import handleModal from './handleModal.vue';
  import { exportExcel } from '/@/utils/common/compUtils';
  const [registerCheckModal, { openModal: openSortModal }] = useModal();
  const [registerPropertyModal, { openModal }] = useModal();
  const { createMessage, createConfirm } = useMessage();
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'banner-list',
    tableProps: {
      api: fullEquityList,
      columns: columns,
      size: 'small',
      showIndexColumn: false,
      // useSearchForm: false, // 是否使用搜索项
      rowSelection: { type: 'checkbox' }, //默认是 checkbox 多选，可以设置成 radio 单选
      formConfig: {
        showAdvancedButton: false,
        labelWidth: 100,
        schemas: searchFormSchema,
        actionColOptions: {
          xs: 24, // <576px
          sm: 24, // ≥576px
          md: 24, // ≥768px
          lg: 24, // ≥992px
          xl: 24, // ≥1200px
          xxl: 24,
          style: { textAlign: 'right' },
        },
      },
      actionColumn: {
        width: 100,
      },
      beforeFetch: (params) => {
        delete params.column;
        delete params.order;
        // return Object.assign({ }, params);
      },
    },
  });

  //注册table数据
  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  //新增
  let recordData = reactive({});
  function handleCreate() {
    openSortModal(true, {
      isAdd: 1,
      isUpdate: true,
      showFooter: true,
    });
  }
  //上下架
  async function handleEnable(record, status) {
    Modal.confirm({
      title: `确定要${record.id ? '' : '批量'}${status === 1 ? '上架' : '下架'}权益吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        let params = {
          ids: record.id ? record.id + ',' : selectedRowKeys.value.join(','),
          status,
        };
        fullEquityStatus(params).then((res) => {
          console.log(res, '===>res');
          selectedRowKeys.value = [];
          createMessage.success('操作成功');
          reload();
        });
      },
    });
  }
  //编辑权益
  function handleEdit(record) {
    openModal(true, {
      record,
    });
  }
  // 批量导入
  function importBatch() {
    // openModal(true, {
    //   isAdd: 0,
    //   isUpdate: true,
    //   showFooter: true,
    // });
  }

  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '下架',
        onClick: handleEnable.bind(null, record, 0),
        auth: 'rightsManage:fullEquity:unShelves',
        ifShow: +record.status === 1,
      },
      {
        label: '上架',
        onClick: handleEnable.bind(null, record, 1),
        auth: 'rightsManage:fullEquity:shelves',
        ifShow: +record.status === 0,
      },
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'rightsManage:fullEquity:edit',
      },
    ];
  }
  function handleSuccess() {
    reload();
  }
</script>
