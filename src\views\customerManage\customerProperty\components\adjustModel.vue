<template>
  <BasicModal 
    v-bind="$attrs" 
    title="调账记录" 
    width="800px"
    :defaultFullscreen="true"
    destroyOnClose 
    @register="registerModal" 
    :showOkBtn="false">
    <BasicTable  @register="registerTable">
      <template #adjustmentVoucherFileName="{ record }">
         <a-button type="link" @click="showImg(record)"><span style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;word-break: keep-all;width:200px" :title="record.adjustmentVoucherFileName">{{record.adjustmentVoucherFileName}}</span></a-button>
         <a-image
          :style="{ display: 'none' }"
          :preview="{
            visible,
            onVisibleChange: setVisible,
          }"
          :src="imgSrc"
        />
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, unref, toRaw } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable, TableRowSelection, useTable } from '/@/components/Table';
  import { adustColumns, searchRecordFormSchema } from './property.data';
  import { getAdjustList, getfileDownload } from '/@/api/property/property';

  import { useListPage } from '/@/hooks/system/useListPage';
  const channelId = ref('');
  const imgSrc = ref('')
  const visible = ref<boolean>(false);
   const setVisible = (value): void => {
      visible.value = value;
    };
  // 声明Emits
  const emit = defineEmits(['select', 'register']);
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    channelId.value = data.record.channelId
    // setProps({ 
    //   searchInfo: { channelId: unref(channelId), } 
    //   });
    //  console.log(setProps,'searchInfo')
    reload();
  });
  //注册table数据
  const [registerTable, { reload, setProps }] = useTable({
    //需要配置rowKey，否则会有警告
    rowKey: 'channelId',
    api: (params) => {
      let otherParams = {
        startTime:'',
        endTime:''
      }
      console.log(params,'params')
      let createTimes = params.createTime && params.createTime.includes(',') ?  params.createTime.split(',') : ''
      if(createTimes.length > 0){
          let startTime = createTimes[0]
          let endTime = createTimes[1]
          otherParams.startTime = startTime
          otherParams.endTime = endTime
      }
     return getAdjustList({
        ...otherParams,
        subtype: params.subtype,
        channelId: unref(channelId),
        pageNo: params.pageNo,
        pageSize: params.pageSize

     });
   },
    columns: adustColumns,
    formConfig: {
      labelWidth: 100,
      baseRowStyle: { maxHeight: '20px' },
      labelAlign: 'right',
      labelCol: {
        offset: 1,
        xs: 24,
        sm: 24,
        md: 24,
        lg: 9,
        xl: 7,
        xxl: 4,
      },
      wrapperCol: {},
      schemas: searchRecordFormSchema,
      autoSubmitOnEnter: true,
    },
    striped: true,
    useSearchForm: true,
    bordered: true,
    showIndexColumn: false,
    canResize: false,
    immediate: false,
  });
  // 列表页面公共参数、方法
  // const { tableContext } = useListPage({
  //   designScope: 'annount-list',
  //   tableProps: {
  //     title: '资产列表',
  //     api: getAdjustList,
  //     rowSelection: {},
  //     columns: adustColumns,
  //     size: 'small',
  //     // showIndexColumn: true,
  //     formConfig: {
  //       showAdvancedButton: false,
  //       labelWidth: 100,
  //       schemas: searchRecordFormSchema,
  //       actionColOptions: {
  //         xs: 24, // <576px
  //         sm: 24, // ≥576px
  //         md: 24, // ≥768px
  //         lg: 24, // ≥992px
  //         xl: 24, // ≥1200px
  //         xxl: 24,
  //         style: { textAlign: 'right' },
  //       },
  //     },
  //     actionColumn: {
  //       width: 120,
  //     },
  //     beforeFetch: (params) => {
  //       delete params.column;
  //       delete params.order;
  //       console.log(params,'1233')
  //    //   searchParams = params
  //       // return Object.assign({ column: 'createTime', order: 'desc' }, params);
  //     },
  //   }
  // });
  // //注册table数据
  // const [registerTable, { reload }] = tableContext;
  async function showImg(record){ 
    const res = await getfileDownload({fileId:record.adjustmentVoucherFile});
    imgSrc.value = res
    setVisible(true)
   
  }
</script>
