<template>
  <BasicModal
    v-bind="$attrs"
    :title="getTitle"
    :defaultFullscreen="true"
    width="800px"
    @register="registerModal"
    @ok="handleSubmit"
    :closeFunc="handleBeforeClose"
  >
    <a-card :title="productTitle" :bordered="true">
      <BasicForm @register="registerForm"></BasicForm>
    </a-card>
    <a-card :title="'规格设置'" :bordered="true" style="margin-top: 16px">
      <searchSelect></searchSelect>
    </a-card>
  </BasicModal>
</template>
<script lang="ts" name="newProductModal" setup>
  import { ref, computed, unref, watch, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formJsProductSchema } from './product.data';
  import { getCommodityCode, getQueryProduct, getGroupId, addCommodity, updateCommodity, commodityDetail } from '/@/api/productconfig/productconfig';
  import searchSelect from './searchSelect.vue';

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    console.log(closeModal);
    await formActionType.resetFields();
    isUpdate.value = !!data?.isUpdate;
    if (unref(isUpdate)) {
      // commodityId.value = data?.record?.commodityId;
    } else {
      getCommotCode();
    }
  });
  const isUpdate = ref(false);
  //设置标题
  const getTitle = computed(() => (!isUpdate.value ? '新增' : '编辑'));
  const productTitle = computed(() => (!unref(isUpdate) ? '新增商品' : '编辑商品'));
  //表单配置
  const [registerForm, formActionType] = useForm({
    schemas: formJsProductSchema,
    showActionButtonGroup: false,
    labelCol: { style: { width: '190px' } },
    labelAlign: 'right',
    //labelCol: { style: { width: '150px' } }
  });
  // 获取商品ID
  async function getCommotCode() {
    let res = await getCommodityCode({});
    if (res) {
      await formActionType.setFieldsValue({
        commodityCode: res,
      });
    }
  }
  function handleSubmit() {}
  async function handleBeforeClose(): Promise<boolean> {
    return await new Promise<boolean>((resolve) => {
      //关闭之前先处理一些事情
      //比如保存数据
      //如果保存成功则返回true否则返回false
      resolve(true);
    });
  }
</script>
<style lang="less" scoped></style>
