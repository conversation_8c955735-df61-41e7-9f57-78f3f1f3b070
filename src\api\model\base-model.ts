export interface BasicPageParams {
  page: number;
  pageSize: number;
}

export interface BasicFetchResult<T> {
  items: T[];
  total: number;
}

export interface BasicRecord<T> {
  records: T[];
  total: number;
}
export interface BasicResult<T> {
  code: number;
  result: T;
  message: string;
}
export interface BasicResultList<T> {
  records: T[];
  total: number;
}
export interface SelectOptionModel {
  label: string;
  value: string | number;
}
