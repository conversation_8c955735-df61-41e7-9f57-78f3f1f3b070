import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';

// 部门基础表单
export function useBasicFormSchema() {
  const basicFormSchema: FormSchema[] = [
    {
      field: 'departName',
      label: '租户名称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入一级租户名称,如浙江移动',
        maxLength: 50,
      },
      rules: [{ required: true, message: '请输入一级租户名称,如浙江移动' }],
    },
    {
      field: 'orgCategory',
      label: '租户类型',
      component: 'RadioGroup',
      defaultValue: '1',
      componentProps: {
        options: [
          { label: '公司', value: '1' },
          // { label: '部门', value: '2' },
        ],
      },
      rules: [{ required: true, message: '租户类型不能为空' }],
    },
    {
      field: 'orgCode',
      label: '租户编码',
      component: 'Input',
      componentProps: {
        placeholder: '请输入租户编码',
        maxLength: 50,
      },
      rules: [{ required: true, message: '请输入租户编码' }],
    },
    // {
    //   field: 'parentId',
    //   label: '上级组织',
    //   component: 'TreeSelect',
    //   componentProps: {
    //     treeData: [],
    //     dropdownStyle: { maxHeight: '200px', overflow: 'auto' },
    //   },
    // },
  ];
  return { basicFormSchema };
}

// 机构类型选项
export const orgCategoryOptions = {
  // 一级部门
  root: [{ value: '1', label: '公司' }],
  // 子级部门
  child: [
    { value: '2', label: '部门' },
    { value: '3', label: '岗位' },
  ],
};
export const columns: BasicColumn[] = [
  {
    title: '租户名称',
    dataIndex: 'departName',
    width: 200,
    align: 'center',
  },
  {
    title: '租户编码',
    dataIndex: 'orgCode',
    width: 200,
    align: 'center'
  },
  {
    title: '租户类型',
    dataIndex: 'orgCategory',
    width: 150,
    customRender: ({ text }) => {
      // orgCategory  1 公司  2 部门
      return text === '1' ? '公司' : '部门';
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 150,
    customRender: ({ text }) => {
      //@ts-ignore
      // orgCategory  1 启用  0 禁用
      return text === '1' ? '启用' : '禁用';
    },
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    field: 'roleName',
    label: '租户名称',
    component: 'Input',
    colProps: { span: 6 },
  },
];
