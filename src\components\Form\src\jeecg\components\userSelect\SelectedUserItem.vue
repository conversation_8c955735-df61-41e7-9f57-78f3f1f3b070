<template>
  <div class="user-selected-item">
    <div class="user-selected-item-box">
      <span class="avatar">
        <a-avatar v-if="info.avatar" :src="getFileAccessHttpUrl(info.avatar)" :size="24" />
        <a-avatar v-else-if="info.avatarIcon" class="ant-btn-primary" :size="24">
          <template #icon>
            <Icon class="font-16 mt-1" :icon="'ant-design:' + info.avatarIcon" />
          </template>
        </a-avatar>
        <a-avatar v-else-if="info.selectType === 'sys_role'" class="bg-role" :size="24">
          <template #icon>
            <team-outlined class="font-16" />
          </template>
        </a-avatar>
        <a-avatar v-else-if="info.selectType === 'sys_position'" class="bg-position" :size="24">
          <template #icon>
            <TagsOutlined class="font-16" />
          </template>
        </a-avatar>
        <a-avatar v-else :size="24">
          <template #icon><UserOutlined /></template>
        </a-avatar>
      </span>
      <div class="ellipsis realname">
        {{ info.realname || info.name }}
      </div>
      <div v-if="showClose" class="icon-close">
        <CloseOutlined @click="removeSelect" />
      </div>
    </div>
    <div v-if="!showClose" class="icon-remove">
      <MinusCircleFilled @click="removeSelect" />
    </div>
  </div>
</template>
<script>
  import { UserOutlined, CloseOutlined, MinusCircleFilled, TagsOutlined, TeamOutlined } from '@ant-design/icons-vue';
  import { computed } from 'vue';
  import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';
  export default {
    name: 'SelectedUserItem',
    components: {
      UserOutlined,
      MinusCircleFilled,
      CloseOutlined,
      TagsOutlined,
      TeamOutlined,
    },
    props: {
      info: {
        type: Object,
        default: () => {},
      },
      // 是否作为查询条件
      query: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['unSelect'],
    setup(props, { emit }) {
      function removeSelect(e) {
        e.preventDefault();
        e.stopPropagation();
        emit('unSelect', props.info.id);
      }
      const showClose = computed(() => {
        if (props.query === true) {
          return true;
        } else {
          return false;
        }
      });
      return {
        showClose,
        removeSelect,
        getFileAccessHttpUrl,
      };
    },
  };
</script>

<style lang="less">
  .font-16 {
    font-size: 16px;
  }
  .bg-role {
    background-color: rgb(255, 173, 0);
  }
  .bg-position {
    background-color: rgb(245, 34, 45);
  }
  .user-selected-item-box {
    display: flex;
    flex-direction: row;
    height: 24px;
    border-radius: 12px;
    padding-right: 10px;
    vertical-align: middle;
    background-color: #f5f5f5;
    .avatar {
      width: 24px;
      height: 24px;
      line-height: 20px;
      margin-right: 3px;
      display: inline-block;
    }
  }
  .user-selected-item {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-right: 8px;
    height: 30px;
    border-radius: 12px;
    line-height: 30px;
    vertical-align: middle;
    .realname {
      height: 24px;
      line-height: 24px;
    }
    .ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .icon-remove {
      position: absolute;
      top: -10px;
      right: -4px;
      font-size: 18px;
      width: 15px;
      height: 15px;
      cursor: pointer;
      display: none;
    }

    .icon-close {
      font:
        normal 10px/24px arial,
        verdana;
      margin-left: 7px;
      &:hover {
        color: #0a8fe9;
      }
    }

    &:hover {
      .icon-remove {
        display: block;
      }
    }
  }
</style>
