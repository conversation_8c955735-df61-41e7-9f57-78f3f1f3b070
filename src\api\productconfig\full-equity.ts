import { defHttp } from '/@/utils/http/axios';

enum Api {
  fullEquityList = '/biz/equityConfigInfo/list',
  fullEquityStatus = '/biz/equityConfigInfo/status',
  fullEquityEdit = '/biz/equityConfigInfo/edit',
  fullEquityQuery = '/biz/equityConfigInfo/queryGoodsSortMode',
  fullEquitySet = '/biz/equityConfigInfo/setGoodsSortMode',
  fullEquityAdd = '/biz/equityConfigInfo/add',
  getConfigRights = '/biz/equityConfigInfo/getConfigRights',
}

// export const getDictCheckList = (params) => {
//   return defHttp.post({ url: Api.dictCheckList, params });
// };

export const fullEquityList = (params) => {
  return defHttp.post({ url: Api.fullEquityList, params });
};

export const fullEquityStatus = (params) => {
  return defHttp.get({ url: Api.fullEquityStatus, params });
};

export const fullEquityEdit = (params) => {
  return defHttp.post({ url: Api.fullEquityEdit, params });
};

export const fullEquityQuery = (params) => {
  return defHttp.get({ url: Api.fullEquityQuery, params });
};

export const fullEquitySet = (params) => {
  return defHttp.post({ url: Api.fullEquitySet, params });
};

export const fullEquityAdd = (params) => {
  return defHttp.post({ url: Api.fullEquityAdd, params });
};

export const getConfigRights = (params) => {
  return defHttp.get({ url: Api.getConfigRights, params });
};
// export const srcOrderExcel = (params = {}) =>
//   defHttp.get(
//     {
//       url: Api.srcOrderExcel,
//       params,
//       responseType: 'blob', // 设置响应类型为blob
//     },
//     {
//       isReturnNativeResponse: true, // 返回原始响应以获取headers
//     }
//   );
