# 是否打开mock
VITE_USE_MOCK = false

# 发布路径
VITE_PUBLIC_PATH = /

# 跨域代理，您可以配置多个 ,请注意，没有换行符
# VITE_PROXY = [["/ncs","http://10.8.17.61:32036/ncs/"]]
#VITE_PROXY = [["/ncs","http://10.8.82.101:20003/ncs/"]]
# VITE_PROXY = [["/ncs","http://10.8.81.15:20003/ncs/"]]
# VITE_PROXY = [["/zqts/file","http://172.18.12.148:20002/zqts/file/"],["/zqts","http://172.18.12.148:20003/zqts/"]]
# VITE_PROXY = [["/zqts/file","http://10.8.81.15:20002/zqts/file/"],["/zqts","http://10.8.81.15:20003/zqts/"]]
#VITE_PROXY = [["/zqqy-manager/file","http://10.27.18.192:8899/zqqy-manager/file/"],["/zqqy-manager","http://10.27.18.192:8899/zqqy-manager/"]]
VITE_PROXY = [["/zqqy-manager/file","http://10.8.17.27:8899/zqqy-manager/file/"],["/zqqy-manager","http://10.8.18.159:8888/zqqy-manager/"]]
#VITE_PROXY = [["/zqqy-manager/file","http://10.27.18.192:8899/zqqy-manager/file/"],["/zqqy-manager","http://10.27.18.12:8899/zqqy-manager/"]]
#VITE_PROXY = [["/zqqy-manager/file","http://10.27.18.192:8899/zqqy-manager/file/"],["/zqqy-manager","http://************:8899/zqqy-manager/"]]
# VITE_PROXY = [["/ncs","http://***********:20003/ncs/"]]

#后台接口全路径地址(必填)
VITE_GLOB_DOMAIN_URL=/
#后台接口父地址(必填)
# VITE_GLOB_API_URL=/ncs
VITE_GLOB_API_URL=/zqqy-manager
# 接口前缀
VITE_GLOB_API_URL_PREFIX=
VITE_GLOB_APP_NEED_CRYPTO=false

#微前端qiankun应用,命名必须以VITE_APP_SUB_开头,jeecg-app-1为子应用的项目名称,也是子应用的路由父路径
# VITE_APP_SUB_jeecg-app-1 = '//localhost:8092'
