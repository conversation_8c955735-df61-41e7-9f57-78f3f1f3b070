<template>
  <BasicModal 
  v-bind="$attrs" 
  :title="getTitle" 
  width="950px"
  :showOkBtn="showFooter"
  @register="registerModal" 
  @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" name="PassWordModal" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formAnnountSchema } from './annount.data';
  import { annountAdd } from '/@/api/annount/annount';
  import dayjs from 'dayjs';
  const isUpdate = ref(false);
  const rowId = ref(undefined);
  const startTime = ref('');
  const endTime = ref('');
  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  //表单配置
  const [registerForm, formActionType] = useForm({
    schemas: formAnnountSchema,
    showActionButtonGroup: false,
  });
  const showFooter = ref(true);
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await formActionType.resetFields();
    showFooter.value = data?.showFooter ?? true;
    setModalProps({ confirmLoading: false, showOkBtn: showFooter.value });
    isUpdate.value = !!data?.isUpdate;
    if (unref(isUpdate)) {
      rowId.value = data?.record?.id;
      //表单赋值
      // await setFieldsValue({
      //   ...data.record,
      // });
    }else{
       rowId.value = undefined;
    }
    console.log(data.record,'record')
    
    if (unref(isUpdate)) {
      let startTime = data?.record?.startTime
      let endTime = data?.record?.endTime
      console.log(startTime,'startTime',endTime)
      formActionType.setFieldsValue({
       // ...data.record,
        title: data?.record?.title,
        announcementType: Number(data?.record?.announcementType),
        terminal:  Number(data?.record?.terminal),
        content: data?.record?.content,
        showtime: startTime+','+endTime
      });
    }
    formActionType.setProps({ disabled: !showFooter.value });
    formActionType.clearValidate();
  });
   //设置标题
 const getTitle = computed(() => (!unref(isUpdate) ? '新增公告' : unref(showFooter) ? '编辑公告' : '公告详情'));
  //表单提交事件
  async function handleSubmit() {
    try {
      const values = await formActionType.validate();
      console.log(values,'values')
      let createTimes = values.showtime && values.showtime.includes(',') ?  values.showtime.split(',') : ''
      console.log(createTimes)
      if(createTimes.length > 0){
           let startDate = createTimes[0]
           let endDate = createTimes[1]
           startTime.value = startDate
           endTime.value = endDate
      }
       // 构造提交数据，新增时不包含 id
      const submitData = {
        startTime: startTime.value,
        endTime: endTime.value,
        title: values.title,
        announcementType: values.announcementType,
        terminal: values.terminal,
        content: values.content
      };
      // // 编辑时才添加 id
      // if (isUpdate.value) {
      //   submitData.id = rowId.value
      // }
      console.log(values,'valueF')
      setModalProps({ confirmLoading: true });
      let isUpdateVal = unref(isUpdate);
      //提交表单
       await annountAdd({ ...submitData, id: unref(rowId)});
       //关闭弹窗
       closeModal();
      // //刷新列表
       emit('success', { isUpdateVal, submitData });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
