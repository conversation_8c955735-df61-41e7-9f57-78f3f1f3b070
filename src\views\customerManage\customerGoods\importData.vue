<template>
  <BasicModal v-bind="$attrs" :title="'订购折扣文件上传'" :defaultFullscreen="false" :width="600" @register="registerModal" @ok="handleSubmit">
    <div>
      <a-steps :current="current">
        <a-step v-for="(item, i) in stepsList" :key="i">
          <template #title>{{ item }}</template>
        </a-step>
      </a-steps>
      <div class="modal-content">
        <a-upload
          :before-upload="beforeUpload"
          accept=".xls,.xlsx"
          name="file"
          :max-count="1"
          :multiple="false"
          class="modal-content-upload"
          :customRequest="customUpload"
          v-if="current === 0"
        >
          <a-button type="primary"> <upload-outlined /> 选择文件 </a-button>
        </a-upload>
        <a-table :dataSource="dataSource" :columns="previewColumns" v-else />
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts">
  // 单独添加一个script块用于导出
  export default {
    name: 'orderManage-transferModel', // 保持与你原来的name一致
  };
</script>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { batchSave, blackAdd } from '/@/api/Integrated/black';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { UploadOutlined } from '@ant-design/icons-vue';
  import { Upload } from 'ant-design-vue';
  import type { UploadProps, UploadFile } from 'ant-design-vue';
  import { previewColumns } from './goods.data';
  import { on } from 'events';
  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  let modelType = ref(1);
  const [registerModal, { closeModal }] = useModalInner(async (data) => {
    modelType.value = data.isAdd;
  });
  const stepsList = ref(['上传文件', '预览数据', '导入数据']);
  const fileList = ref<UploadFile[]>([]);
  const uploading = ref(false);
  const current = ref(0);
  let dataSource = ref<any[]>([]);
  // 文件类型和大小校验
  const beforeUpload: UploadProps['beforeUpload'] = (file) => {
    const isXlsx = file.name.endsWith('.xlsx') || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    if (!isXlsx) {
      createMessage.error('只能上传XLSX格式的Excel文件!');
      return Upload.LIST_IGNORE; // 阻止文件进入列表
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      createMessage.error('文件大小不能超过10MB!');
      return Upload.LIST_IGNORE;
    }
    fileList.value = [file];
    return true; // 阻止自动上传
  };
  async function customUpload({ file, onSuccess, onError, onProgress }) {
    console.log(file, onSuccess, onError, onProgress);
    try {
      const res = await batchSave({ file });
      if (res.data?.code === 200) {
        createMessage.success('操作成功');
        onSuccess(file);
        closeModal();
        fileList.value = [];
        emit('success');
      } else {
        createMessage.warning(res.data?.message || '上传失败');
        onError(file);
      }
    } catch (error) {}
  }
  async function handleSubmit() {}
</script>
<style scoped lang="less">
  .ml8 {
    margin-left: 8px;
  }
  .tips {
    color: #999;
  }
  .custom-desc > div {
    margin-bottom: 8px;
    line-height: 16px;
  }

  .modal-content {
    width: 100%;
    margin-top: 24px;
    .modal-content-upload {
      margin: 0 auto;
    }
  }
</style>
