import { defHttp } from '/@/utils/http/axios';

enum Api {
  pageList = '/biz/ext/pageList',
  extLog = '/biz/ext/log',
  statusCheck = '/biz/ext/rechargeDl/pageList',
}
// 外部积分账号列表
export const postEtxList = (params) => {
  return defHttp.post({ url: Api.pageList, params });
};

// 外部积分账号充值明细
export const postRechargeList = (params) => {
  return defHttp.post({ url: Api.statusCheck, params });
};
// 明细查询
export const getEtxLog = (params) => {
  return defHttp.get({ url: Api.extLog, params });
};
