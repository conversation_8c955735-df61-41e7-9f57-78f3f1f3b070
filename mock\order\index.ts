import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, baseUrl } from '../_util';
import { OrderDetailModel, OrderItemModel } from '/@/api/order/model/OrderModel';
import { OrderLogModel } from '/@/api/order/model/OrderLogModel';
import API_URL from '@/api/url';
const orderList = (() => {
  const result: OrderItemModel[] = [];
  for (let index = 0; index < 20; index++) {
    result.push({
      id: '@guid',
      orderSource: '1',
      hsOrderId: '@guid',
      orgCode: '@guid', //所属组织id
      orgName: '中国移动', //所属组织name
      customerName: '@name', //客户名称
      uid: '@guid', //UID
      dyNum: '@guid', //抖音号
      dyNickname: '@guid', //	抖音昵称
      isRenew: 1, //	是否自动续费
      status: 2, //	订单状态（1-待提交 2-待接单 3-待开通 4-已开通 5-已过期 6-开通失败）
      hsServiceStartTime: '@date', //	服务开通时间	string
      hsServiceEndTime: '@date', //	服务结束时间	string
      createTime: '@date', //	创建时间	string
      operator: '@name', //	操作人
    });
  }
  return result;
})();
const orderLogs = (() => {
  const result: OrderLogModel[] = [];
  for (let index = 0; index < 20; index++) {
    result.push({
      id: '@guid',
      operator: '@cname',
      operateTime: '@date',
      operateAction: '创建订单',
      operateReason: '测试事实上事实上',
    });
  }
  return result;
})();
const orderDetail: OrderDetailModel = {
  orderAddVO: {
    id: 'idxxxx',
    status: 3,
    customerName: 'xxxx',
    customerMobile: '1236257233',
    customerEmail: '<EMAIL>',
    projectName: 'xasasasa',
    settleCode: '1',
    settleName: '河北移动',
    serviceEndMonth: '2024-12',
    isRenew: 1,
    uid: 'xxx-xxxx-xxxx',
    dyNum: 'ttt-sss-xxxx',
    dyNickname: '我是谁',
    province: '北京',
    price: 12132.22,
    source: '移动',
  },
  customerManagerVO: {
    name: '客户经理名称', //客户经理名称
    orgCode: '所属组织', //所属组织
    orgName: '中国移动',
    mobile: '客户经理电话', //客户经理电话
    email: 'email',
  },
  orderReportId: 'order-report-xxxx',
};

export default [
  {
    url: `${baseUrl}${API_URL.order.orderList}`,
    timeout: 200,
    method: 'post',
    response: () => {
      return resultSuccess({
        records: orderList,
        total: 100,
      });
    },
  },
  {
    url: `${baseUrl}${API_URL.order.orderLogs}`,
    timeout: 200,
    method: 'post',
    response: () => {
      return resultSuccess({
        records: orderLogs,
        total: 100,
      });
    },
  },
  {
    url: `${baseUrl}${API_URL.order.orderReport}`,
    timeout: 200,
    method: 'post',
    response: () => {
      return resultSuccess('http://gips3.baidu.com/it/u=1746086795,2510875842&fm=3028&app=3028&f=JPEG&fmt=auto?w=1024&h=1024');
    },
  },
  {
    url: `${baseUrl}${API_URL.order.orderDetail}`,
    timeout: 200,
    method: 'post',
    response: () => {
      return resultSuccess(orderDetail);
    },
  },
] as MockMethod[];
