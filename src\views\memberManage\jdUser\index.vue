<template>
  <PageWrapper>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <template #appId="{ text }">
        {{ getDicNameSync(text) }}
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <AccountModal @register="registerPropertyModal" @success="handleSuccess" />
  </PageWrapper>
</template>

<script lang="ts" name="memberManage-jdUser" setup>
  import { reactive, onMounted } from 'vue';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { postEtxList } from '/@/api/member-manage/jd-user';
  import { columns, searchFormSchema } from './jdUser.data';
  import { useModal } from '/@/components/Modal';
  import AccountModal from './accountModal.vue';
  import { getDicOptions } from '/@/utils/index';
  // 字典缓存
  const dictCache: Record<string, any[]> = {};

  // ✅ 在组件内正确使用生命周期
  onMounted(async () => {
    dictCache.app_id = await getDicOptions('app_id');
    console.log(dictCache.app_id, 'dictCache.app_id');
  });

  function getDicNameSync(value: number) {
    const items = dictCache['app_id'];
    const item = items?.find((item) => item.value === +value);
    return item?.label || value;
  }

  const [registerPropertyModal, { openModal }] = useModal();
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'jsUser-list',
    tableProps: {
      api: postEtxList,
      rowSelection: {},
      columns: columns,
      size: 'small',
      showIndexColumn: false,
      // showActionColumn: false,
      formConfig: {
        showAdvancedButton: false,
        labelWidth: 100,
        schemas: searchFormSchema,
        actionColOptions: {
          xs: 24, // <576px
          sm: 24, // ≥576px
          md: 24, // ≥768px
          lg: 24, // ≥992px
          xl: 24, // ≥1200px
          xxl: 24,
          style: { textAlign: 'right' },
        },
      }, // 是否使用搜索项
      actionColumn: {
        width: 120,
      },
      beforeFetch: (params) => {
        delete params.column;
        delete params.order;
        // return Object.assign({ }, params);
      },
    },
  });

  //注册table数据
  const [registerTable, { reload }] = tableContext;
  let recordData = reactive({});
  function handleDetail(record: Recordable) {
    Object.assign(recordData, record);
    openModal(true, {
      record,
      showFooter: false,
    });
  }
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '账户明细',
        onClick: handleDetail.bind(null, record),
        auth: 'member:jdUser:details',
      },
    ];
  }
  function handleSuccess() {
    reload();
  }
</script>
