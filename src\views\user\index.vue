<template>
  <PageWrapper contentFullHeight>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" @click="handleCreate">新增</a-button>
      </template>
      <template #status="{ record }">
        <a-switch v-model:checked="record.status" :checkedValue="1" :unCheckedValue="2" @change="(checked) => onSwitchChange(checked, record)" />
      </template>
      <template #action="{ record }">
        <TableAction
          :stopButtonPropagation="true"
          :actions="[
            {
              label: '查看',
              onClick: handleView.bind(null, record),
            },
            {
              label: '编辑',
              onClick: handleEdit.bind(null, record),
            },
          ]"
          :dropDownActions="[
            {
              label: '删除',
              onClick: handleDelete.bind(null, record),
              ifShow: record.roleCode.toString() !== 'admin',
            },
            {
              label: '重置密码',
              onClick: handleReset.bind(null, record),
            },
          ]"
        />
      </template>
    </BasicTable>
  </PageWrapper>
  <a-modal
    v-model:visible="modalVisible"
    :destroyOnClose="true"
    :title="`${action === 1 ? '新增' : action === 2 ? '查看' : '编辑'}用户信息`"
    :footer="null"
    :width="700"
  >
    <UserInfo :userInfo="userInfo" :action="action" @closeModal="closeModal" />
  </a-modal>
</template>

<script>
  import { defineComponent, ref, h } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { BasicTable, TableAction, useTable } from '/@/components/Table';
  import { getUserListApi, deleteUser, resetPassword, frozeUser } from '/@/api/user';
  import { columns, searchFormSchema } from './columns/data';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import UserInfo from './UserInfo.vue';
  import useClipboard from 'vue-clipboard3';
  export default defineComponent({
    components: { BasicTable, TableAction, PageWrapper, UserInfo },
    setup() {
      let userInfo = ref({});
      // 1添加，2查看，3编辑
      let action = ref(2);
      let modalVisible = ref(false);

      const { toClipboard } = useClipboard();

      const [registerTable, { reload }] = useTable({
        api: getUserListApi,
        columns,
        bordered: true,
        showIndexColumn: true,
        formConfig: {
          schemas: searchFormSchema,
          baseRowStyle: {
            width: '100%',
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'flex-end',
            alignItems: 'center',
            gap: '8px',
          },
          baseColProps: { style: { width: 'auto' } },
          showAdvancedButton: false,
          actionColOptions: { style: { width: 'auto' } },
        },
        useSearchForm: true,
        actionColumn: {
          title: '操作',
          dataIndex: 'action',
          slots: { customRender: 'action' },
          fixed: 'right',
        },
      });

      // 关闭模态框并刷新
      function closeModal() {
        reload();
        modalVisible.value = false;
      }

      // 打开模态框添加用户
      function handleCreate() {
        userInfo.value = {};
        action.value = 1;
        modalVisible.value = true;
      }

      // 打开模态框查看用户
      function handleView(record) {
        userInfo.value = record;
        action.value = 2;
        modalVisible.value = true;
      }

      // 删除用户
      function handleDelete(record) {
        Modal.confirm({
          title: '确认删除此用户?',
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            deleteUser({ id: record.id }).then((res) => {
              message.success(res);
              reload();
            });
          },
        });
      }

      // 打开模态框编辑用户
      function handleEdit(record) {
        userInfo.value = record;
        action.value = 3;
        modalVisible.value = true;
      }

      // 重置密码
      function resetConfirm(record) {
        let { id, username } = record;
        resetPassword({ id, username }).then((res) => {
          Modal.success({
            title: '重置成功',
            closable: true,
            content: h('div', {}, [h('h4', `账号：${res.username}`), h('h4', `密码：${res.password}`)]),
            okText: '点击复制',
            onOk: async () => {
              try {
                await toClipboard(`账号：${res.username}\n密码：${res.password}`);
                message.success('复制成功!');
              } catch {
                message.error('复制失败');
              }
            },
          });
        });
      }

      // 重置密码确认框
      function handleReset(record) {
        Modal.confirm({
          title: '确认重置密码?',
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            resetConfirm(record);
          },
        });
      }

      // 切换账号启用状态
      function onSwitchChange(checked, record) {
        Modal.confirm({
          title: `确认${checked === 1 ? '启用' : '禁用'}此账号?`,
          okText: '确认',
          cancelText: '取消',
          onCancel: () => {
            reload();
          },
          onOk: () => {
            frozeUser({ ids: record.id, status: checked }).then(() => {
              reload();
            });
          },
        });
      }

      return {
        registerTable,
        handleCreate,
        handleView,
        handleDelete,
        handleEdit,
        handleReset,
        closeModal,
        onSwitchChange,
        modalVisible,
        userInfo,
        action,
      };
    },
  });
</script>
