import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import dayjs from 'dayjs';

export const searchFormSchema: FormSchema[] = [
  {
    label: '手机号',
    field: 'msisdn',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 20,
    },
  },
  {
    label: '用户姓名',
    field: 'name',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '风控原因',
    field: 'reason',
    component: 'Input',
    colProps: { span: 6 },
  },
];

export const columns: BasicColumn[] = [
  {
    title: '手机号',
    dataIndex: 'msisdn',
    width: 150,
  },
  {
    title: '姓名',
    dataIndex: 'name',
    width: 200,
  },
  {
    title: '风控原因',
    dataIndex: 'reason',
  },
];

export const formAddSchema: FormSchema[] = [
  {
    label: '手机号码',
    field: 'msisdn',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    dynamicRules: () => {
      return [{ required: true, pattern: /^1[3456789]\d{9}$/, message: '手机号码格式有误', trigger: 'blur' }];
    },
    componentProps: {
      maxLength: 11,
    },
  },
  {
    label: '姓名',
    field: 'name',
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      maxLength: 10,
    },
  },
  {
    label: '风控原因',
    field: 'reason',
    required: true,
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      maxLength: 20,
    },
  },
];

export const formImportSchema: FormSchema[] = [
  {
    label: '批量导入',
    field: 'file',
    component: 'JUpload',
    slot: 'fileSlot',
    componentProps: {
      //是否显示选择按钮
      text: '文件上传',
      //最大上传数
      maxCount: 2,
      //是否显示下载按钮
      download: true,
      promptText: '仅支持上传xlsx格式文件',
    },
    dynamicRules: ({ values }) => {
      //需要return
      return [
        {
          //默认开启表单检验
          required: true,
          validator: (_, value) => {
            //需要return 一个Promise对象
            return new Promise((resolve, reject) => {
              if (!value) {
                reject('请上传正确格式/大小的文件！');
              }
              resolve();
            });
          },
        },
      ];
    },
    colProps: { span: 24 },
  },
];
