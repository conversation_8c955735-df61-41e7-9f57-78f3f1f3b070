import HashUtils from './hashUtils';
import { ConfigEnum } from '/@/enums/httpEnum';
export default class signUtils {
  /**
   * param 参数升序
   * @param param 发送参数
   */

  static sortAsc(param) {
    /**
     * 递归遍历数据并且排序
     * @param {*} o
     * @returns {*}
     */
    const walk = (o) => {
      let isObject = Object.prototype.toString.call(o) === '[object Object]';
      let target = isObject ? {} : [];
      const sortedKeys = isObject ? Object.keys(o).sort((a, b) => (a < b ? -1 : a === b ? 0 : 1)) : new Array(o.length).fill(1).map((v, i) => i);
      for (let i = 0; i < sortedKeys.length; i++) {
        let k = sortedKeys[i];
        if (Array.isArray(o[k]) || Object.prototype.toString.call(o[k]) === '[object Object]') {
          isObject ? (target[k] = walk(o[k])) : target.push(walk(o[k]));
        } else {
          isObject ? (target[k] = o[k]) : target.push(o[k]);
        }
      }
      return target;
    };
    return walk(param);
  }
  /**
   * @param url 请求的url,应该包含请求参数(url的?后面的参数)
   * @param {null|Recordable|undefined}requestParams 请求参数(POST的JSON参数)
   * @param time 时间戳
   * @returns {string} 获取签名
   */
  static getSign(url, requestParams = null, time, reqId) {
    let urlParams = this.parseQueryString(url);
    let requestBody = '';
    if (requestParams !== null || Object.keys(urlParams).length > 0) {
      let jsonObj = this.mergeObject(urlParams, requestParams || {});
      delete jsonObj[ConfigEnum.SIGN];
      delete jsonObj._t;
      //  requestBody = JSON.stringify(this.sortAsc(jsonObj));
      requestBody = JSON.stringify(jsonObj);
    }
    //return HashUtils.md5HexHash(`timestamp=${time}&params=${requestBody}${__SIGN_SECRET__}`);
    return HashUtils.md5HexHash(`timestamp=${time}&reqId=${reqId}&body=${requestBody}`);
  }
  /**
   * @param {string} query
   *  @returns {string} 获取签名
   */
  static getFileSign(query) {
    return HashUtils.md5HexHash(query);
  }

  /**
   * @param url 请求的url
   * @returns {{}} 将url中请求参数组装成json对象(url的?后面的参数)
   */
  static parseQueryString(url) {
    let urlReg = /^[^\?]+\?([\w\W]+)$/,
      paramReg = /([^&=]+)=([\w\W]*?)(&|$|#)/g,
      urlArray = urlReg.exec(url),
      result = {};

    // 获取URL上最后带逗号的参数变量 sys/dict/getDictItems/sys_user,realname,username
    //【这边条件没有encode】带条件参数例子：/sys/dict/getDictItems/sys_user,realname,id,username!==admin'%20order%20by%20create_time
    let lastpathVariable = url.substring(url.lastIndexOf('/') + 1);
    if (lastpathVariable.includes(',')) {
      if (lastpathVariable.includes('?')) {
        lastpathVariable = lastpathVariable.substring(0, lastpathVariable.indexOf('?'));
      }
      //decodeURI对特殊字符没有没有编码和解码的能力，需要使用decodeURIComponent
      result['x-path-variable'] = decodeURIComponent(lastpathVariable);
    }
    if (urlArray && urlArray[1]) {
      let paramString = urlArray[1],
        paramResult;
      while ((paramResult = paramReg.exec(paramString)) !== null) {
        //数字值转为string类型，前后端加密规则保持一致
        if (this.myIsNaN(paramResult[2])) {
          paramResult[2] = paramResult[2].toString();
        }
        result[paramResult[1]] = paramResult[2];
      }
    }
    return result;
  }
  /**
   *
   * @param {Recordable} param
   * @returns
   */
  static paramsToQuery(param) {
    const keys = Object.keys(param);
    return keys
      .map((v) => {
        return `${v}=${param[v]}`;
      })
      .join('&');
  }

  /**
   * @returns {*} 将两个对象合并成一个
   */
  static mergeObject(objectOne, objectTwo) {
    if (objectTwo && Object.keys(objectTwo).length > 0) {
      for (let key in objectTwo) {
        if (objectTwo.hasOwnProperty(key) === true) {
          // //数字值转为string类型，前后端加密规则保持一致
          // if (this.myIsNaN(objectTwo[key])) {
          //   objectTwo[key] = objectTwo[key].toString();
          // }
          objectOne[key] = objectTwo[key];
        }
      }
    }
    return objectOne;
  }

  static urlEncode(param, key, encode) {
    if (param === null) return '';
    let paramStr = '';
    let t = typeof param;
    if (t === 'string' || t === 'number' || t === 'boolean') {
      paramStr += '&' + key + '=' + (encode === null || encode ? encodeURIComponent(param) : param);
    } else {
      for (let i in param) {
        let k = key === null ? i : key + (param instanceof Array ? '[' + i + ']' : '.' + i);
        paramStr += this.urlEncode(param[i], k, encode);
      }
    }
    return paramStr;
  }

  /**
   * 接口签名用 生成header中的时间戳
   * @returns {number}
   */
  static getTimestamp() {
    return new Date().getTime();
  }
  // true:数值型的，false：非数值型
  static myIsNaN(value) {
    return typeof value === 'number' && !isNaN(value);
  }
  /**
   * 生成uuid
   */
  static genUUID = () => {
    let s = [];
    let hexDigits = '0123456789abcdef';
    for (let i = 0; i < 36; i++) {
      s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
    }
    s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
    s[8] = s[13] = s[18] = s[23] = '-';

    let uuid = s.join('');
    return uuid;
  };
  static randomString(len) {
    len = len || 17;
    let $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprst=wxyz2345678';
    let maxPos = $chars.length;
    let random = '';
    for (let i = 0; i < len; i++) {
      random += $chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return random;
  }
}
