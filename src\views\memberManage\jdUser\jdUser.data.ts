import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '业务渠道',
    dataIndex: 'appId',
    slots: { customRender: 'appId' },
  },
  {
    title: '用户手机号',
    dataIndex: 'msisdn',
  },

  {
    title: '账户余额',
    dataIndex: 'balance',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '手机号',
    field: 'msisdn',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 11,
    },
  },
  {
    label: '业务渠道',
    field: 'appId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'app_id',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
];

export const exchangeColumns: BasicColumn[] = [
  {
    title: '兑换权益商品ID',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '兑换权益商品名称',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '兑换价格',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '兑换结果',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '兑换失败原因',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '兑换结果反馈时间',
    dataIndex: 'sortOrder',
    width: 100,
  },
];
export const payColumns: BasicColumn[] = [
  {
    title: '卡券ID',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '有效期截止日期',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '卡券商品ID',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '卡券商品名称',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '卡券面额',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '支付金额',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '发放集团客户编码',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '发放集团账户ID',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '发放集团客户名称',
    dataIndex: 'sortOrder',
    width: 100,
  },
];

export const accountColumns: BasicColumn[] = [
  {
    title: '序号',
    dataIndex: 'index',
    align: 'center',
    customRender: ({ index }) => index + 1,
  },
  {
    title: '变更类型',
    dataIndex: 'dealType',
    align: 'center',
    slots: { customRender: 'dealType' },
  },
  {
    title: '变更金额',
    dataIndex: 'dealAmount',
    align: 'center',
    customRender: ({ text, record }) => (+record.dealType === 2 ? '-' + text.toFixed(2) : '+' + text.toFixed(2)),
  },
  {
    title: '变更时间',
    dataIndex: 'createTime',
    align: 'center',
  },
];
