export const columns = [
  {
    title: '登录账号',
    dataIndex: 'userid',
  },
  {
    title: '操作时间',
    dataIndex: 'createTime',
  },
  {
    title: 'IP',
    dataIndex: 'ip',
  },
  {
    title: '所属模块',
    dataIndex: 'moduleName',
  },
  {
    title: '操作详情',
    dataIndex: 'logContent',
  },
];

export const searchFormSchema = [
  {
    field: 'operateTime',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
      style: { width: '300px', marginLeft: 'auto' },
    },
  },
  {
    field: 'logType',
    component: 'Select',
    componentProps: {
      options: [
        { label: '登录', value: '1' },
        { label: '其他', value: '2' },
      ],
      placeholder: '请选择',
      style: { width: '200px' },
    },
  },
];
