<template>
  <BasicModal
    v-bind="$attrs"
    title="个人信息"
    destroyOnClose
    :defaultFullscreen="false"
    :canFullscreen="false"
    :footer="null"
    width="1000px"
    @register="registerModal"
  >
    <Description :column="2" :data="customerData" :schema="searchFormSchema" :bordered="true" size="default" />
  </BasicModal>
</template>

<script setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { searchFormSchema } from './info';
  import { Description } from '/@/components/Description/index';
  const customerData = ref({});
  const [registerModal] = useModalInner(async (data) => {
    customerData.value = data;
  });
</script>
