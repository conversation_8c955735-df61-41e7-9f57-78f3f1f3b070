<template>
  <BasicModal v-bind="$attrs" title="浏览" :defaultFullscreen="true" width="800px" @register="registerModal" :showOkBtn="false">
    <a-card title="商品信息" :bordered="false">
      <a-table :dataSource="ipInfoList" :columns="infocolumns" :pagination="false" />
    </a-card>
    <a-card title="扣费编码" :bordered="false">
      <a-table :dataSource="codeInfoList" :columns="codeColumns" :pagination="false" />
    </a-card>
    <a-card title="主产品" :bordered="false">
      <a-table :dataSource="productData" :columns="productColumns" :pagination="false" />
    </a-card>
    <a-card title="路由策略" :bordered="false">
      <a-table :dataSource="routeData" :columns="routeColumns" :pagination="false" />
    </a-card>
    <a-card :bordered="false" v-for="(item, index) in commodityGroupInfoEntityList">
      <div style="color: red; font-size: 14px">
        <span>{{ item.groupName }}(小组ID:{{ item.groupId }})</span>
        <span style="padding-left: 10px">组内可选产品数:{{ item.optionNum }}</span>
        <span style="padding-left: 10px">首订方式:{{ item.orderType == 1 ? '系统自动订购' : '用户领取' }}</span>
        <span style="padding-left: 10px">续订方式:{{ item.renewType == 0 ? '系统续订' : item.renewType == 1 ? '不续订' : '用户领取' }}</span>
      </div>
      <a-table :dataSource="item.productInfos" :columns="groupColumns" :pagination="false" />
    </a-card>
  </BasicModal>
</template>
<script lang="ts" name="PassWordModal" setup>
  import { ref, computed, unref, reactive, toRaw } from 'vue';
  import type { UnwrapRef } from 'vue';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { getDicOptions } from '/@/utils/index';
  let productCategory = [];
  let commonStatus = [];
  let carrier = [];
  let renewal_order_type = [];
  let service_Cat = [];
  import { commodityDetail } from '/@/api/productconfig/productconfig';
  import { useMessage } from '/@/hooks/web/useMessage';
  const isUpdate = ref(false);
  const rowId = ref(undefined);
  const showFooter = ref(false);
  const ipInfoList = ref<any[]>([]);
  const codeInfoList = ref<any[]>([]);
  const productData = ref<any[]>([]);
  const routeData = ref<any[]>([]);
  const commodityGroupInfoEntityList = ref<any[]>([]);
  const labelCol = { style: { width: '100px' } };
  const wrapperCol = { span: 16 };
  const infocolumns = [
    { title: '商品ID', dataIndex: 'commodityCode', align: 'center' },
    { title: '商品名称', dataIndex: 'commodityName', align: 'center' },
    {
      title: '商品类型',
      dataIndex: 'commodityCategory',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        let result = productCategory.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
    { title: '商品面值(元)', dataIndex: 'price', align: 'center' },
    { title: '商品归属省', dataIndex: 'provinceName', align: 'center' },
    {
      title: '商品状态',
      dataIndex: 'status',
      align: 'center',
      customRender: ({ text }) => {
        let result = commonStatus.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
    { title: '商品描述', dataIndex: 'remark', align: 'center' },
  ];
  const codeColumns = [
    { title: '货源ID', dataIndex: 'srcPrdId', align: 'center' },
    { title: '货源名称', dataIndex: 'srcPrdName', align: 'center' },
    { title: '省份', dataIndex: 'province', align: 'center' },
    { title: '流量值(M)/分钟数(分钟)', dataIndex: 'flowValue', align: 'center' },
    { title: '货源周期', dataIndex: 'flowCycle', align: 'center' },
    {
      title: '运营商',
      dataIndex: 'carrier',
      align: 'center',
      customRender: ({ text }) => {
        let result = carrier.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
    { title: '货源面值(元)', dataIndex: 'price', align: 'center' },
    { title: '货源类型', dataIndex: 'srcPrdFlowTypeName', align: 'center' },
    { title: '续订方式', dataIndex: 'renewType', align: 'center' },
    {
      title: '货源状态',
      dataIndex: 'status',
      align: 'center',
      customRender: ({ text }) => {
        let result = commonStatus.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
  ];
  const productColumns = [
    { title: '产品ID', dataIndex: 'productId', align: 'center' },
    { title: '产品名称', dataIndex: 'productName', align: 'center' },
    { title: '省份', dataIndex: 'province', align: 'center' },
    { title: '流量值(M)/分钟数(分钟)', dataIndex: 'flowValue', align: 'center' },
    { title: '产品周期', dataIndex: 'flowCycle', align: 'center' },
    {
      title: '运营商',
      dataIndex: 'carrier',
      align: 'center',
      customRender: ({ text }) => {
        let result = carrier.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
    { title: '产品面值(元)', dataIndex: 'price', align: 'center' },
    {
      title: '产品分类',
      dataIndex: 'productCategory',
      align: 'center',
      customRender: ({ text }) => {
        let result = productCategory.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
    {
      title: '产品类型',
      dataIndex: 'ip',
      align: 'center',
      customRender: ({ text }) => {
        let result = service_Cat.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
    {
      title: '产品订购类型',
      dataIndex: 'renewalOrderType',
      align: 'center',
      customRender: ({ text }) => {
        let result = renewal_order_type.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
    {
      title: '产品状态',
      dataIndex: 'status',
      align: 'center',
      customRender: ({ text }) => {
        let result = commonStatus.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
  ];

  const routeColumns = [
    { title: '产品ID', dataIndex: 'productCode', align: 'center' },
    { title: '产品名称', dataIndex: 'productName', align: 'center' },
    {
      title: '运营商',
      dataIndex: 'carrier',
      align: 'center',
      customRender: ({ text }) => {
        let result = carrier.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
    { title: '供应商', dataIndex: 'srcName', align: 'center' },
    { title: '货源', dataIndex: 'srcPrdName', align: 'center' },
    { title: '货源ID', dataIndex: 'srcPrdCode', align: 'center' },
    {
      title: '产品类型',
      dataIndex: 'productServiceCat',
      align: 'center',
      customRender: ({ text }) => {
        let result = service_Cat.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
  ];

  const groupColumns = [
    { title: '产品ID', dataIndex: 'productCode', align: 'center' },
    { title: '产品名称', dataIndex: 'productName', align: 'center' },
    {
      title: '产品类型',
      dataIndex: 'productServiceCat',
      align: 'center',
      customRender: ({ text }) => {
        let result = service_Cat.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
    {
      title: '产品分类',
      dataIndex: 'productCategory',
      align: 'center',
      customRender: ({ text }) => {
        let result = productCategory.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
    {
      title: '产品订购类型',
      dataIndex: 'renewalOrderType',
      align: 'center',
      customRender: ({ text }) => {
        let result = renewal_order_type.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
    { title: '订购次数', dataIndex: 'orderNum', align: 'center' },
    { title: '省份', dataIndex: 'province', align: 'center' },
    { title: '流量值(M)/分钟数(分钟)', dataIndex: 'flowValue', align: 'center' },
    {
      title: '运营商',
      dataIndex: 'carrier',
      align: 'center',
      customRender: ({ text }) => {
        let result = carrier.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
    { title: '产品面值(元)', dataIndex: 'price', align: 'center' },
    { title: '产品周期', dataIndex: 'flowCycle', align: 'center' },
    { title: '供应商', dataIndex: 'srcName', align: 'center' },
    { title: '货源', dataIndex: 'srcPrdName', align: 'center' },
    { title: '货源ID', dataIndex: 'srcPrdCode', align: 'center' },
    {
      title: '产品状态',
      dataIndex: 'status',
      align: 'center',
      customRender: ({ text }) => {
        let result = commonStatus.find((item) => item.value === text) || {};
        return result.label || text;
      },
    },
    { title: '备注', dataIndex: 'remark', align: 'center' },
  ];
  const { createMessage } = useMessage();
  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    console.log(data, 'data');
    showFooter.value = data?.showFooter ?? true;
    setModalProps({ confirmLoading: false, showOkBtn: showFooter.value });
    let commodityId = data?.record?.commodityId;
    getDetail(commodityId);
    console.log(data.record, 'record');
    getOption();
  });
  async function getDetail(commodityId) {
    const res = await commodityDetail({ commodityId: commodityId });
    ipInfoList.value = [res.commodityInfoRes];
    codeInfoList.value = [res.srcProductEntity];
    productData.value = [res.productInfoEntity];
    routeData.value = res.routeStrategyVoList;
    commodityGroupInfoEntityList.value = res.commodityGroupInfoEntityList;
    console.log(routeData.value, 'ipInfoList.value');
    console.log(res, 'ress');
  }
  function getOption() {
    getDicOptions('product_order_inquiry_category').then((res) => {
      productCategory = res;
    });
    getDicOptions('common_status').then((res) => {
      commonStatus = res;
    });
    getDicOptions('carrier').then((res) => {
      carrier = res;
    });
    getDicOptions('renewal_order_type').then((res) => {
      renewal_order_type = res;
    });
    getDicOptions('service_cat').then((res) => {
      service_Cat = res;
    });
  }
</script>
<style lang="less" scoped>
  .upload-tip {
    margin-top: 8px;
    color: #999;
    font-size: 12px;
  }
</style>
