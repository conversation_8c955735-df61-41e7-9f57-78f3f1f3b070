import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import dayjs from 'dayjs';

export const columns: BasicColumn[] = [
  {
    title: '订单号',
    dataIndex: 'orderId',
  },
  {
    title: '集团客户ID',
    dataIndex: 'channelId',
    customRender: ({ text }) => text || '—',
  },
  {
    title: '发放手机号',
    dataIndex: 'msisdn',
    customRender: ({ text }) => atob(text),
  },
  {
    title: '商品ID',
    dataIndex: 'productId',
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
  },
  {
    title: '发放时间',
    dataIndex: 'orderTime',
  },
  {
    title: '发放状态',
    dataIndex: 'orderResult',
    // customRender:({ text }) => +text === 1 ? '启用中' : '已停用',
    slots: { customRender: 'orderResult' },
  },
  {
    title: '领取状态',
    dataIndex: 'cardStatusStr',
    // customRender:({ text }) => +text === 1 ? '启用中' : '已停用',
    slots: { customRender: 'cardStatusStr' },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '订单号',
    field: 'orderId',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '发放手机号',
    field: 'msisdn',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '发放时间',
    field: 'dateOp',
    component: 'RangeDate',
    componentProps: {
      //是否显示时间
      // showTime: true,
      format: 'YYYY-MM-DD',
      //日期格式化
      valueFormat: 'YYYY-MM-DD',
      //范围文本描述用集合
      placeholder: ['开始时间', '结束时间'],
      // disabledDate: (currentDate) => {
      //   return currentDate < dayjs().startOf('day');
      // },
    },
    colProps: { span: 6 },
  },
  {
    label: '集团客户ID',
    field: 'channelId',
    component: 'Input',
    colProps: { span: 6 },
  },
  // {
  //   label: '集团客户名称',
  //   field: 'msisdn4',
  //   component: 'Input',
  //   colProps: { span: 6 },
  // },
  {
    label: '发放状态',
    field: 'orderState',
    component: 'Select',
    componentProps: {
      options: [
        { label: '成功', value: '1' },
        { label: '失败', value: '2' },
        { label: '处理中', value: '3' },
      ],
      placeholder: '请选择',
      style: { width: '200px' },
    },
    colProps: { span: 6 },
  },
];
