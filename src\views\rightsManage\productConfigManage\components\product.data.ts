import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getAllRolesListNoByTenant } from '/@/api/sys/user';
import { rules } from '/@/utils/helper/validator';
import { getProvinceList, getSrcPrd } from '/@/api/productconfig/productconfig';
export let product_order_inquiry_category = [];
window.$getDicOptions('product_order_inquiry_category').then((data) => {
  product_order_inquiry_category = data;
});

export let common_status = [];
window.$getDicOptions('common_status').then((data) => {
  common_status = data;
});
export let product_order_inquiry_renew_type = [];
window.$getDicOptions('product_order_inquiry_renew_type').then((data) => {
  product_order_inquiry_renew_type = data;
});
export let service_cat = [];
window.$getDicOptions('service_cat').then((data) => {
  service_cat = data;
});
export let common_is = [];
window.$getDicOptions('common_is').then((data) => {
  common_is = data;
});

// 查询
export const searchFormSchema: FormSchema[] = [
  {
    label: '商品ID',
    field: 'commodityCode',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 20,
    },
  },
  {
    label: '商品名称',
    field: 'commodityName',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 20,
    },
  },
  {
    label: '商品类别',
    field: 'commodityCategory',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'product_order_inquiry_category',
      placeholder: '请选择商品类别',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '商品面值',
    field: 'price',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 20,
    },
  },
  {
    label: '商品状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'common_status',
      placeholder: '请选择商品状态',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '商品归属省',
    field: 'provinceId',
    component: 'ApiSelect',
    componentProps: {
      api: getProvinceList,
      labelField: 'province',
      valueField: 'provinceId',
      resultField: 'result',
    },
    colProps: { span: 6 },
  },
  {
    label: '主产品ID',
    field: 'mainProductCode',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 20,
    },
  },
  {
    label: '主产品名称',
    field: 'mainProductName',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 20,
    },
  },
  {
    label: '主产品状态',
    field: 'mainProductStatus',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'common_status',
      placeholder: '请选择主产品状态',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '主产品首订方式',
    field: 'mainOrderType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'main_order_type',
      placeholder: '请选择主产品首订方式',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '主产品续订方式',
    field: 'mainRenewType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'product_order_inquiry_renew_type',
      placeholder: '请选择主产品续订方式',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '主产品类型',
    field: 'mainProductServiceCat',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'service_cat',
      placeholder: '请选择主产品类型',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '单权益卡券',
    field: 'singleFlag',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'common_is',
      placeholder: '请选择单权益卡券',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '是否支持转赠',
    field: 'isPresent',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'common_is',
      placeholder: '请选择',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '是否为VIP权益',
    field: 'sendType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'common_is',
      placeholder: '请选择',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
];
export const columns: BasicColumn[] = [
  {
    title: '商品ID',
    dataIndex: 'commodityCode',
    width: 120,
  },
  {
    title: '商品名称',
    dataIndex: 'commodityName',
    width: 150,
  },
  {
    title: '商品面值(元)',
    width: 150,
    dataIndex: 'price',
  },
  {
    title: '商品类别',
    dataIndex: 'commodityCategory',
    width: 100,
    customRender: ({ text }) => {
      let result = product_order_inquiry_category.find((item) => item.value === text) || {};
      return result.label || text;
    },
  },
  {
    title: '商品归属',
    dataIndex: 'provinceName',
    // customRender: ({ text }) => {
    //   let result = provinceList.find((item)=>item.provinceId === text)
    //   return result.province;
    // },
    width: 100,
  },
  {
    title: '主产品ID',
    dataIndex: 'mainProductCode',
    width: 100,
  },
  {
    title: '主产品名称',
    width: 150,
    dataIndex: 'mainProductName',
  },
  {
    title: '是否需要激活',
    width: 150,
    dataIndex: 'isActive',
    customRender: ({ text }) => {
      let result = common_is.find((item) => item.value === text) || {};
      return result.label || text;
    },
  },
  {
    title: '是否支持转赠',
    width: 150,
    dataIndex: 'isPresent',
    customRender: ({ text }) => {
      let result = common_is.find((item) => item.value === text) || {};
      return result.label || text;
    },
  },
  {
    title: '主产品类型',
    width: 150,
    dataIndex: 'mainProductServiceCat',
    customRender: ({ text }) => {
      let result = service_cat.find((item) => item.value === text) || {};
      return result.label || text;
    },
  },
  {
    title: '是否为单权益卡券',
    width: 150,
    dataIndex: 'singleFlag',
    customRender: ({ text }) => {
      let result = common_is.find((item) => item.value === text) || {};
      return result.label || text;
    },
  },
  {
    title: '关联权益商品ID',
    width: 150,
    dataIndex: 'rightCode',
  },
  {
    title: '是否为VIP权益',
    width: 150,
    dataIndex: 'sendType',
    customRender: ({ text }) => {
      let result = common_is.find((item) => item.value === text) || {};
      return result.label || text;
    },
  },
  {
    title: '主产品周期',
    width: 150,
    dataIndex: 'mainProductCycleName',
  },
  {
    title: '主产品续订方式',
    width: 150,
    dataIndex: 'mainRenewType',
    customRender: ({ text }) => {
      let result = product_order_inquiry_renew_type.find((item) => item.value === text) || {};
      return result.label || text;
    },
  },
  {
    title: '主产品状态',
    width: 150,
    dataIndex: 'mainProductStatus',
    customRender: ({ text }) => {
      let result = common_status.find((item) => item.value === text) || {};
      return result.label || text;
    },
  },
  {
    title: '商品状态',
    width: 150,
    dataIndex: 'status',
    customRender: ({ text }) => {
      let result = common_status.find((item) => item.value === text) || {};
      return result.label || text;
    },
  },
];

export const formAnnountSchema: FormSchema[] = [
  {
    label: '商品类别',
    field: 'commodityCategory',
    component: 'JDictSelectTag',
    slot: 'commodityCategory',
    required: true,
    colProps: { span: 12 },
  },
  {
    label: '商品归属省',
    field: 'province',
    component: 'ApiSelect',
    componentProps: {
      api: getProvinceList,
      labelField: 'province',
      valueField: 'provinceId',
      resultField: 'result',
    },
    colProps: { span: 12 },
  },
  {
    label: '商品名称',
    field: 'commodityName',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
  },
  {
    label: '商品ID',
    field: 'commodityCode',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    colProps: { span: 12 },
  },
  {
    label: '可配置小组数',
    field: 'groupsNum',
    component: 'Input',
    slot: 'groupsNum',
    dynamicRules: ({ values }) => {
      console.log('values:', values);
      //需要return
      return [
        {
          //默认开启表单检验
          required: true,
          // value 当前手机号输入的值
          validator: (_, value) => {
            console.log(value, 'ddddddd');
            //需要return 一个Promise对象
            return new Promise((resolve, reject) => {
              if (!value) {
                reject('请输入可配置小组数！');
              } else if (!/^\d+$/.test(value)) {
                reject('小组配置数必须为非负整数');
              } else if (parseInt(value) > 5) {
                reject('小组产品配置数必须为0~5！');
              }
              resolve();
            });
          },
        },
      ];
    },
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return values.commodityCategory != '2';
    },
    //required: true
  },
  {
    label: '商品面值(元)',
    field: 'price',
    component: 'Input',
    componentProps: {
      type: 'number',
      placeholder: '请输入商品面值',
      min: 0,
    },
    colProps: { span: 12 },
    required: true,
    ifShow: ({ values }) => {
      return values.commodityCategory != '2';
    },
  },
  {
    label: '是否涉及主产品',
    field: 'mainProductNum',
    component: 'RadioGroup',
    // componentProps: {
    //     //options里面由一个一个的radio组成,支持disabled
    //     options: [
    //       { label: '是', value: '1' },
    //       { label: '否', value: '0' }
    //     ],
    // },
    slot: 'mainProductNum',
    required: true,
    ifShow: ({ values }) => {
      return values.commodityCategory == '1';
    },
    colProps: { span: 12 },
  },
  {
    label: '主产品订购成功后订购小组产品',
    field: 'groupsOrderType',
    component: 'RadioGroup',
    componentProps: {
      //options里面由一个一个的radio组成,支持disabled
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
    ifShow: ({ values }) => {
      return values.commodityCategory == '1' && values.mainProductNum != '0';
    },
    labelWidth: '250px',
    colProps: { span: 12 },
  },
  {
    label: '扣费编码选择',
    field: 'payType',
    component: 'RadioGroup',
    componentProps: {
      //options里面由一个一个的radio组成,支持disabled
      options: [
        { label: '主产品货源', value: 0 },
        { label: '扣费货源', value: 1 },
      ],
    },
    dynamicDisabled: ({ values }) => {
      return values.mainProductNum == '0';
    },
    // required: true,
    colProps: { span: 14 },
    ifShow: ({ values }) => {
      return values.commodityCategory != '2';
    },
  },
  {
    label: '',
    field: 'paySrcprdId',
    component: 'ApiSelect',
    componentProps: {
      api: async (params) => {
        const res = await getSrcPrd({});
        return {
          ...res,
          result: res.map((item) => ({
            ...item,
            srcPrdName: `(${item.srcPrdCode})${item.srcPrdName}`,
          })),
        };
      },
      //api: getSrcPrd,
      labelField: 'srcPrdName',
      valueField: 'srcPrdId',
      resultField: 'result',
      showSearch: true,
    },
    ifShow: ({ values }) => {
      return values.payType == 1 || values.mainProductNum == '0';
    },
    colProps: { span: 10 },
  },
  {
    label: '续订方式',
    field: 'renewType',
    component: 'RadioGroup',
    componentProps: {
      //options里面由一个一个的radio组成,支持disabled
      options: [
        { label: '系统自动续订', value: 0 },
        { label: '不续订', value: 1 },
        { label: '平台单次-省移动连续', value: 3 },
      ],
    },
    // required: true,
    colProps: { span: 24 },
    ifShow: ({ values }) => {
      return values.commodityCategory != '2';
    },
  },
  {
    label: '商品描述',
    field: 'remark',
    component: 'InputTextArea',
    componentProps: {
      allowClear: true,
      autoSize: {
        //最小显示行数
        minRows: 3,
        //最大显示行数
        maxRows: 5,
      },
    },
    ifShow: ({ values }) => {
      return values.commodityCategory != '2';
    },
    colProps: { span: 12 },
    // required: true
  },
  {
    label: '权益引入属地',
    field: 'dependency',
    component: 'JDictSelectTag',
    slot: 'dependency',
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return values.commodityCategory != '1';
    },
  },
];
export const formMainProductSchema: FormSchema[] = [
  {
    label: '产品类型',
    field: 'serviceCat',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'service_cat',
      placeholder: '请选择产品类型',
      stringToNumber: true,
    },
    colProps: { span: 12 },
  },
  // {
  //   label: '产品周期',
  //   field: 'flowCycle',
  //   component: 'JDictSelectTag',
  //   componentProps: {
  //        dictCode: 'sys_announcement_terminal',
  //        placeholder: '请选择产品周期',
  //        stringToNumber: true,
  //   },
  //   colProps: { span: 12 },
  // },
  {
    label: '产品名称',
    field: 'productName',
    component: 'Input',
    colProps: { span: 12 },
  },
  {
    label: '产品ID',
    field: 'productCode',
    component: 'Input',
    colProps: { span: 12 },
  },
  {
    label: '运营商',
    field: 'carrier',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'carrier',
      placeholder: '请选择运营商',
      stringToNumber: true,
    },
    colProps: { span: 12 },
  },
];
export const formProductSchema: FormSchema[] = [
  {
    label: '选择主产品(单选)',
    field: 'productId',
    component: 'Select',
    slot: 'productId',
    required: true,
    colProps: { span: 10 },
  },
  {
    label: '商品面值(元)',
    field: 'price',
    component: 'Input',
    componentProps: {
      disabled: true,
      type: 'number',
      min: 0,
    },
    colProps: { span: 12 },
    // required: true
  },
  {
    label: '主产品首订方式',
    field: 'productOrderType',
    component: 'RadioGroup',
    componentProps: {
      //options里面由一个一个的radio组成,支持disabled
      options: [
        { label: '系统自动订购', value: 1 },
        { label: '用户领取', value: 2 },
      ],
    },
    colProps: { span: 12 },
  },
  {
    label: '主产品续订方式',
    field: 'productRenewType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'product_order_inquiry_renew_type',
      type: 'radio',
      //options里面由一个一个的radio组成,支持disabled
      // options: [
      //   { label: '系统续订', value: 0 },
      //   { label: '用户领取', value: 2 },
      //   { label: '不续订', value: 1 },
      //   { label: '平台单次-省移动连续', value: 3 },
      // ],
    },
    // required: true,
    colProps: { span: 12 },
  },
  {
    label: '订购次数',
    field: 'productOrderNum',
    component: 'Input',
    colProps: { span: 12 },
    // required: true
  },
  {
    label: '是否需要激活',
    field: 'isActive',
    component: 'RadioGroup',
    componentProps: {
      //options里面由一个一个的radio组成,支持disabled
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
    // required: true,
    colProps: { span: 12 },
  },
  {
    label: '是否为VIP权益',
    field: 'sendType',
    component: 'RadioGroup',
    componentProps: {
      //options里面由一个一个的radio组成,支持disabled
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
    ///required: true,
    colProps: { span: 12 },
  },
  {
    label: '是否支持转赠',
    field: 'isPresend',
    component: 'RadioGroup',
    componentProps: {
      //options里面由一个一个的radio组成,支持disabled
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
    colProps: { span: 12 },
  },
  {
    label: '卡券业务类型',
    field: 'couponType',
    // required: true,
    component: 'JDictSelectTag',
    slot: 'couponType',
    // componentProps: {
    //   dictCode: 'coupon_type',
    //   placeholder: '请选择',
    //   stringToNumber: true,
    // },
    colProps: { span: 12 },
  },
];

export const formJsProductSchema: FormSchema[] = [
  {
    label: '商品类别',
    field: 'commodityCategory',
    component: 'JDictSelectTag',
    //  dictCode: 'product_order_inquiry_category',
    componentProps: {
      dictCode: 'product_order_inquiry_category',
      placeholder: '请选择商品类别',
      stringToNumber: true,
    },
    required: true,
    colProps: { span: 12 },
  },
  {
    label: '商品归属省',
    field: 'province',
    component: 'ApiSelect',
    componentProps: {
      api: getProvinceList,
      labelField: 'province',
      valueField: 'provinceId',
      resultField: 'result',
    },
    colProps: { span: 12 },
  },
  {
    label: '商品名称',
    field: 'commodityName',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
  },
  {
    label: '商品ID',
    field: 'commodityCode',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    colProps: { span: 12 },
  },
];

export const productColumns: BasicColumn[] = [
  {
    title: '规格名称',
    dataIndex: 'formName',
    key: 'formName',
    width: 120,
  },
  // {
  //   title: '主产品名称',
  //   dataIndex: 'commodityCode',
  // },
  // {
  //   title: '产品面值（元）',
  //   dataIndex: 'commodityCode',
  // },
  // {
  //   title: '主产品首订方式',
  //   dataIndex: 'commodityCode',
  //   key: 'firstOrderWay',
  //   width: 150,
  // },
  // {
  //   title: '主产品续订方式',
  //   dataIndex: 'commodityCode',
  //   key: 'lastOrderWay',
  //   width: 150,
  // },
  // {
  //   title: '订购次数',
  //   dataIndex: 'commodityCode',
  //   key: 'orderNumber',
  //   width: 120,
  // },
  // {
  //   title: 'c端购买价格',
  //   dataIndex: 'commodityCode',
  //   key: 'cOrderPrice',
  //   width: 120,
  // },

  {
    title: '商品ID',
    dataIndex: 'commodityCode',
    width: 120,
  },
  {
    title: '商品名称',
    dataIndex: 'commodityName',
    width: 150,
  },
  {
    title: '商品面值(元)',
    width: 150,
    dataIndex: 'price',
  },
  // {
  //   title: '主产品续订方式',
  //   width: 150,
  //   dataIndex: 'mainRenewType',
  //   customRender: ({ text }) => {
  //     let result = product_order_inquiry_renew_type.find((item) => item.value === text) || {};
  //     return result.label || text;
  //   },
  // },

  {
    title: '排序值',
    dataIndex: 'commodityCode',
    key: 'sort',
    width: 120,
  },
  {
    title: '操作',
    dataIndex: 'actions',
    key: 'actions',
    width: 120,
  },
];
