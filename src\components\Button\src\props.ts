import { ButtonType } from 'ant-design-vue/lib/button';

export const buttonProps = {
  color: { type: String, validator: (v) => ['error', 'warning', 'success', ''].includes(v) },
  loading: { type: Boolean, default: false },
  disabled: { type: Boolean, default: false },
  /**
   * Text before icon.
   */
  preIcon: { type: String, default: undefined },
  /**
   * Text after icon.
   */
  postIcon: { type: String, default: undefined },
  type: { type: String as PropType<ButtonType>, default: undefined },
  /**
   * preIcon and postIcon icon size.
   * @default: 15
   */
  iconSize: { type: Number, default: 15 },
  isUpload: { type: Boolean, default: false },
  onClick: { type: Function as PropType<(...args) => any>, default: null },
};
