import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';

export const searchFormSchema: FormSchema[] = [
  {
    label: '权益名称',
    field: 'equityName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      options: [
        { label: '展示中', value: '1' },
        { label: '未展示', value: '0' },
      ],
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
];

export const columns: BasicColumn[] = [
  {
    title: '权益名称',
    dataIndex: 'equityName',
  },
  {
    title: '关联ID',
    dataIndex: 'equityId',
  },

  // {
  //   title: '推荐权益',
  //   dataIndex: 'hotSale',
  // },
  // {
  //   title: '城市推荐',
  //   dataIndex: 'cities',
  // },
  {
    title: '商品编码',
    dataIndex: 'commodityCode',
    customRender: ({ text }) => text || '—',
  },
  {
    title: '引入属地',
    dataIndex: 'dependency',
    customRender: ({ text }) => text || '—',
  },
  {
    title: '上架范围',
    dataIndex: 'onlineRange',
    customRender: ({ text }) => (+text === 1 ? '全网上架' : +text === 0 ? '仅属地上架' : '其他'),
  },
  {
    title: '排序值',
    dataIndex: 'sort',
    customRender: ({ text }) => text || '—',
  },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => (+text === 1 ? '展示中' : +text === 0 ? '未展示' : '其他'),
  },
  {
    title: '上架时间',
    dataIndex: 'onlineTime',
    width: 180,
  },
  {
    title: '最新修改时间',
    dataIndex: 'updateTime',
    width: 180,
  },
];

export const formAddSchema: FormSchema[] = [
  {
    label: '权益名称',
    field: 'equityId',
    component: 'Select',
    slot: 'equityId',
    required: true,
    colProps: { span: 24 },
  },
  {
    label: '上架范围',
    field: 'onlineRange',
    component: 'JDictSelectTag',
    required: true,
    componentProps: {
      options: [
        { label: '全网上架', value: 1 },
        { label: '属地上架', value: 0 },
      ],
      stringToNumber: true,
    },
    colProps: { span: 24 },
  },
  {
    label: '排序值',
    field: 'sort',
    component: 'InputNumber',
    componentProps: {
      min: 1,
      precision: 0,
      step: 1,
    },
    required: true,
    colProps: { span: 24 },
  },
];

export const formSortSchema: FormSchema[] = [
  {
    label: '“推荐”货架排序模式',
    field: 'hotSortMode',
    component: 'RadioGroup',

    componentProps: {
      options: [
        { label: '按排序值排序', value: 0 },
        { label: '按销量排序', value: 1 },
      ],
      stringToNumber: true,
    },
    colProps: { span: 24 },
    required: true,
  },
  {
    label: '销量统计时间范畴',
    field: 'salesTimeRange',
    component: 'InputNumber',
    componentProps: {
      min: 1,
      precision: 0,
      step: 1,
      addonAfter: '天',
      addonBefore: '近',
    },
    required: true,
    colProps: { span: 24 },
    ifShow: ({ values }) => {
      return values.hotSortMode == 1;
    },
  },
  // {
  //   label: '排序更新频率',
  //   field: 'hotSalesNum',
  //   component: 'InputNumber',
  //   componentProps: {
  //     min: 0,
  //     precision: 1,
  //     step: 0.1,
  //     addonAfter: '小时更新一次',
  //   },
  //   required: true,
  //   colProps: { span: 24 },
  // },
  {
    label: '“推荐”货架商品数',
    field: 'hotSalesNum',
    component: 'InputNumber',
    componentProps: {
      min: 1,
      precision: 0,
      step: 1,
      addonAfter: '件',
    },
    required: true,
    colProps: { span: 24 },
  },
  {
    label: '“上新”货架排序模式',
    field: 'newSalesSortMode',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '按排序值排序', value: 0 },
        { label: '按上架时间排序', value: 1 },
      ],
      stringToNumber: true,
    },
    colProps: { span: 24 },
    required: true,
  },
  {
    label: '“上新”货架商品数',
    field: 'newSalesNum',
    component: 'InputNumber',
    componentProps: {
      min: 1,
      precision: 0,
      step: 1,
      addonAfter: '件',
    },
    required: true,
    colProps: { span: 24 },
  },
  {
    label: '产品分类货架排序模式',
    field: 'productClassMode',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '按排序值排序', value: 0 },
        { label: '按上架时间排序', value: 1 },
        { label: '按销量排序', value: 2 },
      ],
      stringToNumber: true,
    },
    colProps: { span: 24 },
    required: true,
  },
];

export const formImportSchema: FormSchema[] = [
  {
    label: '批量导入',
    field: 'file',
    component: 'JUpload',
    slot: 'fileSlot',
    componentProps: {
      //是否显示选择按钮
      text: '文件上传',
      //最大上传数
      maxCount: 2,
      //是否显示下载按钮
      download: true,
      promptText: '仅支持上传xlsx格式文件',
    },
    dynamicRules: ({ values }) => {
      //需要return
      return [
        {
          //默认开启表单检验
          required: true,
          validator: (_, value) => {
            //需要return 一个Promise对象
            return new Promise((resolve, reject) => {
              if (!value) {
                reject('请上传正确格式/大小的文件！');
              }
              resolve();
            });
          },
        },
      ];
    },
    colProps: { span: 24 },
  },
];

export const equityColumns: BasicColumn[] = [
  {
    title: '城市',
    dataIndex: 'cityName',
  },
  {
    title: '排序值',
    dataIndex: 'sort',
    key: 'sort',
    width: 120,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 120,
  },
];
