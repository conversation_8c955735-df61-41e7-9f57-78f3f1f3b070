import type { Router, RouteRecordRaw } from 'vue-router';

import { usePermissionStoreWithOut } from '/@/store/modules/permission';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { PageEnum } from '/@/enums/pageEnum';
import { PAGE_NOT_FOUND_ROUTE } from '/@/router/routes/basic';
import { RootRoute } from '/@/router/routes';
import { getKeepAliveRouteList } from '../helper/routeHelper';
import { PAGE_NOT_FOUND_NAME_404 } from '/@/router/constant';
const LOGIN_PATH = PageEnum.BASE_LOGIN;
// 邮件中的跳转地址,对应此路由,携带token免登录直接去办理页面
const TOKEN_LOGIN = PageEnum.TOKEN_LOGIN;
const ROOT_PATH = RootRoute.path;
const whitePathList: PageEnum[] = [LOGIN_PATH, TOKEN_LOGIN];
export function createPermissionGuard(router: Router) {
  const userStore = useUserStoreWithOut();
  const permissionStore = usePermissionStoreWithOut();
  router.beforeEach(async (to, from, next) => {
    if (
      from.path === ROOT_PATH &&
      to.path === PageEnum.BASE_HOME &&
      userStore.getUserInfo.homePath &&
      userStore.getUserInfo.homePath !== PageEnum.BASE_HOME
    ) {
      next(userStore.getUserInfo.homePath);
      return;
    }
    const token = userStore.getToken;
    // Whitelist can be directly entered
    if (whitePathList.includes(to.path as PageEnum)) {
      if (to.path === LOGIN_PATH && token) {
        const isSessionTimeout = userStore.getSessionTimeout;
        try {
          if (!isSessionTimeout) {
            next((to.query?.redirect as string) || '/');
            return;
          }
        } catch (e) {
          console.warn(e);
        }
      }
      next();
      return;
    }
    // token does not exist
    if (!token) {
      // You can access without permission. You need to set the routing meta.ignoreAuth to true
      if (to.meta.ignoreAuth) {
        next();
        return;
      }
      //update-begin---author:wangshuai ---date:********  for：[issues/I5BG1I]vue3 Auth2未实现------------
      let path = LOGIN_PATH;
      if (whitePathList.includes(to.path as PageEnum)) {
        //在免登录白名单，直接进入
        next();
      } else {
        path = LOGIN_PATH;
      }
      //update-end---author:wangshuai ---date:********  for：[issues/I5BG1I]vue3 Auth2未实现------------
      const redirectData: { path: string; replace: boolean; query?: Recordable<string> } = {
        path: path,
        replace: true,
      };

      //update-begin---author:scott ---date:2023-04-24  for：【QQYUN-4713】登录代码调整逻辑有问题，改造待观察--
      if (to.fullPath) {
        const getFullPath = to.fullPath;
        if (
          getFullPath === '/' ||
          getFullPath === '/500' ||
          getFullPath === '/400' ||
          getFullPath === '/login?redirect=/' ||
          getFullPath === '/login?redirect=/login?redirect=/'
        ) {
          return;
        }
        //update-end---author:scott ---date:2023-04-24  for：【QQYUN-4713】登录代码调整逻辑有问题，改造待观察--

        redirectData.query = {
          ...redirectData.query,
          // update-begin-author:sunjianlei date:20230306 for: 修复登录成功后，没有正确重定向的问题
          redirect: to.fullPath,
          // update-end-author:sunjianlei date:20230306 for: 修复登录成功后，没有正确重定向的问题
        };
      }

      next(redirectData);
      return;
    }
    // Jump to the 404 page after processing the login
    if (
      (from.path === LOGIN_PATH && to.name === PAGE_NOT_FOUND_NAME_404  || from.path === TOKEN_LOGIN) &&
      to.name === PAGE_NOT_FOUND_ROUTE.name &&
      to.fullPath !== (userStore.getUserInfo.homePath || PageEnum.BASE_HOME)
    ) {
      next(userStore.getUserInfo.homePath || PageEnum.BASE_HOME);
      return;
    }
    if (permissionStore.getIsDynamicAddedRoute) {
      next();
      return;
    }
    await userStore.getUserInfoAction();
    const routes = await permissionStore.buildRoutesAction();
    const keepAliveRouteList = getKeepAliveRouteList(routes);
    permissionStore.setKeepAliveRoutes(keepAliveRouteList);
    routes.forEach((route) => {
      router.addRoute(route as unknown as RouteRecordRaw);
    });
    router.addRoute(PAGE_NOT_FOUND_ROUTE as unknown as RouteRecordRaw);
    permissionStore.setDynamicAddedRoute(true);
    if (to.name === PAGE_NOT_FOUND_ROUTE.name || to.name === PAGE_NOT_FOUND_NAME_404) {
      // 动态添加路由后，此处应当重定向到fullPath，否则会加载404页面内容
      next({ path: to.fullPath, replace: true, query: to.query });
    } else {
      const redirectPath = (from.query.redirect || to.path) as string;
      const redirect = decodeURIComponent(redirectPath);
      const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect };
      next(nextData);
    }
  });
}
