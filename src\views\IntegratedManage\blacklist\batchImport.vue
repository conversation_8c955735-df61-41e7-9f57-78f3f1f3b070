<template>
  <BasicModal
    v-bind="$attrs"
    :title="modelType === 1 ? '新增' : '批量导入'"
    :defaultFullscreen="false"
    :width="modelType === 1 ? 600 : 300"
    @register="registerModal"
    @ok="handleSubmit"
  >
    <BasicForm @register="addForm" v-if="modelType === 1"></BasicForm>
    <div v-else style="">
      <a-upload :before-upload="beforeUpload" accept=".xls,.xlsx" name="file" :max-count="1" :multiple="false">
        <a-button type="primary"> <upload-outlined /> 选择文件 </a-button>
      </a-upload>
    </div>
  </BasicModal>
</template>
<script lang="ts">
  // 单独添加一个script块用于导出
  export default {
    name: 'orderManage-transferModel', // 保持与你原来的name一致
  };
</script>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formAddSchema } from './blackList.data';
  import { batchSave, blackAdd } from '/@/api/Integrated/black';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { UploadOutlined } from '@ant-design/icons-vue';
  import { Upload } from 'ant-design-vue';
  import type { UploadProps, UploadFile } from 'ant-design-vue';
  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  const [addForm, formAddBlack] = useForm({
    schemas: formAddSchema,
    showActionButtonGroup: false,
    labelCol: { style: { width: '150px' } },
  });
  let modelType = ref(1);
  const [registerModal, { closeModal }] = useModalInner(async (data) => {
    modelType.value = data.isAdd;
  });
  const fileList = ref<UploadFile[]>([]);
  const uploading = ref(false);
  // 文件类型和大小校验
  const beforeUpload: UploadProps['beforeUpload'] = (file) => {
    const isXlsx = file.name.endsWith('.xlsx') || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    if (!isXlsx) {
      createMessage.error('只能上传XLSX格式的Excel文件!');
      return Upload.LIST_IGNORE; // 阻止文件进入列表
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      createMessage.error('文件大小不能超过10MB!');
      return Upload.LIST_IGNORE;
    }
    fileList.value = [file];
    return false; // 阻止自动上传
  };

  async function handleSubmit() {
    if (modelType.value === 1) {
      const values = await formAddBlack.validate();
      const params = {
        ...values,
      };
      await blackAdd(params);

      //关闭弹窗
      closeModal();
      await formAddBlack.resetFields();
      createMessage.success('操作成功');
      //刷新列表
      emit('success');
    } else {
      if (fileList.value.length === 0) {
        createMessage.warning('请先选择文件');
        return;
      }

      uploading.value = true;
      try {
        // 关键修复：直接添加File对象而不是UploadFile对象
        let file = fileList.value[0];
        const res = await batchSave({ file });
        if (res.data?.code === 200) {
          createMessage.success('操作成功');
          closeModal();
          fileList.value = [];
          emit('success');
        } else {
          createMessage.warning(res.data?.message || '上传失败');
        }
      } catch (error) {
        createMessage.error(JSON.stringify(error) || '上传失败');
        throw error;
      } finally {
        uploading.value = false;
      }
    }
  }
</script>
<style scoped>
  .ml8 {
    margin-left: 8px;
  }
  .tips {
    color: #999;
  }
  .custom-desc > div {
    margin-bottom: 8px;
    line-height: 16px;
  }
</style>
