<template>
  <BasicModal 
  v-bind="$attrs" 
  title="人工调账" 
  width="800px" 
  @register="registerModal" 
  @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #adjustVoucherFileSlot>
        <repoUpload
          v-model:value="adjustmentVoucherFile"
          :maxCount="1"
          :multiple="false"
          accept=".jpg,.jpeg,.png"
          uploadText="点击上传"
          :customUpload="customUploadRequest"
          @change="handleChange"
        />
         <div class="upload-tip">
            * 支持JPEG、JPG、PNG等格式图标上传，单张图片不超过1M
          </div>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts" name="PassWordModal" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import repoUpload from '/@/components/Form/src/components/repoUpload.vue';
  import { formAnnountSchema } from './property.data';
  import { adjustSave, customUpload } from '/@/api/property/property';
  import { useMessage } from '/@/hooks/web/useMessage';
  const isUpdate = ref(false);
  const channelId = ref(undefined);
  const adjustmentVoucherFile = ref([]);
  const imgfileName = ref('');
  const imgfilePath = ref('');
  const { createMessage } = useMessage();
  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  //表单配置
  const [registerForm, formActionType] = useForm({
    schemas: formAnnountSchema,
    showActionButtonGroup: false,
    labelCol: { style: { width: '150px' } },
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    console.log(data,'data')
    //重置表单
    await formActionType.resetFields();

    setModalProps({ confirmLoading: false });
  
    if (typeof data.record === 'object') {
      channelId.value = data?.record?.channelId;
      formActionType.setFieldsValue({
        ...data.record,
       // channelId: data.record.channelId
      });
      adjustmentVoucherFile.value = []
    }
   
    formActionType.clearValidate();
  });
  async function handleChange(fileList){
    console.log(fileList,'fileList')
    let fileId = '';
    let fileName = '';
    let filePath = ''
    if (fileList && fileList.length > 0) {
      // 处理不同的数据结构
      if (fileList[0].response?.data?.result?.id) {
        fileId = fileList[0].response.data.result.id;
        fileName = fileList[0].response.data.result.name;
        filePath = fileList[0].response.data.result.path
      } else if (fileList[0].response?.result?.id) {
        fileId = fileList[0].response.result.id;
        fileName = fileList[0].response.result.name;
        filePath = fileList[0].response.result.path
      } else if (fileList[0].id) {
        fileId = fileList[0].id;
        fileName = fileList[0].name;
        filePath = fileList[0].path
      }
    }
    imgfileName.value = fileName
    imgfilePath.value = filePath
    console.log(fileId,'adjustmentVoucherFile',fileName,'filePath',filePath)
    await formActionType.setFieldsValue({
      adjustmentVoucherFile: fileId,
      adjustmentVoucherFileName: fileName,
      adjustmentVoucherFilePath: filePath
    }); 
    formActionType.clearValidate();
  }
    // 自定义上传请求
  const customUploadRequest = async (file) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'adjustmentVoucherFile');
      const res = await customUpload({ 
        file
      });
      // 检查返回结果中的 data 字段
      if (res.data?.code == 200) {
        return res
      }else{
        createMessage.error(res.data?.message);
      }
      console.log(res.data.message,'resss')
      return Promise.reject(new Error(res.data?.message || '上传失败'));
    } catch (error) {
      console.log(error,'error')
      return Promise.reject(error);
    }
  };
   //设置标题
  //表单提交事件
  async function handleSubmit() {
    try {
      const values = await formActionType.validate();
      console.log(values,'values')
      setModalProps({ confirmLoading: true });
     // let isUpdateVal = unref(isUpdate);
      //提交表单
      await adjustSave({...values,channelId: unref(channelId), adjustmentVoucherFileName: unref(imgfileName),adjustmentVoucherFilePath:unref(imgfilePath)} );
      //关闭弹窗
      closeModal();
      createMessage.success('操作成功')
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
<style lang="less" scoped>
  .upload-tip {
      margin-top: 8px;
      color: #999;
      font-size: 12px;
  }
</style>