import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';
import axios from 'axios';
import { getToken } from '/@/utils/auth';
enum Api {
  queryCommodityList = '/biz/commodity/query',
  batchUpdate = '/biz/commodity/batchUpdate',
  exportExcel = '/biz/commodity/exportExcel',
  updateCommodity = '/biz/commodity/updateCommodityInfo',
  setStatus = '/biz/commodity/setStatus',
  createCard = '/biz/commodity/batchCreateCard',
  addCommodity = '/biz/commodity/addCommodity',
  commodityDetail = '/biz/commodity/queryDetail',
  provinceList = '/biz/common/province',
  querySrcPrd = '/biz/common/querySrcPrd',
  getCommodityCode = '/biz/commodity/getCommodityCode',
  getMinSquadId = '/biz/commodity/getMinSquadId',
  queryProduct = '/biz/commodity/queryProduct',
  dependency = '/biz/common/dependency',
  //rsatest = '/sys/user/rsatest'
}

/**
 * 资产列表
 * @param params
 */
export const getCommodityListList = (params) => {
  return defHttp.post({ url: Api.queryCommodityList, params });
};

// export const getrsatest = (params) => {
//   return defHttp.post({ url: Api.rsatest, params });
// };
/**
 * 导出
 * @param params
 */
export const getExportExcel = (params) => {
  return defHttp.post(
    {
      url: Api.exportExcel,
      params,
      responseType: 'blob', // 设置响应类型为blob
    },
    {
      isReturnNativeResponse: true, // 返回原始响应以获取headers
    }
  );
};

/**
 * 新增
 * @param params
 */

export const addCommodity = (params = {}) => defHttp.post({ url: Api.addCommodity, params });
/**
 * 编辑
 * @param params
 */

export const updateCommodity = (params = {}) => defHttp.post({ url: Api.updateCommodity, params });

/**
 * 详情
 * @param params
 */

export const commodityDetail = (params = {}) => defHttp.get({ url: Api.commodityDetail, params });
/**
 * 省份
 * @param params
 */

export const getProvinceList = (params = {}) => defHttp.get({ url: Api.provinceList, params });
/**
 * 货源
 * @param params
 */

export const getSrcPrd = (params = {}) => defHttp.get({ url: Api.querySrcPrd, params });
/**
 * 商品ID
 * @param params
 */

export const getCommodityCode = (params = {}) => defHttp.get({ url: Api.getCommodityCode, params });
/**
 * 小组ID
 * @param params
 */

export const getGroupId = (params = {}) => defHttp.get({ url: Api.getMinSquadId, params });
/**
 * 产品
 * @param params
 */

export const getQueryProduct = (params = {}) => defHttp.get({ url: Api.queryProduct, params });

/**
 * 上下架
 * @param params
 */

export const commoditySetStatus = (params = {}) => defHttp.post({ url: Api.setStatus, params });
/**
 * 批量制卡
 * @param params
 */
export const commodityCreateCard = (params = {}) => defHttp.post({ url: Api.createCard, params });
/**
 * 自定义上传
 * @param customUpload
 */
// export const customUpload = (params) => {
//   defHttp.uploadFile({ url: Api.channelUpload }, params);
// };
// 文件上传 api
export const customUpload = ({ file }) => {
  console.log(file, 'filelllllll');
  const formData = new FormData();
  formData.append('myfiles3', file);
  const token = getToken();
  return axios({
    url: '/zqqy-manager' + Api.batchUpdate,
    method: 'POST',
    data: formData,
    headers: {
      'Content-type': 'multipart/form-data',
      Authorization: token,
      'X-Access-Token': token,
    },
  });
};

/**
 * 分省
 * @param params
 */

export const getDependency = (params = {}) => defHttp.get({ url: Api.dependency, params });
