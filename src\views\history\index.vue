<template>
  <PageWrapper contentFullHeight>
    <BasicTable @register="registerTable" />
  </PageWrapper>
</template>

<script>
  import { defineComponent, ref } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getHistoryListApi } from '/@/api/history';
  import { columns, searchFormSchema } from './columns/data';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  export default defineComponent({
    components: { BasicTable, PageWrapper },
    setup() {
      let selectedTime = ref([]);
      let moduleName = ref('');

      const [registerTable] = useTable({
        api: getHistoryListApi,
        columns,
        formConfig: {
          schemas: searchFormSchema,
          fieldMapToTime: [['operateTime', ['operaTimeStart', 'operaTimeEnd'], 'YYYY-MM-DD']],
          baseRowStyle: {
            width: '100%',
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'flex-end',
            alignItems: 'center',
            gap: '8px',
          },
          baseColProps: { style: { width: 'auto' } },
          showAdvancedButton: false,
          actionColOptions: { style: { width: 'auto' } },
        },
        useSearchForm: true,
        bordered: true,
        showIndexColumn: true,
      });

      return {
        registerTable,
        selectedTime,
        moduleName,
      };
    },
  });
</script>

<style lang="less" scoped>
  :deep(.jeecg-basic-table-header__toolbar) {
    width: auto;

    & > * {
      margin-right: 0;
    }
  }
</style>
