import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getAllRolesListNoByTenant } from '/@/api/sys/user';
import { rules } from '/@/utils/helper/validator';
import dayjs from 'dayjs';
export let sys_announcement_terminal = []
export let sys_announcement_announcement_type = []
export let sys_announcement_status = []
//展示终端
window.$getDicOptions('sys_announcement_terminal').then((data)=>{
  sys_announcement_terminal  = data
})
// 公告类型
window.$getDicOptions('sys_announcement_announcement_type').then((data)=>{
  sys_announcement_announcement_type  = data
})
// 公告状态
window.$getDicOptions('sys_announcement_status').then((data)=>{
  sys_announcement_status  = data
})
// 查询
export const searchFormSchema: FormSchema[] = [
  {
    label: '公告标题',
    field: 'title',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 20,
    }
  },
  {
    label: '最新发布时间',
    field: 'dateRangeSelect',
    colProps: { span: 10 },
    component: 'RangeDate',
    componentProps: {
        //是否显示时间
        // showTime: true,
        //日期格式
        valueFormat:"YYYY-MM-DD HH:mm:ss",
        //范围文本描述用集合
        placeholder:['请选择开始日期时间','请选择结束日期时间']
    },
  },
  {
    label: '展示终端',
    field: 'terminal',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_announcement_terminal',
      placeholder: '请选择展示终端',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '公告状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_announcement_status',
      placeholder: '请选择公告状态',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
];
export const columns: BasicColumn[] = [
  {
    title: '公告标题',
    dataIndex: 'title',
    width: 120,
  },
  {
    title: '公告类型',
    dataIndex: 'announcementType',
    width: 100,
    customRender: ({ text }) => {
      let result = sys_announcement_announcement_type.find((item)=>item.itemValue === text) || {}
      return result.label || text;
    },
    
  },
  {
    title: '展示终端',
    width: 150,
    dataIndex: 'terminal',
    customRender: ({ text }) => {
      let result = sys_announcement_terminal.find((item)=>item.itemValue === text) || {}
      return result.label || text;
    },
    
  },
  {
    title: '展示时间',
    dataIndex: 'showTime',
    width: 200,
    slots: { customRender: 'showTime' },
  },
  {
    title: '公告状态',
    dataIndex: 'statusName',
    width: 100
  },
  {
    title: '最新发布时间',
    dataIndex: 'updateTime',
    width: 150,
  },
  {
    title: '最新发布人',
    width: 100,
    dataIndex: 'createBy',
  },
  
];

export const formAnnountSchema: FormSchema[] = [
  {
    label: '公告标题',
    field: 'title',
    component: 'Input',
    componentProps: {
      maxLength: 20,
    },
    rules: [
      {
        required: true,
        message: '请输入公告标题',
        trigger: 'blur',
      }
    ],
  },
  {
    label: '公告类型',
    field: 'announcementType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_announcement_announcement_type',
      placeholder: '请选择公告类型',
      stringToNumber: true,
    },
    required: true
  },
  {
    label: '展示终端',
    field: 'terminal',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_announcement_terminal',
      placeholder: '请选择展示终端',
      stringToNumber: true,
    },
    required: true
  },
  {
    label: '展示时间',
    field: 'showtime',
    component: 'RangeDate',
    componentProps: {
        //是否显示时间
        showTime: true,
        format:'YYYY-MM-DD HH:mm:ss',
        //日期格式化
        valueFormat:"YYYY-MM-DD HH:mm:ss",
        //范围文本描述用集合
        placeholder:['请选择开始日期时间','请选择结束日期时间'],
        disabledDate:(currentDate)=>{
          return currentDate < dayjs().startOf('day');
        }
    },
    required: true
  },
  {
     label: '公告内容',
     field: 'content',
     component: 'JEditor',
     required: true,
     componentProps: {
       //是否禁用
       disabled: false,
       placeholder: '请输入公告内容',
     },
 },
];
