export default [
  {
    redirect: '/dashboard/analysis',
    path: '/dashboard',
    component: 'layouts/default/index',
    route: '1',
    children: [
      {
        path: '/dashboard/analysis',
        component: 'dashboard/Analysis',
        route: '1',
        meta: {
          internalOrExternal: false,
          icon: 'ant-design:bank-filled',
          componentName: 'Analysis',
          title: '首页',
        },
        name: 'dashboard-analysis',
        id: '9502685863ab87f0ad1134142788a385',
      },
      {
        path: '/dashboard/workbench',
        component: 'dashboard/workbench/index',
        route: '1',
        meta: {
          keepAlive: false,
          internalOrExternal: false,
          icon: 'ant-design:appstore-twotone',
          componentName: 'index',
          title: '工作台',
        },
        name: 'dashboard-workbench',
        id: '1438108176814825473',
      },
    ],
    meta: {
      keepAlive: false,
      internalOrExternal: false,
      icon: 'ion:grid-outline',
      componentName: 'index',
      title: 'Dashboard',
    },
    name: 'dashboard',
    id: '1438108176273760258',
  },
  {
    redirect: '/order',
    path: '/order',
    component: 'layouts/RouteView',
    route: '1',
    children: [
      {
        path: '/order/list',
        component: '/order/list/index',
        route: '1',
        meta: {
          keepAlive: true,
          internalOrExternal: false,
          icon: 'ant-design:bank-filled',
          componentName: 'order-list',
          title: '订单管理',
        },
        name: 'order-list',
        id: '9502685863ab87f0ad1134142788a3ssss89995',
      },
      {
        path: '/order/create',
        component: '/order/create/index',
        route: '1',
        meta: {
          internalOrExternal: false,
          icon: 'ant-design:bank-filled',
          componentName: 'order-detail',
          title: '创建订单',
        },
        name: 'order-create',
        id: '9502685863ab87f0ad1134142788a38699',
      },
      {
        path: '/order/detail',
        component: '/order/detail/index',
        route: '1',
        meta: {
          internalOrExternal: false,
          icon: 'ant-design:bank-filled',
          componentName: 'order-detail',
          title: '订单详情',
        },
        name: 'order-detail',
        id: '9502685863ab87f0ad1134142788a386',
      },
      {
        path: '/order/deal',
        component: 'order/deal/index',
        route: '1',
        meta: {
          internalOrExternal: false,
          icon: 'ant-design:bank-filled',
          componentName: 'order-deal',
          title: '处理订单',
        },
        name: 'order-deal',
        id: '9502685863ab87f0ad1134142788a3888',
      },
    ],
    meta: {
      keepAlive: false,
      internalOrExternal: false,
      icon: 'ion:grid-outline',
      componentName: 'index',
      title: '任务中心',
    },
    name: 'task',
    id: '1438108176273760258',
  },
  {
    path: '/bill/list',
    component: 'bill/index',
    route: '0',
    children: [
      {
        path: '/bill/upDetail',
        component: '/bill/detail/UpStreamDetail',
        route: '2',
        meta: {
          internalOrExternal: false,
          icon: 'ant-design:bank-filled',
          componentName: 'bill-detail',
          title: '账单详情',
        },
        hidden: true,
        name: 'bill-detail',
        id: '9502685863ab87f0ad1134142788a385',
      },
      {
        path: '/bill/downDetail',
        component: '/bill/detail/DownStreamDetail',
        route: '2',
        meta: {
          internalOrExternal: false,
          icon: 'ant-design:bank-filled',
          componentName: 'bill-detail',
          title: '账单详情',
        },
        hidden: true,
        name: 'bill-detail',
        id: '9502685863ab87f0ad1134142788a385',
      },
    ],
    meta: {
      keepAlive: false,
      internalOrExternal: false,
      icon: 'ion:grid-outline',
      componentName: 'bill',
      title: '账单管理',
    },
    name: 'bill',
    id: '1438108176273760258',
  },
  {
    redirect: null,
    path: '/system',
    component: 'layouts/RouteView',
    route: '1',
    children: [
      {
        path: '/system/user',
        component: 'system/user/index',
        route: '1',
        meta: {
          internalOrExternal: false,
          icon: 'ant-design:user',
          componentName: 'index',
          title: '用户管理',
        },
        name: 'system-user',
        id: '3f915b2769fc80648e92d04e84ca059d',
      },
      {
        path: '/system/role',
        component: 'system/role/index',
        route: '1',
        meta: {
          internalOrExternal: false,
          icon: 'ant-design:solution',
          componentName: 'index',
          title: '角色管理',
        },
        name: 'system-role',
        id: '190c2b43bec6a5f7a4194a85db67d96a',
      },
      {
        path: '/system/menu',
        component: 'system/menu/index',
        route: '1',
        meta: {
          keepAlive: false,
          internalOrExternal: false,
          icon: 'ant-design:menu-fold-outlined',
          componentName: 'index',
          title: '菜单管理',
        },
        name: 'system-menu',
        id: '1170592628746878978',
      },
      {
        path: '/system/depart',
        component: 'system/depart/index',
        route: '1',
        meta: {
          keepAlive: false,
          internalOrExternal: false,
          icon: 'ant-design:team',
          componentName: 'index',
          title: '部门管理',
        },
        name: 'system-depart',
        id: '45c966826eeff4c99b8f8ebfe74511fc',
      },
      {
        path: '/system/depart-user',
        component: 'system/departUser/index',
        route: '1',
        meta: {
          keepAlive: false,
          internalOrExternal: false,
          icon: 'ant-design:home-outlined',
          componentName: 'index',
          title: '我的部门',
        },
        name: 'system-depart-user',
        id: '5c2f42277948043026b7a14692456828',
      },
      {
        path: '/system/position',
        component: 'system/position/index',
        route: '1',
        meta: {
          keepAlive: false,
          internalOrExternal: false,
          icon: 'ant-design:database-filled',
          componentName: 'index',
          title: '职务管理',
        },
        name: 'system-position',
        id: '1438469604861403137',
      },
      {
        path: '/system/notice',
        component: 'system/notice/index',
        route: '1',
        meta: {
          keepAlive: false,
          internalOrExternal: false,
          icon: 'ant-design:bell-outlined',
          componentName: 'index',
          title: '通知公告',
        },
        name: 'system-notice',
        id: '1438782851980210178',
      },
      {
        path: '/system/category',
        component: 'system/category/index',
        route: '1',
        meta: {
          keepAlive: false,
          internalOrExternal: false,
          icon: 'ant-design:group-outlined',
          componentName: 'index',
          title: '分类字典',
        },
        name: 'system-category',
        id: '1438782530717495298',
      },
      {
        path: '/system/dict',
        component: 'system/dict/index',
        route: '1',
        meta: {
          keepAlive: false,
          internalOrExternal: false,
          icon: 'ant-design:hdd-twotone',
          componentName: 'index',
          title: '数据字典',
        },
        name: 'system-dict',
        id: '1438782641187074050',
      },
      {
        path: '/address',
        component: 'system/address/index',
        route: '1',
        meta: {
          keepAlive: false,
          internalOrExternal: false,
          icon: 'ant-design:book-outlined',
          componentName: 'index',
          title: '通讯录',
        },
        name: 'address',
        id: '1455735714507472898',
      },
      {
        path: '/system/tenant',
        component: 'system/tenant/index',
        route: '1',
        meta: {
          keepAlive: false,
          internalOrExternal: false,
          icon: 'ant-design:appstore-twotone',
          componentName: 'index',
          title: '租户管理',
        },
        name: 'system-tenant',
        id: '1280350452934307841',
      },
      {
        path: '/system/log',
        component: 'system/log/index',
        route: '1',
        meta: {
          keepAlive: false,
          internalOrExternal: false,
          icon: 'ant-design:insert-row-above-outlined',
          componentName: 'operation-log',
          title: '操作日志',
        },
        name: 'operation-log',
        id: '1280350452934307841',
      },
    ],
    meta: {
      keepAlive: false,
      internalOrExternal: false,
      icon: 'ant-design:setting',
      componentName: 'RouteView',
      title: '系统管理',
    },
    name: 'isystem',
    id: 'd7d6e2e4e2934f2c9385a623fd98c6f3',
  },
];
