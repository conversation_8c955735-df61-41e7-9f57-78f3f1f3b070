import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';

enum Api {
  exchangeOrderList = '/biz/equityExchangeOrder/pageList',
  getExcel = '/biz/equityExchangeOrder/getExcel',
  getDetail = '/biz/equityExchangeOrder/detail',
}

// export const getDictCheckList = (params) => {
//   return defHttp.post({ url: Api.dictCheckList, params });
// };

export const getExchangeOrderList = (params) => {
  return defHttp.post({ url: Api.exchangeOrderList, params });
};

export const getExChangeExcel = (params = {}) =>
  defHttp.post(
    {
      url: Api.getExcel,
      params,
      responseType: 'blob', // 设置响应类型为blob
    },
    {
      isReturnNativeResponse: true, // 返回原始响应以获取headers
    }
  );

export const getDetail = (params) => defHttp.get({ url: Api.getDetail, params });
