<template>
  <BasicModal 
  v-bind="$attrs" 
  :title="getTitle" 
  :defaultFullscreen="true"
  width="800px" 
  @register="registerModal" 
  @ok="handleSubmit">
    <a-card title="审批意见" v-if="!unref(isUpdate)" :bordered="false">
      <a-form :model="formState" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-item label="审批意见">
          <a-select style="height:100px" v-model:value="formState.desc" />
        </a-form-item>
        <a-form-item :wrapper-col="{ span: 14, offset: 10}">
          <a-button type="primary" @click="handleSubmit">通过</a-button>
          <a-button style="margin-left: 10px" @click="rejectClick">驳回</a-button>
        </a-form-item>
      </a-form>
    </a-card>
    <a-card title="集团客户基本信息" :bordered="false">
      <BasicForm @register="registerForm">
        <template #monnthTimes="{ model, field }">
          <JDictSelectTag type="select" v-model:value="model[field]" dictCode="sys_announcement_terminal" placeholder="请选择连续包月续订时间" />  
          <span style="color:red" class="font-color">
            提示：1、首次订购时间为集团客户充值时间。
            <br/>2、提前、推迟意为在首订时间（日-时:分:秒）的基础上提前或推迟xx分钟进行续充。
          </span>
        </template>
      </BasicForm>
    </a-card>
    <a-card title="IP信息列表" :bordered="false">
      <a-table :dataSource="ipInfoList" :columns="infocolumns" />
    </a-card>
    <a-card title="二级集团客户信息列表" v-if="!unref(isUpdate)" :bordered="false">
      <a-table :dataSource="secondInfoList" :columns="secondInfocolumns" />
    </a-card>
    <a-card title="审批流程" :bordered="false">
      <a-table :dataSource="dataSource" :columns="columns" />
    </a-card>
  </BasicModal>
</template>
<script lang="ts" name="PassWordModal" setup>
  import { ref, computed, unref, reactive, toRaw } from 'vue';
  import type { UnwrapRef } from 'vue';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  //import { BasicTable, TableRowSelection, useTable } from '/@/components/Table';
  import { formAnnountSchema } from './info.data';
  //import { adjustSave, customUpload } from '/@/api/property/property';
  import { useMessage } from '/@/hooks/web/useMessage';
  const isUpdate = ref(false);
  const rowId = ref(undefined);
  const showFooter = ref(true);
  const ipInfoList = ref<any[]>([]);
  const secondInfoList = ref<any[]>([]);
  const dataSource = ref<any[]>([]);
  interface FormState {
    desc: string;
  }
  const formState: UnwrapRef<FormState> = reactive({
    desc: '',
  });
  const labelCol = { style: { width: '100px' } };
  const wrapperCol = { span: 16 };
  const infocolumns = [
    { title: '集团客户ID', dataIndex: 'chanelid',align:'center' },
    { title: 'IP', dataIndex: 'ip',align:'center' },
  ];
  const columns = [
    { title: '审批环节', dataIndex: 'chanelid',align:'center' },
    { title: '处理时间', dataIndex: 'ip',align:'center' },
    { title: '处理结束', dataIndex: 'chanelid',align:'center' },
    { title: '处理人', dataIndex: 'ip',align:'center' },
    { title: '审批意见', dataIndex: 'ip',align:'center' },
  ]
  const secondInfocolumns = [
    { title: '集团客户ID', dataIndex: 'chanelid',align:'center' },
    { title: '二级集团客户名称', dataIndex: 'ip',align:'center' },
    { title: '二级集团客户ID', dataIndex: 'ip',align:'center' },
  ]
  const { createMessage } = useMessage();
  //设置标题
 const getTitle = computed(() => (!unref(isUpdate) ? '审批' : '浏览'));
  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  //表单配置
  const [registerForm, formActionType] = useForm({
    schemas: formAnnountSchema,
    showActionButtonGroup: false,
    labelCol: { style: { width: '150px' } }
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    console.log(data,'data')
   // reload();
    //重置表单
    await formActionType.resetFields();
    showFooter.value = data?.showFooter ?? true;
    setModalProps({ confirmLoading: false, showOkBtn: showFooter.value });
    isUpdate.value = !!data?.isUpdate;
    if (unref(isUpdate)) {
      rowId.value = data?.record?.id;
      //表单赋值
      // await setFieldsValue({
      //   ...data.record,
      // });
    }else{
       rowId.value = undefined;
    }
    console.log(data.record,'record')
    if (unref(isUpdate)) {
      let startTime = data?.record?.startTime
      let endTime = data?.record?.endTime
      console.log(startTime,'startTime',endTime)
      formActionType.setFieldsValue({
       // ...data.record,
        title: data?.record?.title,
        announcementType: Number(data?.record?.announcementType),
        terminal:  Number(data?.record?.terminal),
        content: data?.record?.content,
        showtime: startTime+','+endTime
      });
    }
    formActionType.setProps({ disabled: !showFooter.value });
    formActionType.clearValidate();
  });

   //设置标题
  //表单提交事件
  async function handleSubmit() {
    try {
      const values = await formActionType.validate();
      console.log(values,'values')
      setModalProps({ confirmLoading: true });
      
      // let isUpdateVal = unref(isUpdate);
      //提交表单
      // await adjustSave({...values,channelId: unref(channelId), adjustmentVoucherFileName: unref(imgfileName),adjustmentVoucherFilePath:unref(imgfilePath)} );
      //关闭弹窗
      closeModal();
      createMessage.success('操作成功')
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
  async function rejectClick(){
    try {
      const values = await formActionType.validate();
      console.log(values,'values')
      setModalProps({ confirmLoading: true });
      
      // let isUpdateVal = unref(isUpdate);
      //提交表单
      // await adjustSave({...values,channelId: unref(channelId), adjustmentVoucherFileName: unref(imgfileName),adjustmentVoucherFilePath:unref(imgfilePath)} );
      //关闭弹窗
      closeModal();
      createMessage.success('操作成功')
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
<style lang="less" scoped>
  .upload-tip {
      margin-top: 8px;
      color: #999;
      font-size: 12px;
  }
</style>