<template>
  <PageWrapper contentFullHeight>
    <BasicTable @register="registerTable" />
  </PageWrapper>
</template>
<script>
  import { defineComponent } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { logListApi } from '/@/api/logs';
  import { columns, searchFormSchema } from './columns/data';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  export default defineComponent({
    components: { BasicTable, PageWrapper },
    setup() {
      const [registerTable] = useTable({
        api: logListApi,
        columns,
        formConfig: {
          labelWidth: 100,
          schemas: searchFormSchema,
          fieldMapToTime: [['operateTime', ['operaTimeStart', 'operaTimeEnd'], 'YYYY-MM-DD']],
          actionColOptions: {
            span: 6,
            style: { display: 'inline-block', marginLeft: '4px' },
          },
        },
        useSearchForm: true,
        bordered: true,
        showIndexColumn: true,
      });
      return {
        registerTable,
      };
    },
  });
</script>
