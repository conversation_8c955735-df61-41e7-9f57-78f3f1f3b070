<template>
  <div class="upload-container">
    <a-upload
      v-model:file-list="fileList"
      :before-upload="handleBeforeUpload"
      :customRequest="handleCustomUpload"
      :accept="accept"
      :multiple="multiple"
      :maxCount="maxCount"
      :list-type="listType"
      @preview="handlePreview"
      @remove="handleRemove"
    >
      <a-button>
        <upload-outlined></upload-outlined>
        {{ uploadText }}
      </a-button>
    </a-upload>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { useMessage } from '/@/hooks/web/useMessage';

const props = defineProps({
  value: {
    type: [String, Array],
    default: '',
  },
  // 最大上传数量
  maxCount: {
    type: Number,
    default: 1,
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: false,
  },
  // 接受的文件类型
  accept: {
    type: String,
    default: '.jpg,.jpeg,.png,.pdf',
  },
  // 上传按钮文字
  uploadText: {
    type: String,
    default: '上传',
  },
  // 列表类型
  listType: {
    type: String,
    default: 'picture',
  },
  // 自定义上传前的处理函数
  beforeUpload: {
    type: Function,
    default: null,
  },
  // 自定义上传函数
  customUpload: {
    type: Function,
    default: null,
  },
  // 自定义删除函数
  customRemove: {
    type: Function,
    default: null,
  },
  // 自定义预览函数
  customPreview: {
    type: Function,
    default: null,
  },
  // 初始文件列表
  defaultFileList: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['update:value', 'change', 'success', 'error']);
const { createMessage } = useMessage();

const fileList = ref<any[]>([]);

// 监听外部值变化
watch(
  () => props.value,
  (val) => {
    if (val) {
      // 如果是字符串，尝试解析JSON
     if (Array.isArray(val)) {
        try {
          fileList.value = val.map(file => {
            let result = {
              uid:file.id || file.uid || `-${Date.now()}`,
              name: file.name || file.fileName || 'file',
              status: 'done',
            }
            console.log(file,'file')
            if(file.filePath){
              result.url =  file.bytes ? `data:image/png;base64,${file.bytes}` : file.url
              result.response = { ...file }
            } else if(file.response.data) {
              result.url = file.response.data.result.bytes ? `data:image/png;base64,${file.response.data.result.bytes}` : file.response.data.result.url
              result.response = { ...file.response.data.result }
            } else {
              result.url = file.url
              result.response = { ...file.response }
            }
            return result
          });
        } catch (error) {
          console.log("🚀 ~ error:", error)
        }
      }
    } else {
      fileList.value = [];
    }
  },
  { immediate: true }
);

// 处理上传前的验证
const handleBeforeUpload = async (file: File) => {
  try {
    // 如果有自定义的上传前处理
    if (props.beforeUpload) {
      const canUpload = await props.beforeUpload(file);
      if (!canUpload) {
        return false;
      }
    }
    return true;
  } catch (error) {
    createMessage.error('文件验证失败');
    return false;
  }
};

// 处理自定义上传
const handleCustomUpload = async ({ file, onSuccess, onError, onProgress }) => {
  
  try {
    // 如果有自定义上传函数，使用自定义上传
    if (props.customUpload) {
      console.log(file,'props.',onProgress)
      const res = await props.customUpload(file, onProgress);
      console.log(res,'33333resresres')
      if (res.data.success) {
        const fileInfo = {
          uid: file.uid,
          name: file.name,
          status: 'done',
          url: res.data.result.url,
          response: res.data.result,
        };
        // 更新文件列表
        if (props.maxCount === 1) {
          fileList.value = [fileInfo];
        } else {
          fileList.value = [...fileList.value, fileInfo];
        }
        onSuccess(res);
        emit('success', res);
        // emit('update:value', fileList.value);
        emit('change', fileList.value);
        return res;
      } else {
        console.log(res,'rrrrr')
        const error = new Error(res.data.message || '上传失败');
        onError(error);
        emit('error', error);
        createMessage.error(res.data.message || '上传失败');
        return Promise.reject(error);
      }
    }

    // 默认上传处理
    const mockResult = {
      success: true,
      url: URL.createObjectURL(file),
      message: 'Upload success',
    };
    onSuccess(mockResult);
    return mockResult;
  } catch (error) {
    console.log(error,'error')
    onError(error);
    emit('error', error);
   // createMessage.error('上传失败');
    return Promise.reject(error);
  }
};

// 处理文件删除
const handleRemove = async (file: any) => {
  try {
    // 如果有自定义删除函数，使用自定义删除
    if (props.customRemove) {
      const res = await props.customRemove(file);
      if (res !== false) {
        // 从文件列表中移除对应文件
        const newFileList = fileList.value.filter(item => {
          // 对于新上传的文件，通过 uid 匹配
          if(item.response?.id && file.response?.id){
            return item.response?.id !== file.response?.id;
          }
          return item.uid !== file.uid;
        }).map(item => {
          // 确保所有文件的状态和预览URL正确
          const bytes = item.response?.bytes;
          return {
            ...item,
            status: 'done',
            url: bytes ? `data:image/png;base64,${bytes}` : item.url
          };
        });

        fileList.value = [...newFileList];
        emit('update:value', newFileList);
        emit('change', newFileList);
      }
      return res;
    }

    // 默认删除处理
    const newFileList = fileList.value.filter(item => item.uid !== file.uid);
    fileList.value = [...newFileList];
    emit('update:value', newFileList);
    emit('change', newFileList);
    return true;
  } catch (error) {
    createMessage.error('删除失败');
    return false;
  }
};

// 处理文件预览
const handlePreview = (file: any) => {
  // 如果有自定义预览函数，使用自定义预览
  if (props.customPreview) {
    props.customPreview(file);
    return;
  }

  // 默认预览处理
  if (file.response?.result?.bytes) {
    // 如果有base64数据，使用base64预览
    const previewUrl = 'data:image/png;base64,' + file.response.result.bytes;
    window.open(previewUrl);
  } else if (file.url) {
    // 否则使用url预览
    window.open(file.url);
  }
};

</script>

<style lang="less" scoped>
.upload-container {
  :deep(.ant-upload-list-picture-card-container) {
    width: 104px;
    height: 104px;
    margin: 0 8px 8px 0;
  }

  :deep(.ant-upload-list) {
    margin-top: 8px;
  }

  :deep(.ant-upload-list-picture) {
    .ant-upload-list-item {
      padding: 8px;
      border: 1px solid #d9d9d9;
      border-radius: 2px;

      &:hover {
        background-color: #f5f5f5;
      }

      .ant-upload-list-item-thumbnail {
        width: 48px;
        height: 48px;
        img {
          object-fit: cover;
        }
      }
    }
  }

  :deep(.ant-upload-list-picture-card) {
    .ant-upload-list-item {
      padding: 8px;
      border: 1px solid #d9d9d9;

      .ant-upload-list-item-thumbnail {
        img {
          object-fit: cover;
        }
      }
    }
  }
}
</style>