<template>
  <PageWrapper>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'system:banner:add'" preIcon="ant-design:plus-outlined" @click="handleCreate({})"> 新增</a-button>
      </template>
      <template #imageUrl="{ text }">
        <TableImg :size="60" :simpleShow="true" :imgList="[text]" />
      </template>
      <template #status="{ text }">
        <a-tag :color="+text === 0 ? 'error' : 'success'">{{ +text === 0 ? '已停用' : '启用中' }}</a-tag>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 验收弹窗 -->
    <!-- <CheckModel @register="registerCheckModal" @success="handleSuccess" /> -->
    <BannerModel @register="registerPropertyModal" @success="handleSuccess" />
  </PageWrapper>
</template>

<script lang="ts" name="system-banner" setup>
  import { reactive } from 'vue';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction, ActionItem, TableImg } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { postBannerList, postStatusCheck } from '/@/api/sys/banner';
  import { columns } from './components/banner.data';
  import { Modal } from 'ant-design-vue';
  import { useModal } from '/@/components/Modal';
  import BannerModel from './components/bannerModel.vue';
  const [registerPropertyModal, { openModal }] = useModal();
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'banner-list',
    tableProps: {
      api: postBannerList,
      rowSelection: {},
      columns: columns,
      size: 'small',
      showIndexColumn: true,
      useSearchForm: false, // 是否使用搜索项
      actionColumn: {
        width: 120,
      },
      beforeFetch: (params) => {
        delete params.column;
        delete params.order;
        // return Object.assign({ }, params);
      },
    },
  });

  //注册table数据
  const [registerTable, { reload }] = tableContext;
  let recordData = reactive({});
  function handleCreate(record: Recordable) {
    Object.assign(recordData, record);
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  function handleDetail(record: Recordable) {
    Object.assign(recordData, record);
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  function handleEnable(record: Recordable) {
    Modal.confirm({
      title: +record.status === 1 ? '确定要停用这个Banner图配置内容吗?' : '确定要启用这个Banner图配置内容吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        postStatusCheck({ bannerId: record.bannerId, status: +record.status ? 0 : 1 }).then((res) => {
          if (res) {
            reload();
          }
        });
      },
    });
  }
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleDetail.bind(null, record),
        auth: 'system:banner:edit',
      },
      {
        label: '启用',
        onClick: handleEnable.bind(null, record),
        ifShow: +record.status === 0,
        auth: 'system:banner:enable',
      },
      {
        label: '停用',
        onClick: handleEnable.bind(null, record),
        ifShow: +record.status === 1,
        auth: 'system:banner:disabled',
      },
    ];
  }
  function handleSuccess() {
    reload();
  }
</script>
