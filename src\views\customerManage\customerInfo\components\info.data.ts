import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getAllRolesListNoByTenant } from '/@/api/sys/user';
import { rules } from '/@/utils/helper/validator';
export let adjustment_sub_type = []
window.$getDicOptions('adjustment_sub_type').then((data)=>{
  adjustment_sub_type  = data
})
// 查询
export const searchFormSchema: FormSchema[] = [
  {
    label: '账户ID',
    field: 'channelId',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 20,
    }
  },
  {
    label: '集团客户名称',
    field: 'channelName',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 20,
    }
  },
  {
    label: '接入模式',
    field: 'terminal',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_announcement_terminal',
      placeholder: '请选择接入模式',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '业务类型',
    field: 'terminal',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_announcement_terminal',
      placeholder: '请选择业务类型',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '审批状态',
    field: 'terminal',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_announcement_terminal',
      placeholder: '请选择审批状态',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '集团客户状态',
    field: 'terminal',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_announcement_terminal',
      placeholder: '请选择审批状态',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  }, 
  {
    label: '地区',
    field: 'area',
    component: 'Cascader',
    componentProps: {
      maxTagCount:2,
        //浮层预设位置
      placement: 'bottomLeft',
      options: [
        {
          label: '北京',
          value: 'BeiJin',
          children: [
            {
              label: '海淀区',
              value: 'HaiDian',
            },
          ],
        },
        {
          label: '江苏省',
          value: 'JiangSu',
          children: [
            {
              label: '南京',
              value: 'Nanjing'
            },
          ],
        },
      ],

    },
    colProps: { span: 6 },
  },
  {
    label: '行业类型',
    field: 'type',
    component: 'Cascader',
    componentProps: {
      maxTagCount:2,
        //浮层预设位置
      placement: 'bottomLeft',
      options: [
        {
          label: '北京',
          value: 'BeiJin',
          children: [
            {
              label: '海淀区',
              value: 'HaiDian',
            },
          ],
        },
        {
          label: '江苏省',
          value: 'JiangSu',
          children: [
            {
              label: '南京',
              value: 'Nanjing'
            },
          ],
        },
      ],

    },
    colProps: { span: 6 },
  },
  
];
export const columns: BasicColumn[] = [
  {
    title: '账户ID',
    dataIndex: 'channelId',
    width: 120,
  },
  {
    title: '集团客户名称',
    width: 150,
    dataIndex: 'channelName',
  },
  {
    title: '地区',
    dataIndex: 'groupId',
    width: 100,
  },
  {
    title: '联系人',
    dataIndex: 'currBalance',
    width: 100,
  },
  {
    title: '电话',
    dataIndex: 'lockedMoney',
    width: 100
  },
  {
    title: '邮箱',
    dataIndex: 'accuMoney',
    width: 100
  },
  {
    title: '状态',
    width: 150,
    dataIndex: 'orderedMoney'
  },
  {
    title: '业务类型',
    dataIndex: 'accuMoney',
    width: 100
  },
  {
    title: '审批状态',
    width: 150,
    dataIndex: 'orderedMoney'
  },
];

export const formAnnountSchema: FormSchema[] = [
  {
    label: '集团客户名称',
    field: 'channelName',
    component: 'Input',
    componentProps: {
     
    },
    required: true,
    colProps: { span: 12 },
  },
  {
    label: '开户行',
    field: 'currBalance',
    component: 'Input',
    required: true,
    colProps: { span: 12},
  },
  {
    label: '公司地址',
    field: 'currBalance',
    component: 'Input',
    colProps: { span: 12},
  },
  {
    label: '账户名称',
    field: 'currBalance',
    component: 'Input',
    colProps: { span: 12},
    required: true
  },
  {
    label: '联系人',
    field: 'currBalance',
    component: 'Input',
    colProps: { span: 12},
    required: true
  },
  {
    label: '银行账号',
    field: 'currBalance',
    component: 'Input',
    colProps: { span: 12},
    required: true
  },
  {
    label: '联系电话',
    field: 'currBalance',
    component: 'Input',
    colProps: { span: 12},
    required: true
  },
  {
    label: 'ip白名单',
    field: 'currBalance',
    component: 'Input',
    colProps: { span: 12},
  },
  {
    label: '联系邮箱',
    field: 'currBalance',
    component: 'Input',
    colProps: { span: 12},
    required: true
  },
  {
    label: '回调地址',
    field: 'currBalance',
    component: 'Input',
    colProps: { span: 12},
  },
  {
    label: '地区',
    field: 'area',
    component: 'Cascader',
    componentProps: {
      maxTagCount:2,
        //浮层预设位置
      placement: 'bottomLeft',
      options: [
        {
          label: '北京',
          value: 'BeiJin',
          children: [
            {
              label: '海淀区',
              value: 'HaiDian',
            },
          ],
        },
        {
          label: '江苏省',
          value: 'JiangSu',
          children: [
            {
              label: '南京',
              value: 'Nanjing'
            },
          ],
        },
      ],

    },
    colProps: { span:12 },
  },
  {
    label: '行业类型',
    field: 'type',
    component: 'Cascader',
    componentProps: {
      maxTagCount:2,
        //浮层预设位置
      placement: 'bottomLeft',
      options: [
        {
          label: '北京',
          value: 'BeiJin',
          children: [
            {
              label: '海淀区',
              value: 'HaiDian',
            },
          ],
        },
        {
          label: '江苏省',
          value: 'JiangSu',
          children: [
            {
              label: '南京',
              value: 'Nanjing'
            },
          ],
        },
      ],

    },
    colProps: { span: 12 },
  },
  {
    label: '连续包月续订时间',
    field: 'monnthTimes',
    component: 'JDictSelectTag',
    //  componentProps: {
    //     dictCode: 'sys_announcement_terminal',
    //     placeholder: '请选择连续包月续订时间',
    //     stringToNumber: true,
    //  },
    slot: 'monnthTimes',
    colProps: { span: 12 },
  },
  {
    label: '业务类型',
    field: 'terminal',
    component: 'RadioGroup',
      componentProps: {
        //options里面由一个一个的radio组成,支持disabled
        options: [
          { label: '预付费模式', value: 0 },
          { label: '直营模式', value: 1 },
          { label: '营销模式', value: 2 },
          { label: '自有模式', value: 3 },
        ],
      },
    required: true,  
    colProps: { span: 24 },
  },
  {
    label: '接入模式',
    field: 'terminal',
    component: 'RadioGroup',
      componentProps: {
        //options里面由一个一个的radio组成,支持disabled
        options: [
          { label: '接口', value: 0 },
          { label: 'h5', value: 1 }
        ],
      },
    colProps: { span: 24 },
  },
  {
    label: '结算周期',
    field: 'terminal',
    component: 'JDictSelectTag',
      componentProps: {
        //options里面由一个一个的radio组成,支持disabled
        options: [
          { label: '+4', value: '4' },
          { label: '+3', value: '3' },
          { label: '+2', value: '2' },
          { label: '+1', value: '1' },
          { label: '0', value: '0' },
          { label: '-1', value: '-1' },
          { label: '-2', value: '-2' },
          { label: '-3', value: '-3'},
          { label: '-4', value: '-4' },
        ],
      },
    colProps: { span: 24 },
  },
  {
    label: '扣减规则',
    field: 'terminal',
    component: 'RadioGroup',
      componentProps: {
        //options里面由一个一个的radio组成,支持disabled
        options: [
          { label: '资金', value: 0 }
        ],
      },
    colProps: { span: 24 },
  },
  {
    label: '接口收单规则',
    field: 'terminal',
    component: 'RadioGroup',
      componentProps: {
        //options里面由一个一个的radio组成,支持disabled
        options: [
          { label: '月末正常收单', value: 0 },
          { label: '月末最后一天不收单', value: 1 },
          { label: '月末最后两天不收单', value: 2 },
          { label: '月末最后三天不收单', value: 3 },
        ],
      },
    colProps: { span: 24 },
  },
];
export const adustColumns: BasicColumn[] = [
  {
    title: '调账时间',
    dataIndex: 'createTime',
    width: 120,
  },
  {
    title: '调账原因',
    dataIndex: 'subtype',
    width: 100,
    customRender: ({ text }) => {
      let result = adjustment_sub_type.find((item)=>item.value === text) || {}
      return result.label || text;
    },
  },
  {
    title: '调账前资金余额',
    width: 150,
    dataIndex: 'preMoneyStr',
  },
  {
    title: '调账金额',
    dataIndex: 'adjustmentAmtStr',
    width: 100,
  },
  {
    title: '调账类型',
    dataIndex: 'mainType',
    width: 100,
    customRender: ({ text }) => {
      return text == 1 ? '减少资金金额': '增加资金金额';
    },
  },
  {
    title: '调账凭证',
    dataIndex: 'adjustmentVoucherFileName',
    width: 150,
    slots: { customRender: 'adjustmentVoucherFileName' },
  },
  {
    title: '操作账号',
    dataIndex: 'createBy',
    width: 100,
  }
];
// 查询
export const searchRecordFormSchema: FormSchema[] = [
  {
    label: '调账时间',
    field: 'createTime',
    component: 'RangePicker',
    colProps: { span: 10 },
    componentProps: {
      //是否显示时间
    //  showTime: true,
      //日期格式化
      valueFormat:"YYYY-MM-DD",
      //范围文本描述用集合
      placeholder:['请选择开始日期时间','请选择结束日期时间']
    },
  },
  {
    label: '调账原因',
    field: 'subtype',
    colProps: { span: 8 },
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'adjustment_sub_type',
      placeholder: '请选择调账原因',
      stringToNumber: true,
    },
  },
];
