import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import dayjs from 'dayjs';

let srcOrderType = [];
window.$getDicOptions('src_order_send_type').then((data) => {
  srcOrderType = data;
});

export const columns: BasicColumn[] = [
  // {
  //   title: '订单ID',
  //   dataIndex: 'orderId',
  //   customRender: ({ text }) => text || '—',
  //   width: 200,
  // },
  {
    title: '订单号',
    dataIndex: 'srcOrderId',
    width: 300,
  },
  {
    title: '订单类型',
    dataIndex: 'sendType',
    customRender: ({ text }) => {
      return srcOrderType.find((item: any) => item.value === text)?.label || text || '—';
    },
  },
  {
    title: '货源名称',
    dataIndex: 'srcPrdName',
    customRender: ({ text }) => text || '—',
  },
  {
    title: '供应商名称',
    dataIndex: 'srcName',
  },
  {
    title: '手机号',
    dataIndex: 'msisdn',
    // customRender: ({ text }) => atob(text),
  },
  {
    title: '订单总金额（元）',
    dataIndex: 'orderMoney',
  },
  {
    title: '通知地址',
    dataIndex: 'callBackUrl',
    customRender: ({ text }) => text || '—',
  },
  {
    title: '回调信息',
    dataIndex: 'resultMessage',
    // customRender:({ text }) => +text === 1 ? '启用中' : '已停用',
  },
  {
    title: '创建时间',
    dataIndex: 'srcOrderTime',
  },
];

export const searchFormSchema: FormSchema[] = [
  // {
  //   label: 'ID',
  //   field: 'id',
  //   component: 'Input',
  //   colProps: { span: 8 },
  // },
  {
    label: '订单号',
    field: 'srcOrderId',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '手机号',
    field: 'msisdn',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '创建时间',
    field: 'dateOp',
    component: 'RangeDate',
    componentProps: {
      //是否显示时间
      // showTime: true,
      format: 'YYYY-MM-DD',
      //日期格式化
      valueFormat: 'YYYY-MM-DD',
      //范围文本描述用集合
      placeholder: ['开始时间', '结束时间'],
      // disabledDate: (currentDate) => {
      //   return currentDate < dayjs().startOf('day');
      // },
    },
    colProps: { span: 8 },
  },
];
