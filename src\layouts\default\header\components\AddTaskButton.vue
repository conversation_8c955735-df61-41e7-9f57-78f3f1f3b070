<template>
  <div class="mr-1">
    <a-button v-if="showButton" :id="prefixCls" type="primary" size="small" @click="handleAdd">提任务</a-button>
  </div>
</template>
<script lang="ts" setup>
  import { watch, ref } from 'vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useRouter, useRoute } from 'vue-router';
  const { prefixCls } = useDesign('header-add-button');
  const router = useRouter();
  const route = useRoute();
  const showButton = ref<boolean>(true);
  watch(
    () => route.fullPath,
    () => {
      const fullPath = route.fullPath;
      if (fullPath === '/task/create') {
        showButton.value = false;
      } else {
        showButton.value = true;
      }
    }
  );
  function handleAdd() {
    router.push({ path: '/task/create' });
  }
</script>
<style lang="less"></style>
