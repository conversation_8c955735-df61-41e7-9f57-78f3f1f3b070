#app {
  width: 100%;
  height: 100%;
}

// ==============scrollbar==========

::-webkit-scrollbar {
  width: 7px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.05);
}

::-webkit-scrollbar-thumb {
  // background: rgba(0, 0, 0, 0.6);
  background-color: rgba(144, 147, 153, 0.3);
  // background-color: rgba(144, 147, 153, 0.3);
  border-radius: 2px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb:hover {
  background-color: @border-color-dark;
}

[data-theme='dark'] {
  ::-webkit-scrollbar-thumb:hover {
    background-color: #5e6063;
  }
}

// =================================
// ==============nprogress==========
// =================================
#nprogress {
  pointer-events: none;

  .bar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99999;
    width: 100%;
    height: 2px;
    background-color: @primary-color;
    opacity: 0.75;
  }
}

// =======================================
// ============ [sjl] 按钮组样式 ==========
// =======================================
.j-table-operator {
  // Button按钮间距
  .ant-btn {
    margin: 0 8px 8px 0;
    transition: margin 0s;
  }

  & > .ant-btn:last-of-type {
    margin: 0 0 8px 0;
  }

  .ant-btn-group,
  &.ant-btn-group {
    .ant-btn {
      margin: 0;
      transition: margin 0s;
    }

    & > .ant-btn:last-of-type {
      margin: 0 8px 8px 0;
    }
  }
}

// ========================================
// ============ [sjl] 底部按钮样式 ==========
// ========================================
.j-box-bottom-button {
  height: 28px;

  &-float {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    background: #fff;
    border-radius: 0 0 2px 2px;

    & .ant-btn {
      margin-left: 8px;
    }
  }

  &.offset-20 &-float {
    left: -20px;
    right: -20px;
    bottom: -20px;
  }
}
