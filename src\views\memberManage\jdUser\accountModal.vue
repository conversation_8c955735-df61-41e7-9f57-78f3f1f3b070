<template>
  <BasicModal v-bind="$attrs" :title="'账户明细'" :defaultFullscreen="true" width="800px" :showOkBtn="false" @register="registerModal">
    <!-- <h3>兑换权益明细</h3>
    <p>兑换权益总数量8，兑换成功5，兑换失败3，兑换应支付总金额50.00</p>
    <BasicTable @register="exchangeTable" />
    <h3>支付卡券明细</h3> -->
    <!-- <BasicTable @register="payTable" /> -->
    <a-table :dataSource="dataSource" :columns="accountColumns" :loading="loading" :pagination="pagination" @change="handleTableChange">
      <template #dealType="{ text }"> {{ getDicNameSync(text) }}</template>
    </a-table>
  </BasicModal>
</template>
<script lang="ts">
  // 单独添加一个script块用于导出
  export default {
    name: 'memberManage-accountModal', // 保持与你原来的name一致
  };
</script>
<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { getEtxLog } from '/@/api/member-manage/jd-user';
  import { accountColumns } from './jdUser.data';
  import { getDicOptions } from '/@/utils/index';
  import type { TablePaginationConfig } from 'ant-design-vue';

  // 字典缓存
  const dictCache: Record<string, any[]> = {};

  // ✅ 在组件内正确使用生命周期
  onMounted(async () => {
    dictCache.app_id = await getDicOptions('external_channels_changeType');
  });

  function getDicNameSync(value: number) {
    const items = dictCache['app_id'];
    const item = items?.find((item) => item.value == value);
    return item?.label || value;
  }
  const emit = defineEmits(['success', 'register']);
  let dataSource = reactive<any[]>([]);
  const acctId = ref<string>('');
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    acctId.value = data?.record?.acctId || 0;
    getList();
  });

  const loading = ref<boolean>(false);
  const pagination = ref<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条`,
    pageSizeOptions: ['10', '20', '50'],
  });

  // 表格变化处理
  const handleTableChange = (pag: TablePaginationConfig, filters: Record<string, any>, sorter: any) => {
    getList();
  };
  async function getList() {
    let params = {
      pageNo: pagination.value.current,
      pageSize: pagination.value.pageSize,
      acctId: acctId.value,
    };
    try {
      loading.value = true;
      const result = await getEtxLog(params);
      dataSource.length = 0;
      dataSource.push(...(result.records || []));
      pagination.value.total = result.total;
    } catch (e) {
    } finally {
      loading.value = false;
    }
  }
</script>
<style scoped>
  .ml8 {
    margin-left: 8px;
  }
  .tips {
    color: #999;
  }
</style>
