<template>
  <RouterView>
    <template #default="{ Component, route }">
      <keep-alive v-if="openCache" :include="getCaches" :max="10">
        <component :is="Component" :key="route.fullPath" />
      </keep-alive>
      <component :is="Component" v-else :key="route.fullPath" />
    </template>
  </RouterView>
</template>
<script lang="ts">
  import { computed, defineComponent, ref, unref } from 'vue';
  import { useRootSetting } from '/@/hooks/setting/useRootSetting';
  import { useTransitionSetting } from '/@/hooks/setting/useTransitionSetting';
  import { useMultipleTabSetting } from '/@/hooks/setting/useMultipleTabSetting';
  import { getTransitionName } from './transition';
  import { REDIRECT_NAME } from '/@/router/constant';
  import { listenerRouteChange } from '/@/logics/mitt/routeChange';
  import { RouteLocationNormalized } from 'vue-router';
  import { useUserStore } from '/@/store/modules/user';
  export default defineComponent({
    name: 'PageLayout',
    setup() {
      const { getShowMultipleTab } = useMultipleTabSetting();
      const userStore = useUserStore();
      const { getOpenKeepAlive, getCanEmbedIFramePage } = useRootSetting();
      const { getBasicTransition, getEnableTransition } = useTransitionSetting();
      const openCache = computed(() => {
        return unref(getOpenKeepAlive) && unref(getShowMultipleTab);
      });
      const caches = ref<Recordable>({});
      listenerRouteChange((route: RouteLocationNormalized) => {
        const rawCahces = { ...unref(caches) };
        const { path, name } = route;
        if (name === REDIRECT_NAME || !route || !userStore.getToken) {
          return;
        }
        if (!route?.meta?.ignoreKeepAlive) {
          const routeName = route.name as string;
          rawCahces[routeName] = path;
        }
        const newCache: Recordable = {};
        Object.keys(rawCahces).forEach((v) => {
          let p = rawCahces[v];
          if (path.startsWith(p)) {
            newCache[v] = p;
          }
        });
        caches.value = newCache;
      });
      const getCaches = computed(() => {
        return Object.keys(caches.value);
      });
      return {
        getTransitionName,
        openCache,
        getEnableTransition,
        getBasicTransition,
        getCaches,
        getCanEmbedIFramePage,
      };
    },
  });
</script>
