<template>
  <LoginFormTitle class="enter-x" />
  <Form ref="formRef" class="p-4 enter-x" :model="formData" :rules="getFormRules" @keypress.enter="handleLogin">
    <FormItem name="account" class="enter-x">
      <Input v-model:value="formData.account" size="large" placeholder="请输入登录账号" class="fix-auto-fill" />
    </FormItem>
    <FormItem name="pawrd" class="enter-x">
      <InputPassword v-model:value="formData.pawrd" size="large" visibilityToggle placeholder="请输入登录密码" />
    </FormItem>
    <!--验证码-->
    <ARow class="enter-x">
      <ACol :span="12">
        <FormItem name="inputCode" class="enter-x">
          <Input v-model:value="formData.inputCode" size="large" placeholder="请输入验证码" style="min-width: 100px" />
        </FormItem>
      </ACol>
      <ACol :span="8">
        <FormItem class="ml-2 text-right enter-x">
          <img v-if="randCodeData.requestCodeSuccess" class="mt-2px" alt="验证码" :src="randCodeData.randCodeImage" @click="handleChangeCheckCode" />
          <img v-else class="mt-2px" :src="checkCodePng" alt="验证码" @click="handleChangeCheckCode" />
        </FormItem>
      </ACol>
    </ARow>
    <FormItem class="enter-x">
      <Button type="primary" size="large" block :loading="loading" @click="handleLogin">登录</Button>
    </FormItem>
  </Form>
</template>
<script lang="ts" setup>
  import { reactive, ref, toRaw, onMounted } from 'vue';
  import { Form, Input, Row, Col, Button } from 'ant-design-vue';
  import LoginFormTitle from './LoginFormTitle.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';

  import { useUserStore } from '/@/store/modules/user';
  import { useFormRules, useFormValid } from './useLogin';
  import signUtils from '/@/utils/encryption/signUtils';
  import { getCodeInfo } from '/@/api/sys/user';
  import checkCodePng from '/@/assets/images/checkcode.png';

  const ACol = Col;
  const ARow = Row;
  const FormItem = Form.Item;
  const InputPassword = Input.Password;
  const { t } = useI18n();
  const { notification } = useMessage();
  const userStore = useUserStore();
  const { getFormRules } = useFormRules();
  const formRef = ref();
  const loading = ref(false);

  const formData = reactive({
    account: '',
    // 密码
    pawrd: '',
    inputCode: '',
  });
  const randCodeData = reactive({
    randCodeImage: '',
    requestCodeSuccess: false,
    checkKey: null,
  });
  const { validForm } = useFormValid(formRef);
  async function handleLogin() {
    const data = await validForm();
    if (!data) return;
    try {
      loading.value = true;
      const res = await userStore.login(
        toRaw({
          password: signUtils.randomString() + window.btoa(data.pawrd),
          username: data.account,
          captcha: data.inputCode,
          checkKey: randCodeData.checkKey,
          mode: 'none', //不要默认的错误提示
        })
      );
      const userInfo = res?.userInfo;
      if (userInfo) {
        notification.success({
          message: t('sys.login.loginSuccessTitle'),
          description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.realname}`,
          duration: 3,
        });
      }
    } catch (error) {
      notification.error({
        message: t('sys.api.errorTip'),
        // @ts-ignore
        description: error.message || t('sys.api.networkExceptionMsg'),
        duration: 3,
      });
      loading.value = false;

      //update-begin-author:taoyan date:2022-5-3 for: issues/41 登录页面，当输入验证码错误时，验证码图片要刷新一下，而不是保持旧的验证码图片不变
      handleChangeCheckCode();
      //update-end-author:taoyan date:2022-5-3 for: issues/41 登录页面，当输入验证码错误时，验证码图片要刷新一下，而不是保持旧的验证码图片不变
    }
  }
  function handleChangeCheckCode() {
    formData.inputCode = '';
    //TODO 兼容mock和接口，暂时这样处理
    // @ts-ignore
    randCodeData.checkKey = 1629428467008;
    getCodeInfo(randCodeData.checkKey).then((res) => {
      randCodeData.randCodeImage = res;
      randCodeData.requestCodeSuccess = true;
    });
  }

  //初始化验证码
  onMounted(() => {
    handleChangeCheckCode();
  });
</script>
