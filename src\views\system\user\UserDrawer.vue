<template>
  <BasicDrawer
    v-bind="$attrs"
    :title="getTitle"
    :width="adaptiveWidth"
    :showFooter="showFooter"
    destroyOnClose
    @register="registerDrawer"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from './user.data';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { saveOrUpdateUser, getUserRoles } from '/@/api/sys/user';
  import { useDrawerAdaptiveWidth } from '/@/hooks/jeecg/useAdaptiveWidth';
  import { queryTreeList } from '/@/api/common/api';
  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  const isUpdate = ref(false);
  const rowId = ref(undefined);
  //表单配置
  const [registerForm, formActionType] = useForm({
    labelWidth: 90,
    schemas: formSchema,
    showActionButtonGroup: false,
  });
  const showFooter = ref(true);
  //表单赋值
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    getRootTree();
    await formActionType.resetFields();
    let roles = [];
    showFooter.value = data?.showFooter ?? true;
    setDrawerProps({ confirmLoading: false, showFooter: showFooter.value });
    isUpdate.value = !!data?.isUpdate;
    console.log(isUpdate.value,'isUpdate.value')
    if (unref(isUpdate)) {
      rowId.value = data?.record?.id;
      //处理角色用户列表情况(和角色列表有关系)
      roles = await getUserRoles({ userId: unref(rowId) });
    } else {
      rowId.value = undefined;
    }
    // 判断数据类型是否为 自定义， true  打开 地域 选项  否则隐藏地域input
    if (data?.record?.dataAuthType === '4') {
      data.record.orgCodes = data?.record?.orgCodeList || data.record.orgCodes;
      
      formActionType.updateSchema({
        field: 'orgCodes',
        required: true,
        show: true,
      });
    } else {
      formActionType.updateSchema({
        field: 'orgCodes',
        required: false,
        show: false,
      });
    }
    // 编辑模式下不可修改编码
    formActionType.updateSchema([
      {
        field: 'password',
        show: !unref(isUpdate),
      },
      {
        field: 'confirmPassword',
        ifShow: !unref(isUpdate),
      },
      {
        field: 'orgCodeList',
        show: data?.record?.dataAuthType === '4' ? true : false,
      },
      {
        field: 'username',
        dynamicDisabled: () => unref(isUpdate),
      },
      // {
      //   field: 'selecteddeparts',
      //   dynamicDisabled: () => unref(isUpdate),
      // },
      {
        field: 'systemType',
        dynamicDisabled: () => unref(isUpdate),
      },
    ]);
    // 无论新增还是编辑，都可以设置表单值
    if (typeof data.record === 'object') {
      console.log(data.record,'data.record')
      formActionType.setFieldsValue({
        ...data.record,
        companyId: data?.record?.companyId || '',
        selectedroles: roles,
      });

    }
    // 隐藏底部时禁用整个表单
    formActionType.setProps({ disabled: !showFooter.value });
    formActionType.clearValidate();
  });
  //获取标题
  const getTitle = computed(() => (!unref(isUpdate) ? '新增用户' : unref(showFooter) ? '编辑用户' : '用户详情'));
  const { adaptiveWidth } = useDrawerAdaptiveWidth();
  //提交事件
  async function handleSubmit() {
    try {
      let values = await formActionType.validate();
      
      values.systemType = '3'
      console.log(values,'values')
      setDrawerProps({ confirmLoading: true });
      let isUpdateVal = unref(isUpdate);
      //提交表单
      await saveOrUpdateUser({ ...values, id: unref(rowId) }, isUpdateVal);
      //关闭弹窗
      closeDrawer();
      //刷新列表
      emit('success', { isUpdateVal, values });
    } catch (e) {
      console.warn(e);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
  // 获取组织树
  function getRootTree() {
    queryTreeList().then((res) => {
      formActionType.updateSchema([
        { field: 'companyId', componentProps: { treeData: res } },
        { field: 'orgCodes', componentProps: { treeData: res } },
      ]);
    });
  }
</script>
