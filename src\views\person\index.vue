<template>
  <div class="p-4">
    <!-- 用户信息展示 -->
    <a-card>
      <a-spin :spinning="loading">
        <a-descriptions title="个人资料" bordered :labelStyle="{ width: '105px' }">
          <a-descriptions-item :label="item.label" :span="3" v-for="item in userItem" :key="item.key">{{ userInfo[item.key] }}</a-descriptions-item>
        </a-descriptions>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue';
  import { queryUserInfo } from '/@/api/user';

  let userInfo = ref({});
  let loading = ref(false);

  const userItem = reactive([
    { label: '登录账号', key: 'username' },
    { label: '用户姓名', key: 'realName' },
    { label: '所属组织', key: 'departName' },
    { label: '联系电话', key: 'phone' },
    { label: '电子邮箱', key: 'email' },
    { label: '用户角色', key: 'roleName' },
    { label: '注册时间', key: 'createTime' },
  ]);

  onMounted(() => {
    loading.value = true;
    queryUserInfo()
      .then((res) => {
        userInfo.value = res;
      })
      .finally(() => {
        loading.value = false;
      });
  });
</script>
