<template>
  <PageWrapper>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" 
        preIcon="ant-design:plus-outlined" 
        v-auth="'system:annount:add'"  
        @click="handleCreate"> 新增
        </a-button>
        <a-button 
         type="primary" 
         v-auth="'customer:property:export'"  
         preIcon="ant-design:export-outlined" @click="exportClick">导出</a-button>
        <!-- <a-button @click="startLoop" :disabled="isRunning">
          {{ isRunning ? '运行中...' : '开始执行(1000次)' }}
        </a-button>
        <a-button @click="stopLoop" v-if="isRunning">停止</a-button> -->
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- <propertyModal @register="registerPropertyModal" @success="handleSuccess" /> -->
    <infoModel @register="registerInfoModel" @success="handleSuccess" />
    <detailModel @register="registerDetailModal" @success="handleSuccess" />
  </PageWrapper>
</template>
<script lang="ts" name="system-user" setup>
  import { Modal } from 'ant-design-vue';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import infoModel from './components/infoModel.vue';
  import detailModel from './components/detailModel.vue';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { columns, searchFormSchema } from './components/info.data';
  import { getQueryChannelList, getChannelExport  } from '/@/api/property/property';
  import { exportExcel } from '/@/utils/common/compUtils';
  import { getrsatest  } from '/@/api/property/property';
  import {ref,unref,onMounted,onUnmounted} from "vue"
  const count = ref(0);
  const isRunning = ref(false);
  const stopFlag = ref(false);
  //const searchParams = ref({})
  let searchParams = {}
  const { createMessage } = useMessage();
  //model
  const [registerInfoModel, { openModal }] = useModal();
  const [registerDetailModal, { openModal: openDetailModal }] = useModal();
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'annount-list',
    tableProps: {
      title: '资产列表',
      api: getQueryChannelList,
      rowSelection: {},
      columns: columns,
      // showIndexColumn: true,
      formConfig: {
        showAdvancedButton: false,
        labelWidth: 100,
        schemas: searchFormSchema,
        actionColOptions: {
          xs: 24, // <576px
          sm: 24, // ≥576px
          md: 24, // ≥768px
          lg: 24, // ≥992px
          xl: 24, // ≥1200px
          xxl: 24,
          style: { textAlign: 'right' },
        },
      },
      actionColumn: {
        width: 250,
      },
      beforeFetch: (params) => {
        delete params.column;
        //delete params.order;
        console.log(params,'1233')
        searchParams = params
        // return Object.assign({ column: 'createTime', order: 'desc' }, params);
      },
    }
  });
  
  
  //注册table数据
  const [registerTable, { reload }] = tableContext;
  // 新增事件
  function handleCreate() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }
  //编辑事件
  async function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  async function handleDetail(record: Recordable) {
    console.log(record,'record')
    openDetailModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  function handleApprove(record){
    openDetailModal(true, {
      record,
      isUpdate: false,
      showFooter: false,
    });
  }
  // 成功回调
  function handleSuccess() {
    reload();
  }
  function handleStatus(status,record){
      console.log(status,'status')
        Modal.confirm({
          title: status == '0' ? '确定要将集团客户置为新申请吗？':'确定要将集团客户状态置为无效吗？' ,
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            // dictTaskDone({ taskId: record.taskId, status: 2 }).then(() => {
            //     handleSuccess();
            // });
          },
        });
  }

  function handleSubmit(record){
        Modal.confirm({
          title: '确定要提交审批吗？',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            // dictTaskDone({ taskId: record.taskId, status: 2 }).then(() => {
            //     handleSuccess();
            // });
          },
        });
  }
  // 启动循环
  const startLoop = () => {
    if (isRunning.value) return;
    
    isRunning.value = true;
    stopFlag.value = false;
    count.value = 0;
    
    const execute = () => {
      if (stopFlag.value || count.value >= 1000) {
        isRunning.value = false;
        return;
      }
      
      btnClick();
      count.value++;
      
      // 使用setTimeout避免阻塞UI
      setTimeout(execute, 5000);
    };
    
    execute();
  };
  // 停止循环
const stopLoop = () => {
  stopFlag.value = true;
};

// 组件卸载时自动停止
onUnmounted(stopLoop);
   async function btnClick(){
     let searchParams = {
        username:"djl",
        password:"Aa111111.",
        realname:"定价两2",
        phone:"17611111111",
        companyId:"999",
        selectedroles:"1891677676294115330",
        email:"<EMAIL>",
        dataAuthType:"1",
        systemType:"3"
      }
      const randomizedParams = Object.fromEntries(
        Object.entries(searchParams)
          .sort(() => Math.random() - 0.5)
      );
     console.log(randomizedParams,'searchParams')
     try {
        const res = await getrsatest(randomizedParams);
        console.log(res,'resss')
       
      } catch (error: any) {
       
      }
  }
  
  // 操作栏
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'customer:property:record'
      },
      {
        label: '提交',
        onClick: handleSubmit.bind(null, record),
        auth: 'customer:property:adjust'
      },
      {
        label: '审批',
        onClick: handleApprove.bind(null, record),
        auth: 'customer:property:adjust'
      },
      {
        label: '浏览',
        onClick: handleDetail.bind(null, record),
        auth: 'customer:property:adjust'
      },
      {
        label: '有效',
        onClick: () => handleStatus('0',record),
        auth: 'customer:property:adjust'
     },
     {
        label: '无效',
        onClick: () => handleStatus('1',record),
        auth: 'customer:property:adjust'
      },
    ];
  }
  async function exportClick(){
     console.log(searchParams,'searchParams')
     try {
        const res = await getChannelExport(searchParams);
        exportExcel(res)
        createMessage.success('导出成功');
      } catch (error: any) {
        createMessage.error('导出失败：' + error.message);
      }
  }
</script>
