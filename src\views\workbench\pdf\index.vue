<template>
  <h3 class="m-0 pl-5 pt-5">{{ route.query.title }}报告</h3>
  <div class="w-4/5 mx-auto py-5">
    <div class="flex">
      <a-button class="ml-auto mr-2" type="primary" :disabled="loading" @click="download">导出</a-button>
      <a-button type="primary" @click="goBack">返回</a-button>
    </div>
    <!-- 报告展示区 -->
    <a-spin class="h-full w-full" :spinning="loading" size="large">
      <VuePdfEmbed :source="fileUrl" text-layer annotation-layer @progress="loading = true" @loaded="loading = false" />
    </a-spin>
  </div>
</template>

<script setup>
  import { Modal } from 'ant-design-vue';
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import { ref, createVNode } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import VuePdfEmbed from 'vue-pdf-embed';
  import 'vue-pdf-embed/dist/styles/annotationLayer.css';
  import 'vue-pdf-embed/dist/styles/textLayer.css';
  import { getFileAccessUrl, downloadByScript } from '/@/utils/common/compUtils';

  const router = useRouter();
  const route = useRoute();

  const loading = ref(true);
  const fileUrl = getFileAccessUrl(route.query.id);

  // 下载报告
  const download = () => {
    Modal.confirm({
      title: '确认导出此数据?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        downloadByScript(route.query.id);
      },
    });
  };

  const goBack = () => {
    router.replace({
      path: '/workbench/list',
      query: {
        ...route.query,
      },
    });
  };
</script>

<style lang="less" scoped>
  :deep(.vue-pdf-embed__page) {
    margin-top: 20px;
  }
</style>
