<template>
  <div class="py-5 px-10">
    <!-- 查看时展示 -->
    <a-descriptions v-if="action === 2" :aria-hidden="action !== 2" bordered :column="1" :labelStyle="{ width: '120px' }">
      <a-descriptions-item label="登录账号">{{ user.username }}</a-descriptions-item>
      <a-descriptions-item label="真实姓名">{{ user.realname }}</a-descriptions-item>
      <a-descriptions-item label="所属公司">{{ user.companyName }}</a-descriptions-item>
      <a-descriptions-item label="联系电话">{{ user.phone }}</a-descriptions-item>
      <a-descriptions-item label="电子邮箱">{{ user.email }}</a-descriptions-item>
    </a-descriptions>

    <!-- 添加和编辑展示 -->
    <a-form v-else :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-item v-show="action !== 3" label="登录账号" v-bind="validateInfos.username">
        <a-input v-model:value="user.username" placeholder="请输入登录账号" :maxlength="20" />
      </a-form-item>

      <a-form-item label="真实姓名" v-bind="validateInfos.realname">
        <a-input v-model:value="user.realname" placeholder="请输入真实姓名" :maxlength="50" />
      </a-form-item>

      <a-form-item label="所属公司" v-bind="validateInfos.companyId">
        <a-select v-model:value="user.companyId" placeholder="请选择所属公司">
          <a-select-option v-for="item in options" :key="item.id" :value="item.id">{{ item.departName }}</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="联系电话" v-bind="validateInfos.phone">
        <a-input v-model:value="user.phone" placeholder="请输入联系电话" :maxlength="11" />
      </a-form-item>

      <a-form-item label="邮箱地址" v-bind="validateInfos.email">
        <a-input v-model:value="user.email" placeholder="请输入邮箱地址" :maxlength="30" />
      </a-form-item>

      <a-form-item :wrapper-col="{ span: 14, offset: 4 }">
        <a-button @click="onClose">取消</a-button>
        <a-button style="margin-left: 10px" type="primary" @click.prevent="onSubmit">确认</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref, reactive, onMounted, h } from 'vue';
  import { Form, message, Modal } from 'ant-design-vue';
  import cloneDeep from 'lodash-es/cloneDeep';
  import useClipboard from 'vue-clipboard3';
  import { getDepartList, addUser, editUser } from '/@/api/user';

  const { toClipboard } = useClipboard();
  const useForm = Form.useForm;
  export default defineComponent({
    props: {
      userInfo: {
        type: Object,
        default: () => {
          return {};
        },
      },
      // 1添加，2查看，3编辑
      action: {
        type: Number,
        default: () => 2,
      },
    },
    emits: ['closeModal'],
    setup(props, { emit }) {
      let user = reactive({
        username: '',
        realname: '',
        companyId: undefined,
        phone: '',
        email: '',
        departmentName: '',
        departmentId: undefined,
        dataAuthType: '1', // 数据权限默认为全部
        orgCodes: '',
        selectedroles: '',
        roleCode: [],
      });
      const options = ref([{ id: 0, departName: '' }]);

      const rulesRef = reactive({
        username: [{ required: true, message: '请输入登录账号' }],
        realname: [{ required: true, message: '请输入真实姓名' }],
        companyId: [{ required: true, message: '请选择所属公司' }],
        phone: [
          { required: true, message: '请输入联系电话' },
          { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式有误' },
        ],
        email: [
          { required: true, message: '请输入用户邮箱' },
          {
            pattern: /^[a-zA-Z0-9._%+-]{1,29}@[a-zA-Z0-9.-]{1,30}\.[a-zA-Z]{2,}$/,
            message: '邮箱格式有误',
          },
        ],
      });

      const { resetFields, validate, validateInfos } = useForm(user, rulesRef, {});

      // 根据action值来决定是添加还是编辑
      const onSubmit = () => {
        validate()
          .then(() => {
             console.log('props',props)
            if (props.action === 1) {
              user.selectedroles = '1843911360750530561'; // 角色默认为政企管理员
              addUser(user).then((res) => {
                Modal.success({
                  title: '添加成功',
                  closable: true,
                  content: h('div', {}, [h('h4', `账号：${res.username}`), h('h4', `密码：${res.password}`)]),
                  okText: '点击复制',
                  onOk: async () => {
                    try {
                      await toClipboard(`账号：${res.username}\n密码：${res.password}`);
                      message.success('复制成功!');
                    } catch {
                      message.error('复制失败');
                    }
                  },
                });
                onClose();
              });
            } else {
              console.log('22222')
              user.orgCodes = '';
              if (user.roleCode.toString() === 'admin') {
                user.selectedroles = '1513351043114311682';
              }
              editUser(user).then(() => {
                message.success('修改成功');
                onClose();
              });
            }
          })
          .catch(() => {});
      };

      // 关闭弹窗
      const onClose = () => {
        resetFields();
        emit('closeModal');
      };

      // 获取部门列表
      async function getDepartment() {
        options.value = await getDepartList();
      }

      onMounted(() => {
        // 对父组件传递过来的userInfo进行深拷贝，避免影响原始数据
        user = Object.assign(user, cloneDeep(props.userInfo));
        if (!user.companyId) {
          user.companyId = user.companyId || undefined;
        }
        getDepartment();
      });

      return {
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
        validateInfos,
        user,
        onSubmit,
        onClose,
        options,
      };
    },
  });
</script>
