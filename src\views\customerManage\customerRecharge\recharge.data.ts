import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import dayjs from 'dayjs';

let srcOrderType = [];
// window.$getDicOptions('src_order_send_type').then((data) => {
//   srcOrderType = data;
// });

export const columns: BasicColumn[] = [
  {
    title: '充值单号ID',
    dataIndex: 'chargeId',
  },
  {
    title: '集团客户名称',
    dataIndex: 'channelName',
  },
  {
    title: '账户ID',
    dataIndex: 'channelId',
  },
  {
    title: '充值类型',
    dataIndex: 'chargeValueStr',
  },
  {
    title: '充值金额',
    dataIndex: 'chargeValue',
  },
  {
    title: '充值时间',
    dataIndex: 'chargeTime',
    // customRender: ({ text }) => atob(text),
  },
  {
    title: '审批状态',
    dataIndex: 'auditStatusStr',
    // customRender: ({ text }) => atob(text),
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '账户ID',
    field: 'channelId',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '充值单号',
    field: 'chargeId',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '审批状态',
    field: 'auditStatus',
    component: 'Select',
    componentProps: {
      options: [
        { label: '新申请', value: 0 },
        { label: '项目经理审批', value: 1 },
        { label: '省侧负责人审批', value: 2 },
        { label: '合规组审批', value: 4 },
        { label: '审批通过', value: 5 },
      ],
      placeholder: '请选择',
    },
    colProps: { span: 8 },
  },
  {
    label: '查询时间',
    field: 'dateOp',
    component: 'RangeDate',
    componentProps: {
      //是否显示时间
      // showTime: true,
      format: 'YYYY-MM-DD',
      //日期格式化
      valueFormat: 'YYYY-MM-DD',
      //范围文本描述用集合
      placeholder: ['开始时间', '结束时间'],
      // disabledDate: (currentDate) => {
      //   return currentDate < dayjs().startOf('day');
      // },
    },
    colProps: { span: 8 },
  },
  {
    label: '充值类型',
    field: 'chargeType',
    component: 'Select',
    componentProps: {
      options: [
        { label: '集团客户资金充值', value: 0 },
        { label: '集团客户资金退款', value: 1 },
      ],
      placeholder: '请选择',
    },
    colProps: { span: 8 },
  },
  {
    label: '集团客户名称',
    field: 'channelName',
    component: 'Input',
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    label: '充值类型',
    field: 'chargeType',
    component: 'RadioGroup',
    required: true,
    colProps: { span: 24 },
    componentProps: {
      options: [
        { label: '集团客户资金充值', value: 0 },
        { label: '集团客户资金退款', value: 1 },
      ],
    },
  },
  {
    label: '集团客户名称',
    field: 'channelName',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
  },
  {
    label: '充值金额',
    field: 'channelName',
    component: 'InputNumber',
    required: true,
    colProps: { span: 24 },
  },
  {
    label: '提单说明',
    field: 'channelName',
    component: 'InputTextArea',
    required: true,
    colProps: { span: 24 },
  },
  {
    label: '资金入账截图',
    field: 'channelName2',
    component: 'JUpload',
    required: true,
    slot: 'screenshotSlot',
    colProps: { span: 24 },
    dynamicRules: ({ values }) => {
      //需要return
      return [
        {
          //默认开启表单检验
          required: true,
          validator: (_, value) => {
            console.log(value);
            //需要return 一个Promise对象
            return new Promise((resolve, reject) => {
              if (!value || value.length === 0) {
                reject('请上传正确格式/大小的资金入账截图！');
              }
              resolve();
            });
          },
        },
      ];
    },
  },
];

export const tableColumns: BasicColumn[] = [
  {
    title: '审批环节',
    dataIndex: 'index',
  },
  {
    title: '处理时间',
    dataIndex: 'index',
  },
  {
    title: '处理结果',
    dataIndex: 'index',
  },
  {
    title: '处理人',
    dataIndex: 'index',
  },
  {
    title: '备注/审批意见',
    dataIndex: 'index',
  },
];
