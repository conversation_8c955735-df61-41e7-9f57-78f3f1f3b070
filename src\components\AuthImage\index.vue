<template>
  <a-image :src="srcUrl" v-bind="$attrs" :fallback="fallbackImg" preview="preview">
    <template v-if="$slots.previewMask" #previewMask>
      <slot name="previewMask"></slot>
    </template>
  </a-image>
</template>

<script lang="ts" setup name="auth-image">
  import { ref } from 'vue';
  import { getFileAccessUrl, fallbackImg } from '/@/utils/common/compUtils';
  type PropType = { src?: string; fileId?: string; isImg?: boolean };
  const props = withDefaults(defineProps<PropType>(), {
    src: '',
    fileId: '',
    isImg: false,
    preview: true,
  });
  const srcUrl = ref(props.src || getFileAccessUrl(props.fileId));
</script>
<style lang="less" scoped></style>
