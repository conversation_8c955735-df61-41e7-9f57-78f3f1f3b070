import { unref } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';
import URL from '/@/api/url';

const { createConfirm } = useMessage();

/**
 * 获取部门树列表
 */
export const queryDepartTreeSync = (params?) => defHttp.post({ url: URL.depart.queryDepartTreeSync, params });

/**
 * 保存或者更新部门角色
 */
export const saveOrUpdateDepart = (params, isUpdate) => {
  if (isUpdate) {
    return defHttp.post({ url: URL.depart.edit, params });
  } else {
    return defHttp.post({ url: URL.depart.save, params });
  }
};

/**
 * 批量删除部门角色
 */
export const deleteBatchDepart = (params, confirm = false) => {
  return new Promise((resolve, reject) => {
    const doDelete = () => {
      resolve(defHttp.post({ url: URL.depart.deleteBatch, params }));
    };
    if (confirm) {
      createConfirm({
        iconType: 'warning',
        title: '删除',
        content: '确定要删除吗？',
        onOk: () => doDelete(),
        onCancel: () => reject(),
      });
    } else {
      doDelete();
    }
  });
};

/**
 * 获取权限树列表
 */
export const queryRoleTreeList = (params?) => defHttp.post({ url: URL.depart.roleQueryTreeList, params });
/**
 * 查询部门权限
 */
export const queryDepartPermission = (params?) => defHttp.post({ url: URL.depart.queryDepartPermission, params });
/**
 * 保存部门权限
 */
export const saveDepartPermission = (params) => defHttp.post({ url: URL.depart.saveDepartPermission, params });

/**
 *  查询部门数据权限列表
 */
export const queryDepartDataRule = (functionId, departId, params?) => {
  const url = `${URL.depart.dataRule}/${unref(functionId)}/${unref(departId)}`;
  return defHttp.post({ url, params });
};
/**
 * 保存部门数据权限
 */
export const saveDepartDataRule = (params) => defHttp.post({ url: URL.depart.dataRule, params });
/**
 * 获取登录用户部门信息
 */
export const getUserDeparts = (params?) => defHttp.post({ url: URL.depart.getCurrentUserDeparts, params });
/**
 * 切换选择部门
 */
export const selectDepart = (params?) => defHttp.post({ url: URL.depart.selectDepart, params });

/**
 * 编辑部门前获取部门相关信息
 * @param id
 */
export const getUpdateDepartInfo = (id) => defHttp.post({ url: URL.depart.getUpdateDepartInfo, params: { id } });

/**
 * 编辑部门
 * @param params
 */
export const doUpdateDepartInfo = (params) => defHttp.post({ url: URL.depart.doUpdateDepartInfo, params });

/**
 * 删除部门
 * @param id
 */
export const deleteDepart = (id) => defHttp.post({ url: URL.depart.delete, params: { id } });

/**
 * 禁用部门
 * @param id
 */
export const forbiddenDepart = (id) => defHttp.post({ url: URL.depart.forbidden, params: { id } });

/**
 * 设置负责人 取消负责人
 * @param params
 */
export const changeDepartChargePerson = (params) => defHttp.post({ url: URL.depart.changeDepartChargePerson, params });
