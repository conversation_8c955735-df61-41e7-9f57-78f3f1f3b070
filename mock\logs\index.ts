import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, baseUrl } from '../_util';
import API_URL from '@/api/url';
import { LogModel } from '/@/api/logs/model';
const logsList = (() => {
  const result: LogModel[] = [];
  for (let index = 0; index < 100; index++) {
    result.push({
      username: 'admin',
      operationIp: '@cname',
      operationTime: '@first',
      operationModules: '@datetime',
      operationContent: '@cname',
    });
  }
  return result;
})();

export default [
  // mock loglist
  {
    url: `${baseUrl}${API_URL.logs.logList}`,
    timeout: 200,
    method: 'post',
    response: () => {
      return resultSuccess({
        records: logsList,
        total: 100,
      });
    },
  },
] as MockMethod[];
