<template>
  <BasicModal v-bind="$attrs" :title="getTitle" width="800px" :showOkBtn="showFooter" @register="registerModal" @ok="handleSubmit">
    <div>
      <a-row class="elDiv" v-for="(item, index) in detailList" :key="index" v-if="detailList.length">
        <a-col :span="10" :push="5">
          <p style="font-size: 14px; color: #606266">阶段{{ index + 1 }}：</p>
          <div class="commitText" v-if="item.status == 1">{{ item.status == 1 ? '已提交' : '未提交' }}</div>
        </a-col>
        <a-col :span="14">
          <a-row class="elItem">
            <a-col :span="12">交付数量：{{ item.deliverCount }}</a-col>
          </a-row>
          <a-row class="elItem">
            <a-col :span="12">交付单价：{{ item.price }}</a-col>
          </a-row>
          <a-row class="elItem">
            <a-col :span="12">交付金额：{{ item.totalPrice }}</a-col>
          </a-row>
          <a-row class="elItem">
            <a-col :span="12">验收日期：{{ item.createTimeStr }}</a-col>
          </a-row>
        </a-col>
        <a-divider />
      </a-row>
      <a-row v-else-if="detailList.length == 0 && unref(isUpdate)">
        <a-col :span="24" style="text-align: center">暂无详情......</a-col>
      </a-row>
    </div>

    <a-row v-if="!unref(isUpdate)">
      <a-col :span="10" :push="5">
        <p style="font-size: 14px; color: #606266">
          <span style="color: #f56c6c">*</span>阶段{{ detailList.length ? detailList.length + 1 : 1 }}：
        </p>
      </a-col>
      <a-col :span="14" :pull="2">
        <BasicForm @register="registerForm">
          <template #totalPrice="{ model, field }">
            <a-input v-model:value="model[field]" />
          </template>
          <template #deliverCount="{ model, field }">
            <!-- 如果是组件需要进行双向绑定，model当前表单对象，field当前字段名称  -->
            <a-input v-model:value="model[field]" placeholder="请输入交付数量" />
          </template>
        </BasicForm>
      </a-col>
    </a-row>
    <template #footer v-if="!unref(isUpdate)">
      <a-button type="primary" v-auth="'order:check:commit'" @click="handleSubmit">提交验收</a-button>
      <a-button type="primary" v-auth="'order:check:over'" @click="handleOk">项目完结</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" name="PassWordModal" setup>
  import { Modal } from 'ant-design-vue';
  import { ref, computed, unref, h } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formAnnountSchema } from './check.data';
  import { dictCheckAdd, getTaskPrice, getCheckDatail, dictTaskDone } from '/@/api/order/dict-check';
  const { createMessage } = useMessage();
  const isUpdate = ref(false);
  const taskId = ref(undefined);
  const detailList = ref([]);
  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  //表单配置
  const [registerForm, formActionType] = useForm({
    schemas: formAnnountSchema,
    showActionButtonGroup: false,
  });
  const showFooter = ref(true);
  //设置标题
  const getTitle = computed(() => (!unref(isUpdate) ? '项目验收' : '项目验收详情'));
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await formActionType.resetFields();
    showFooter.value = data?.showFooter ?? true;
    setModalProps({ confirmLoading: false, showOkBtn: showFooter.value });
    isUpdate.value = !!data?.isUpdate;
    if (data) {
      taskId.value = data?.record?.taskId;
      //表单赋值
      getDetail(taskId.value);
      if (!isUpdate.value) {
        getPrice(taskId.value);
      }
    }

    formActionType.setProps({ disabled: !showFooter.value });
    formActionType.clearValidate();
  });
  async function getPrice(taskId) {
    let res = await getTaskPrice({ taskId });
    if (res) {
      await formActionType.setFieldsValue({
        srcProdId: res.srcProdId,
        price: res.price,
      });
    }
  }
  async function getDetail(taskId) {
    let res = await getCheckDatail({ taskId });
    if (res) {
      detailList.value = res;
    }
  }
  async function handleOk() {
    Modal.confirm({
      title: '是否确认完结项目?',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        try {
          setModalProps({ confirmLoading: true });
          dictTaskDone({ taskId: unref(taskId), status: 1 });
          //关闭弹窗
          closeModal();
          createMessage.success('操作成功');
          //刷新列表
          emit('success');
        } finally {
          setModalProps({ confirmLoading: false });
        }
      },
    });
  }
  //表单提交事件
  async function handleSubmit() {
    try {
      const values = await formActionType.validate();
      setModalProps({ confirmLoading: true });
      //let isUpdateVal = unref(isUpdate);
      //提交表单
      await dictCheckAdd({ ...values, taskId: unref(taskId) });
      //关闭弹窗
      closeModal();
      createMessage.success('操作成功');
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
<style lang="less" scoped>
  .commitText {
    width: 65px;
    height: 65px;
    line-height: 65px;
    text-align: center;
    border: 1px solid #cccccc;
    border-radius: 50px;
    font-size: 14px;
    background-color: #cccccc;
    opacity: 0.5;
    transform: rotate(300deg);
  }
</style>
