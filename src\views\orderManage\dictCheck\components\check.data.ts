// import { BasicColumn } from '/@/components/Table';
// import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { h } from 'vue';
import { Input } from 'ant-design-vue';
// 查询
export const searchFormSchema: FormSchema[] = [
  {
    label: '集团客户名称',
    field: 'channelName',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 30,
    }
  },
  {
    label: '订单号',
    field: 'orderId',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      maxLength: 20,
    }
  },
  {
    label: '任务状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'task_status',
      placeholder: '请选择任务状态',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  }
];

export const columns: BasicColumn[] = [
  {
    title: '账户ID',
    dataIndex: 'channelId',
    width: 120,
  },
  {
    title: '集团客户名称',
    dataIndex: 'channelName',
    width: 120,
  },
  {
    title: '联系人',
    width: 150,
    dataIndex: 'contact',
  },
  {
    title: '电话',
    dataIndex: 'contactPhone',
    width: 100,
  },
  {
    title: '订单号',
    dataIndex: 'orderId',
    width: 100
  },
  {
    title: '任务状态',
    dataIndex: 'statusStr',
    width: 100,
  }
  
];

export const formAnnountSchema: FormSchema[] = [
  {
    label: '产品编号',
    field: 'srcProdId',
    component: 'Input',
    componentProps:{
      disabled: true
    },
  },
  {
    label: '交付单价',
    field: 'price',
    component: 'Input',
    componentProps:{
      disabled: true
    },
  },
  {
    label: '交付金额',
    field: 'totalPrice',
    component: 'Input',
    componentProps:{
      disabled: true
    },
    render: ({ model, field }) => {
      console.log(field,'model')
      //渲染自定义组件，以Input为例
      let price = model.price ? model.price : 0;
      let num = model.num ? model.num : 0;
      return h(Input, {
        value: model[field],
        style: { width: '100%' },
        disabled: true
      });
    }
  },
  {
    label: '交付数量',
    field: 'deliverCount',
    component: 'Input',
    dynamicRules: ({ values }) => {
      console.log('values:', values);
      //需要return
      return [
        {
          //默认开启表单检验
          required: true,
          // value 当前手机号输入的值
          validator: (_, value) => {
            //需要return 一个Promise对象
            return new Promise((resolve, reject) => {
              if (!value) {
                reject('请输入交付数量！');
              }else if(!/^\d+$/.test(value)){
                reject('请输入正整数!');
              }else if(parseInt(value, 10) > 10000){
                reject('输入值不能超过 10000');
              }
              resolve();
            });
          },
        },
      ];
    },
    render: ({ model, field }) => {
      console.log(field,'model',model)
      //渲染自定义组件，以Input为例
      return h(Input, {
        placeholder: '请输入交付数量',
        value: model[field],
        style: { width: '100%' },
        onChange: (e: ChangeEvent) => {
          model[field] = e.target.value;
          model['totalPrice'] = (e.target.value * model.price).toFixed(2)
        },
      });
    },

  },
];
