import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import dayjs from 'dayjs';

let srcOrderType = [];
window.$getDicOptions('llyx_order_status').then((data) => {
  srcOrderType = data;
});

export const columns: BasicColumn[] = [
  // {
  //   title: '订单ID',
  //   dataIndex: 'orderId',
  //   customRender: ({ text }) => text || '—',
  //   width: 200,
  // },
  {
    title: '流量营销平台订单号',
    dataIndex: 'srcTransId',
    width: 250,
  },
  {
    title: 'CRM订单号',
    dataIndex: 'oprSeq',
    width: 200,
  },
  {
    title: '充值手机号',
    dataIndex: 'mobile',
    // customRender: ({ text }) => atob(text),
  },
  {
    title: '充值金额',
    dataIndex: 'orderValue',
    // customRender: ({ text }) => atob(text),
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    customRender: ({ text }) => {
      return srcOrderType.find((item: any) => item.value === text)?.label || text || '—';
    },
  },
  {
    title: '失败原因',
    dataIndex: 'orderMsg',
    customRender: ({ text }) => text || '—',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
  {
    title: '成功时间',
    dataIndex: 'updateTime',
  },
];

export const searchFormSchema: FormSchema[] = [
  // {
  //   label: 'ID',
  //   field: 'id',
  //   component: 'Input',
  //   colProps: { span: 8 },
  // },
  {
    label: '流量营销平台订单号',
    field: 'srcTransId',
    component: 'Input',
    colProps: { span: 8 },
    labelWidth: '140px',
  },
  {
    label: 'CRM订单号',
    field: 'oprSeq',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '充值手机号',
    field: 'msisdn',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '订单状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'llyx_order_status',
    },
    colProps: { span: 8 },
  },
  {
    label: '创建时间',
    field: 'dateOp',
    component: 'RangeDate',
    componentProps: {
      //是否显示时间
      // showTime: true,
      format: 'YYYY-MM-DD',
      //日期格式化
      valueFormat: 'YYYY-MM-DD',
      //范围文本描述用集合
      placeholder: ['开始时间', '结束时间'],
      // disabledDate: (currentDate) => {
      //   return currentDate < dayjs().startOf('day');
      // },
    },
    colProps: { span: 8 },
  },
  {
    label: '成功时间',
    field: 'dateOp2',
    component: 'RangeDate',
    componentProps: {
      //是否显示时间
      // showTime: true,
      format: 'YYYY-MM-DD',
      //日期格式化
      valueFormat: 'YYYY-MM-DD',
      //范围文本描述用集合
      placeholder: ['开始时间', '结束时间'],
      // disabledDate: (currentDate) => {
      //   return currentDate < dayjs().startOf('day');
      // },
    },
    colProps: { span: 8 },
  },
];
