<template>
  <BasicModal v-bind="$attrs" :title="'充值'" width="800px" @register="registerModal" @ok="handleSubmit" :closeFunc="handleBeforeClose">
    <BasicForm @register="registerForm">
      <template #screenshotSlot="{ model, field }">
        <a-upload v-model:file-list="model[field]" :before-upload="beforeUpload" name="file" :max-count="1" :multiple="false" accept="image/*">
          <a-button type="primary"> <upload-outlined /> 选择文件 </a-button>
        </a-upload>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts">
  // 单独添加一个script块用于导出
  export default {
    name: 'system-bannerModel', // 保持与你原来的name一致
  };
</script>
<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from './recharge.data';
  // import { postBannerSave, postBannerUpdata } from '/@/api/sys/banner';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Upload } from 'ant-design-vue';
  import { UploadOutlined } from '@ant-design/icons-vue';
  import type { UploadProps, UploadFile } from 'ant-design-vue';
  import { func } from 'vue-types';
  const emit = defineEmits(['success', 'register']);
  let modelType = ref('add');
  let bannerId = ref(0);
  const getTitle = computed(() => (modelType.value !== 'add' ? '编辑' : '新增'));
  const { createMessage, createConfirm } = useMessage();
  const [registerForm, rechargeForm] = useForm({
    schemas: formSchema,
    showActionButtonGroup: false,
    labelCol: { style: { width: '190px' } },
    labelAlign: 'right',
    //labelCol: { style: { width: '150px' } }
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    await rechargeForm.resetFields();
    modelType.value = !data.record.bannerId ? 'add' : 'edit';
    if (modelType.value !== 'add') {
      bannerId.value = data.record.bannerId;
      rechargeForm.setFieldsValue({
        imageUrl: data.record.imageUrl,
        jumpLink: data.record.jumpLink,
        sortOrder: data.record.sortOrder,
        terminal: data.record.terminal,
        title: data.record.title,
        startTime: data.record.startTime + ',' + data.record.endTime,
      });
    }
    console.log(closeModal);
  });

  const fileList = ref<UploadFile[]>([]);
  const uploading = ref(false);

  const beforeUpload: UploadProps['beforeUpload'] = (file) => {
    // 校验图片格式
    const isImage = /\.(jpg|jpeg|png|gif|webp|bmp)$/i.test(file.name) || /^image\/(jpeg|png|gif|webp|bmp)/.test(file.type);

    if (!isImage) {
      createMessage.error('只能上传图片格式文件 (JPG/JPEG/PNG/GIF/WEBP/BMP)!');
      return Upload.LIST_IGNORE; // 阻止文件进入列表
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      createMessage.error('文件大小不能超过10MB!');
      return Upload.LIST_IGNORE;
    }
    fileList.value = [file];
    return false; // 阻止自动上传
  };

  async function handleSubmit() {
    const values = await rechargeForm.validate();
    console.log(values, 'valuesvalues');
    // try {
    //   const values = await rechargeForm.validate();
    //   if (modelType.value !== 'add') {
    //     let params = {
    //       bannerId: bannerId.value,
    //       imageUrl: values.imageUrl,
    //       title: values.title,
    //       jumpLink: values.jumpLink,
    //       sortOrder: values.sortOrder,
    //       terminal: values.terminal,
    //       startTime: values.startTime.split(',')[0],
    //       endTime: values.startTime.split(',')[1],
    //     };
    //     await postBannerUpdata(params);

    //     //关闭弹窗
    //     closeModal();
    //     await rechargeForm.resetFields();
    //     createMessage.success('操作成功');
    //     //刷新列表
    //     emit('success');
    //   } else {
    //     let params = {
    //       imageUrl: values.imageUrl,
    //       title: values.title,
    //       jumpLink: values.jumpLink,
    //       sortOrder: values.sortOrder,
    //       terminal: values.terminal,
    //       startTime: values.startTime.split(',')[0],
    //       endTime: values.startTime.split(',')[1],
    //     };
    //     await postBannerSave(params);
    //     //关闭弹窗
    //     closeModal();
    //     await rechargeForm.resetFields();
    //     createMessage.success('操作成功');
    //     //刷新列表
    //     emit('success');
    //   }
    // } catch (error) {
    //   // createMessage.success('操作失败！');
    // }
  }
  async function handleBeforeClose(): Promise<boolean> {
    return await new Promise<boolean>((resolve) => {
      createConfirm({
        title: '确认关闭？',
        content: '所有未保存的更改将会丢失，确定要关闭吗？',
        iconType: 'warning',
        onOk: () => resolve(true),
        onCancel: () => resolve(false),
      });
    });
  }
</script>
<style scoped>
  .ml8 {
    margin-left: 8px;
  }
  .tips {
    color: #999;
  }
</style>
