<template>
  <BasicModal
    v-bind="$attrs"
    :title="getTitle"
    :defaultFullscreen="true"
    width="800px"
    @register="registerModal"
    @ok="handleSubmit"
    :closeFunc="handleBeforeClose"
  >
    <BasicForm @register="registerForm">
      <template #sortOrderSlot="{ model, field }">
        <InputNumber v-model:value="model[field]" :min="1" :precision="0" :step="1" placeholder="请输入" />
        <span class="ml8 tips">排序规则：数值越大，位置越优先</span>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts">
  // 单独添加一个script块用于导出
  export default {
    name: 'system-bannerModel', // 保持与你原来的name一致
  };
</script>
<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from './banner.data';
  import { postBannerSave, postBannerUpdata } from '/@/api/sys/banner';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { InputNumber } from 'ant-design-vue';
  import { func } from 'vue-types';
  const emit = defineEmits(['success', 'register']);
  let modelType = ref('add');
  let bannerId = ref(0);
  const getTitle = computed(() => (modelType.value !== 'add' ? '编辑' : '新增'));
  const { createMessage, createConfirm } = useMessage();
  const [registerForm, bannerForm] = useForm({
    schemas: formSchema,
    showActionButtonGroup: false,
    labelCol: { style: { width: '190px' } },
    labelAlign: 'right',
    //labelCol: { style: { width: '150px' } }
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    await bannerForm.resetFields();
    modelType.value = !data.record.bannerId ? 'add' : 'edit';
    if (modelType.value !== 'add') {
      bannerId.value = data.record.bannerId;
      bannerForm.setFieldsValue({
        imageUrl: data.record.imageUrl,
        jumpLink: data.record.jumpLink,
        sortOrder: data.record.sortOrder,
        terminal: data.record.terminal,
        title: data.record.title,
        startTime: data.record.startTime + ',' + data.record.endTime,
      });
    }
  });
  async function handleSubmit() {
    try {
      const values = await bannerForm.validate();
      if (modelType.value !== 'add') {
        let params = {
          bannerId: bannerId.value,
          imageUrl: values.imageUrl,
          title: values.title,
          jumpLink: values.jumpLink,
          sortOrder: values.sortOrder,
          terminal: values.terminal,
          startTime: values.startTime.split(',')[0],
          endTime: values.startTime.split(',')[1],
        };
        await postBannerUpdata(params);

        //关闭弹窗
        closeModal();
        await bannerForm.resetFields();
        createMessage.success('操作成功');
        //刷新列表
        emit('success');
      } else {
        let params = {
          imageUrl: values.imageUrl,
          title: values.title,
          jumpLink: values.jumpLink,
          sortOrder: values.sortOrder,
          terminal: values.terminal,
          startTime: values.startTime.split(',')[0],
          endTime: values.startTime.split(',')[1],
        };
        await postBannerSave(params);
        //关闭弹窗
        closeModal();
        await bannerForm.resetFields();
        createMessage.success('操作成功');
        //刷新列表
        emit('success');
      }
    } catch (error) {
      // createMessage.success('操作失败！');
    }
  }
  async function handleBeforeClose(): Promise<boolean> {
    return await new Promise<boolean>((resolve) => {
      createConfirm({
        title: '确认关闭？',
        content: '所有未保存的更改将会丢失，确定要关闭吗？',
        iconType: 'warning',
        onOk: () => resolve(true),
        onCancel: () => resolve(false),
      });
    });
  }
</script>
<style scoped>
  .ml8 {
    margin-left: 8px;
  }
  .tips {
    color: #999;
  }
</style>
