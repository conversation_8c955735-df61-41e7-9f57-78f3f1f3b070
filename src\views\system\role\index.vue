<template>
  <PageWrapper>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" v-auth="'system:role:add'" preIcon="ant-design:plus-outlined" @click="handleCreate"> 新增</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>
  </PageWrapper>
  <!--角色用户表格 暂不开放-->
  <!-- <RoleUserTable @register="roleUserDrawer" /> -->
  <!--角色编辑抽屉-->
  <RoleDrawer :showFooter="showFooter" @register="registerDrawer" @success="reload" />
  <!--角色详情-->
  <RoleDesc @register="registerDesc" />
  <!--角色菜单授权抽屉-->
  <RolePermissionDrawer @register="rolePermissionDrawer" />
</template>
<script lang="ts" name="system-role" setup>
  import { ref } from 'vue';
   import { message, Modal } from 'ant-design-vue';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useDrawer } from '/@/components/Drawer';
  import RoleDrawer from './components/RoleDrawer.vue';
  import RoleDesc from './components/RoleDesc.vue';
  import RolePermissionDrawer from './components/RolePermissionDrawer.vue';
  import { columns, searchFormSchema } from './role.data';
  import { list, deleteRole } from '/@/api/sys/role';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { usePermission } from '/@/hooks/web/usePermission'
  const { hasPermission } = usePermission();
  const showFooter = ref(true);
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [rolePermissionDrawer, { openDrawer: openRolePermissionDrawer }] = useDrawer();
  const [registerDesc] = useDrawer();

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'role-template',
    tableProps: {
      title: '系统角色列表',
      api: list,
      columns: columns,
      showIndexColumn: true,
      formConfig: {
        labelWidth: 100,
        schemas: searchFormSchema,
      },
      actionColumn: {
        width: 120,
      },
      rowSelection: {},
      //自定义默认排序
      defSort: {
        column: 'id',
        order: 'desc',
      },
    },
  });
  const [registerTable, { reload }] = tableContext;
  // 新增
  function handleCreate() {
    showFooter.value = true;
    openDrawer(true, {
      isUpdate: false,
    });
  }
  // 编辑
  function handleEdit(record: Recordable) {
    showFooter.value = true;
    openDrawer(true, {
      record,
      isUpdate: true,
    });
  }
   // 删除用户
  // function handleDelete(record) {
  //       Modal.confirm({
  //         title: '确认删除此角色?',
  //         okText: '确认',
  //         cancelText: '取消',
  //         onOk: () => {
            
  //         },
  //       });
  //     }
  async function handleDelete(record) {
    Modal.confirm({
          title: '确认删除此角色?',
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
             deleteRole({ id: record.id }, reload);
          },
        })
  }
  // 角色授权弹窗
  function handlePerssion(record) {
    openRolePermissionDrawer(true, { roleId: record.id });
  }
  // 操作栏
  function getTableAction(record) {
    return [
      {
        label: '权限',
        onClick: handlePerssion.bind(null, record),
        auth: 'system:role:permission',
      },
    ];
  }
  // 下拉操作栏
  function getDropDownAction(record) {
      return [
            {
              label: '编辑',
              onClick: handleEdit.bind(null, record),
              auth: 'sys_role_edit',
            },
            {
              label: '删除',
              onClick: handleDelete.bind(null, record),
              auth: 'sys_role_delete',
            },
          ];
   
  }
</script>
