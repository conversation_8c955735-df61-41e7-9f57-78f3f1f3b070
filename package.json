{"name": "ncs-web", "version": "3.6.0", "scripts": {"pinstall": "pnpm install", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "dev": "vite", "dev:4a": "cross-env platform=4a vite", "dev:cam": "cross-env platform=cam vite", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno ./build/script/postBuild.ts", "build:cam": "cross-env NODE_ENV=production platform=cam NODE_OPTIONS=--max-old-space-size=8192 vite build && cross-env platform=cam esno ./build/script/postBuild.ts", "build:4a": "cross-env NODE_ENV=production platform=4a NODE_OPTIONS=--max-old-space-size=8192 vite build && cross-env platform=4a esno ./build/script/postBuild.ts", "build:all": "pnpm run build:4a && pnpm run build:cam", "build:report": "pnpm clean:cache && cross-env REPORT=true npm run build", "preview": "npm run build && vite preview", "reinstall": "rimraf pnpm-lock.yaml && rimraf yarn.lock && rimraf package.lock.json && rimraf node_modules && pnpm run install", "clean:lib": "rimraf node_modules", "gen:icon": "esno ./build/generate/icon/index.ts", "batch:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "upgrade:log": "conventional-changelog -p angular -i CHANGELOG.md -s", "husky:install": "husky install", "prepare:pre-commit": "husky add .husky/pre-commit \"npm run lint:lint-staged\"", "lint:lint-staged": "lint-staged"}, "dependencies": {"@ant-design/colors": "^7.0.0", "@ant-design/icons-vue": "^6.1.0", "@iconify/iconify": "^3.1.1", "@jeecg/online": "3.5.3-vite4", "@logicflow/core": "^1.2.16", "@logicflow/extension": "^1.2.17", "@qiaoqiaoyun/drag-free": "^1.1.4", "@tinymce/tinymce-vue": "^6.1.0", "@vue/runtime-core": "3.3.6", "@vue/shared": "3.3.6", "@vueuse/core": "10.5.0", "@vueuse/shared": "10.5.0", "@zxcvbn-ts/core": "3.0.4", "ant-design-vue": "3.2.20", "axios": "^1.5.1", "china-area-data": "^5.0.1", "clipboard": "^2.0.11", "codemirror": "^5.65.15", "cron-parser": "^4.9.0", "cropperjs": "^1.6.1", "crypto-js": "4.1.1", "dayjs": "^1.11.10", "dom-align": "^1.12.4", "echarts": "^5.4.3", "emoji-mart-vue-fast": "^15.0.0", "enquire.js": "^2.1.6", "html2canvas": "^1.4.1", "intro.js": "^7.2.0", "jsencrypt": "3.3.2", "lodash-es": "^4.17.21", "lodash.get": "^4.4.2", "md5": "^2.3.0", "mitt": "^3.0.1", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "pinia": "2.1.6", "print-js": "^1.6.0", "qrcode": "^1.5.3", "qrcodejs2": "0.0.2", "qs": "^6.11.2", "resize-observer-polyfill": "^1.5.1", "showdown": "^2.1.0", "sortablejs": "^1.15.0", "tinymce": "^7.7.0", "vditor": "^3.9.6", "vue": "3.3.6", "vue-clipboard3": "^2.0.0", "vue-cropper": "^0.6.4", "vue-cropperjs": "^5.0.0", "vue-i18n": "9.2.2", "vue-infinite-scroll": "^2.0.2", "vue-json-pretty": "^2.2.4", "vue-pdf-embed": "^2.1.0", "vue-print-nb-jeecg": "^1.0.12", "vue-router": "^4.2.5", "vue-types": "^5.1.1", "vuedraggable": "^4.1.0", "vxe-table": "4.5.12", "vxe-table-plugin-antd": "3.1.0", "xe-utils": "3.5.13", "xss": "^1.0.14"}, "devDependencies": {"@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@iconify/json": "^2.2.131", "@purge-icons/generated": "^0.9.0", "@rys-fe/vite-plugin-theme": "^0.8.6", "@types/codemirror": "^5.60.12", "@types/crypto-js": "^4.1.3", "@types/fs-extra": "^11.0.3", "@types/inquirer": "^9.0.6", "@types/intro.js": "^5.1.3", "@types/jest": "^29.5.6", "@types/lodash-es": "^4.17.10", "@types/mockjs": "^1.0.9", "@types/node": "^20.8.7", "@types/nprogress": "^0.2.2", "@types/qrcode": "^1.5.4", "@types/qs": "^6.9.9", "@types/showdown": "^2.0.3", "@types/sortablejs": "^1.15.4", "@typescript-eslint/eslint-plugin": "^6.8.0", "@typescript-eslint/parser": "^6.8.0", "@vitejs/plugin-vue": "^4.4.0", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vue/compiler-sfc": "^3.3.6", "@vue/test-utils": "^2.4.1", "autoprefixer": "^10.4.16", "commitizen": "^4.3.0", "conventional-changelog-cli": "^3.0.0", "cross-env": "^7.0.3", "cz-git": "^1.7.1", "czg": "^1.7.1", "dotenv": "^16.3.1", "eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "eslint-define-config": "^1.24.1", "eslint-plugin-jest": "^27.4.3", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-vue": "^9.17.0", "esno": "^0.17.0", "fs-extra": "^11.1.1", "http-server": "^14.1.1", "husky": "^8.0.3", "inquirer": "^9.2.11", "is-ci": "^3.0.1", "jest": "^29.7.0", "less": "^4.2.0", "lint-staged": "14.0.1", "npm-run-all": "^4.1.5", "picocolors": "^1.0.0", "postcss": "^8.4.31", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^3.0.3", "pretty-quick": "^3.1.3", "rimraf": "^5.0.5", "rollup": "^3.29.4", "rollup-plugin-visualizer": "^5.9.2", "stylelint": "^15.11.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended": "^13.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^34.0.0", "stylelint-order": "^6.0.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^4.9.5", "unocss": "^0.55.7", "vite": "^4.5.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-mkcert": "^1.16.0", "vite-plugin-mock": "^2.9.8", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-eslint-parser": "^9.3.2", "vue-tsc": "^1.8.19"}, "repository": {"type": "git", "url": "git+https://github.com/jeecgboot/jeecgboot-vue3.git"}, "license": "MIT", "bugs": {"url": "https://github.com/jeecgboot/jeecgboot-vue3/issues"}, "homepage": "https://github.com/jeecgboot/jeecgboot-vue3", "engines": {"node": "^12 || >=14"}}