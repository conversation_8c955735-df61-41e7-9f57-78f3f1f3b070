export function downloadFile(response, file) {
  if (!response) return;
  const fileName = file ? file : '';
  const data = response;
  const objectURL = window.URL.createObjectURL(new Blob([data]));
  const link = document.createElement('a');
  link.href = objectURL;
  link.setAttribute('download', decodeURIComponent(fileName));
  document.body.appendChild(link);
  link.click();
  window.URL.revokeObjectURL(objectURL);
}
