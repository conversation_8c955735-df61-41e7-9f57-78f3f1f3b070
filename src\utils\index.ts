import type { RouteLocationNormalizedLoaded, RouteRecordNormalized } from 'vue-router';
import type { App, Plugin } from 'vue';

import { unref } from 'vue';
import { isObject } from '/@/utils/is';
import { initDictOptions } from '/@/utils/dict';
export const URL_HASH_TAB = `__AGWE4H__HASH__TAG__PWHRG__`;

export const noop = () => {};

/**
 * @description:  Set ui mount node
 */
export function getPopupContainer(node?: HTMLElement): HTMLElement {
  return (node?.parentNode as HTMLElement) ?? document.body;
}

/**
 * Add the object as a parameter to the URL
 * @param baseUrl url
 * @param obj
 * @returns {string}
 * eg:
 *  let obj = {a: '3', b: '4'}
 *  setObjToUrlParams('www.baidu.com', obj)
 *  ==>www.baidu.com?a=3&b=4
 */
export function setObjToUrlParams(baseUrl: string, obj: any): string {
  let parameters = '';
  for (const key in obj) {
    parameters += key + '=' + encodeURIComponent(obj[key]) + '&';
  }
  parameters = parameters.replace(/&$/, '');
  return /\?$/.test(baseUrl) ? baseUrl + parameters : baseUrl.replace(/\/?$/, '?') + parameters;
}

export function deepMerge<T = any>(src: any = {}, target: any = {}): T {
  let key: string;
  for (key in target) {
    src[key] = isObject(src[key]) ? deepMerge(src[key], target[key]) : (src[key] = target[key]);
  }
  return src;
}

export function openWindow(url: string, opt?: { target?: TargetContext | string; noopener?: boolean; noreferrer?: boolean }) {
  const { target = '__blank', noopener = true, noreferrer = true } = opt || {};
  const feature: string[] = [];

  noopener && feature.push('noopener=yes');
  noreferrer && feature.push('noreferrer=yes');

  window.open(url, target, feature.join(','));
}

// dynamic use hook props
export function getDynamicProps<T, U>(props: T): Partial<U> {
  const ret: Recordable = {};

  Object.keys(props).forEach((key) => {
    ret[key] = unref((props as Recordable)[key]);
  });

  return ret as Partial<U>;
}

/**
 * 获取表单字段值数据类型
 * @param props
 * @param field
 * @updateBy:zyf
 */
export function getValueType(props, field) {
  const formSchema = unref(unref(props)?.schemas);
  let valueType = 'string';
  if (formSchema) {
    const schema = formSchema.filter((item) => item.field === field)[0];
    valueType = schema.componentProps && schema.componentProps.valueType ? schema.componentProps.valueType : valueType;
  }
  return valueType;
}

export function getRawRoute(route: RouteLocationNormalizedLoaded): RouteLocationNormalizedLoaded {
  if (!route) return route;
  const { matched, ...opt } = route;
  return {
    ...opt,
    matched: (matched
      ? matched.map((item) => ({
          meta: item.meta,
          name: item.name,
          path: item.path,
        }))
      : undefined) as RouteRecordNormalized[],
  };
}
/**
 * 深度克隆对象、数组
 * @param obj 被克隆的对象
 * @return 克隆后的对象
 */
export function cloneObject(obj) {
  return JSON.parse(JSON.stringify(obj));
}

export const withInstall = <T>(component: T, alias?: string) => {
  const comp = component as any;
  comp.install = (app: App) => {
    app.component(comp.name || comp.displayName, component);
    if (alias) {
      app.config.globalProperties[alias] = component;
    }
  };
  return component as T & Plugin;
};

/**
 * 获取url地址参数
 * @param paraName
 */
export function getUrlParam(paraName) {
  const url = document.location.toString();
  const arrObj = url.split('?');

  if (arrObj.length > 1) {
    const arrPara = arrObj[1].split('&');
    let arr;

    for (let i = 0; i < arrPara.length; i++) {
      arr = arrPara[i].split('=');

      if (arr !== null && arr[0] === paraName) {
        return arr[1];
      }
    }
  } else {
    return '';
  }
}

/**
 * 休眠（setTimeout的promise版）
 * @param ms 要休眠的时间，单位：毫秒
 * @param fn callback，可空
 * @return Promise
 */
export function sleep(ms: number, fn?: Fn) {
  return new Promise<void>((resolve) =>
    setTimeout(() => {
      fn && fn();
      resolve();
    }, ms)
  );
}

/**
 * 不用正则的方式替换所有值
 * @param text 被替换的字符串
 * @param checker  替换前的内容
 * @param replacer 替换后的内容
 * @returns {String} 替换后的字符串
 */
export function replaceAll(text, checker, replacer) {
  const lastText = text;
  text = text.replace(checker, replacer);
  if (lastText !== text) {
    return replaceAll(text, checker, replacer);
  }
  return text;
}

/**
 * 获取URL上参数
 * @param url
 */
export function getQueryVariable(url) {
  if (!url) return;

  let t, n, r;
  const i = url.split('?')[1];
  const s = {};
  (t = i.split('&')), (r = null), (n = null);
  for (const o in t) {
    const u = t[o].indexOf('=');
    if (u !== -1) {
      r = t[o].substr(0, u);
      n = t[o].substr(u + 1);
      s[r] = n;
    }
  }
  return s;
}

/**
 * 判断是否显示办理按钮
 * @param bpmStatus
 * @returns {*}
 */
export function showDealBtn(bpmStatus) {
  if (bpmStatus !== '1' && bpmStatus !== '3' && bpmStatus !== '4') {
    return true;
  }
  return false;
}

/**
 * 数字转大写
 * @param value
 * @returns {*}
 */
export function numToUpper(value) {
  if (value !== '') {
    const unit = ['仟', '佰', '拾', '', '仟', '佰', '拾', '', '角', '分'];
    const toDx = (n) => {
      switch (n) {
        case '0':
          return '零';
        case '1':
          return '壹';
        case '2':
          return '贰';
        case '3':
          return '叁';
        case '4':
          return '肆';
        case '5':
          return '伍';
        case '6':
          return '陆';
        case '7':
          return '柒';
        case '8':
          return '捌';
        case '9':
          return '玖';
      }
    };
    const lth = value.toString().length;
    value *= 100;
    value += '';
    const length = value.length;
    if (lth <= 8) {
      let result = '';
      for (let i = 0; i < length; i++) {
        if (i === 2) {
          result = '元' + result;
        } else if (i === 6) {
          result = '万' + result;
        }
        if (value.charAt(length - i - 1) === 0) {
          if (i !== 0 && i !== 1) {
            if (result.charAt(0) !== '零' && result.charAt(0) !== '元' && result.charAt(0) !== '万') {
              result = '零' + result;
            }
          }
          continue;
        }
        result = toDx(value.charAt(length - i - 1)) + unit[unit.length - i - 1] + result;
      }
      result += result.charAt(result.length - 1) === '元' ? '整' : '';
      return result;
    } else {
      return null;
    }
  }
  return null;
}

//update-begin-author:taoyan date:2022-6-8 for:解决老的vue2动态导入文件语法 vite不支持的问题
const allModules = import.meta.glob('../views/**/*.vue');
export function importViewsFile(path): Promise<any> {
  if (path.startsWith('/')) {
    path = path.substring(1);
  }
  let page = '';
  if (path.endsWith('.vue')) {
    page = `../views/${path}`;
  } else {
    page = `../views/${path}.vue`;
  }
  return new Promise((resolve, reject) => {
    let flag = true;
    for (const path in allModules) {
      if (path === page) {
        flag = false;
        allModules[path]().then((mod) => {
          resolve(mod);
        });
      }
    }
    if (flag) {
      reject('该文件不存在:' + page);
    }
  });
}
//update-end-author:taoyan date:2022-6-8 for:解决老的vue2动态导入文件语法 vite不支持的问题

/**
 * 跳转至积木报表的 预览页面
 * @param url
 * @param id
 * @param token
 */
export function goJmReportViewPage(url, id, token) {
  // update-begin--author:liaozhiyang---date:20230904---for：【QQYUN-6390】eval替换成new Function，解决build警告
  // URL支持{{ window.xxx }}占位符变量
  url = url.replace(/{{([^}]+)?}}/g, (_s1, s2) => _eval(s2));
  // update-end--author:liaozhiyang---date:20230904---for：【QQYUN-6390】eval替换成new Function，解决build警告
  if (url.includes('?')) {
    url += '&';
  } else {
    url += '?';
  }
  url += `id=${id}`;
  url += `&token=${token}`;
  window.open(url);
}

/**
 * 获取随机颜色
 */
export function getRandomColor(index?) {
  const colors = [
    'rgb(100, 181, 246)',
    'rgb(77, 182, 172)',
    'rgb(255, 183, 77)',
    'rgb(229, 115, 115)',
    'rgb(149, 117, 205)',
    'rgb(161, 136, 127)',
    'rgb(144, 164, 174)',
    'rgb(77, 208, 225)',
    'rgb(129, 199, 132)',
    'rgb(255, 138, 101)',
    'rgb(133, 202, 205)',
    'rgb(167, 214, 118)',
    'rgb(254, 225, 89)',
    'rgb(251, 199, 142)',
    'rgb(239, 145, 139)',
    'rgb(169, 181, 255)',
    'rgb(231, 218, 202)',
    'rgb(252, 128, 58)',
    'rgb(254, 161, 172)',
    'rgb(194, 163, 205)',
  ];
  return index && index < 19 ? colors[index] : colors[Math.floor(Math.random() * (colors.length - 1))];
}

export function getRefPromise(componentRef) {
  return new Promise((resolve) => {
    (function next() {
      const ref = componentRef.value;
      if (ref) {
        resolve(ref);
      } else {
        setTimeout(() => {
          next();
        }, 100);
      }
    })();
  });
}

/**
 * 2023-09-04
 * liaozhiyang
 * 用new Function替换eval
 */
export function _eval(str: string) {
  return new Function(`return ${str}`)();
}

//映射等级
export function convertKolLevels(levels) {
  const levelMap = {
    '0': 'LV0',
    '1': 'LV1',
    '2': 'LV2',
    '3': 'LV3',
    '4': 'LV4',
    '5': 'LV5',
    '6': 'LV6',
    '7': 'LV7',
    // Add more mappings if necessary
  };

  const levelArray = levels.split(','); // 将字符串拆分为数组

  const convertedLevels = levelArray.map((level) => {
    return levelMap[level]; // 使用映射将数字转换为对应的字符串
  });

  return convertedLevels.join(','); // 将转换后的字符串数组连接为一个字符串
}

//  关闭当前页
export function closePage() {
  if (navigator.userAgent.indexOf('Firefox') !== -1 || navigator.userAgent.indexOf('Chrome') !== -1) {
    window.location.href = 'about:blank';
    window.close();
  } else {
    window.opener = null;
    window.open('', '_self');
    window.close();
  }
}

export function randomString(len) {
  len = len || 17;
  const $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprst=wxyz2345678';
  const maxPos = $chars.length;
  let random = '';
  for (let i = 0; i < len; i++) {
    random += $chars.charAt(Math.floor(Math.random() * maxPos));
  }
  return random;
}

export async function getDicOptions(key) {
  let result = await initDictOptions(key);
  let result_array = result.reduce((prev, next) => {
    if (next) {
      const value = next['value'];
      prev.push({
        label: next['text'] || next['label'],
        value: +value,
        color: next['color'],
        itemValue: value,
        // ...omit(next, ['text', 'value', 'color']),
      });
    }
    return prev;
  }, []);
  return result_array;
}

export async function filterDictList(value, key, defaultStr = '—') {
  let _str = '';
  if (!value && value !== 0) return defaultStr;
  if (Array.isArray(value)) {
    const _arr = value.map((el) => filterDictList(el, key));
    _str = _arr.join('、');
  } else {
    value = value + '';
    const _centerList = (await initDictOptions(key)) || [];
    const filterList = _centerList.filter((el) => el.value + '' === value);
    if (filterList.length) _str = filterList[0].label || filterList[0].text || filterList[0].title;
  }
  return _str || value;
}
