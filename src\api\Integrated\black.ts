import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';
import axios from 'axios';
import { getToken } from '/@/utils/auth';
enum Api {
  batchSave = '/biz/blackList/batchSave',
  blackAdd = '/biz/blackList/add',
  pageList = '/biz/blackList/pageList',
  blackDelete = '/biz/blackList/delete',
  getBlacklistTemplate = '/biz/blackList/getBlacklistTemplate',
  batchSftp = '/biz/blackList/batchSftp',
}

/**
 * 批量上传黑名单
 * @param params
 */
// export const batchSave = (params = {}) => defHttp.post({ url: Api.batchSave, params });// 文件上传 api
export const batchSave = ({ file }) => {
  const formData = new FormData();
  formData.append('file', file);
  const token = getToken();
  return axios({
    url: '/zqqy-manager' + Api.batchSave,
    method: 'POST',
    data: formData,
    headers: {
      'Content-type': 'multipart/form-data',
      Authorization: token,
      'X-Access-Token': token,
    },
  });
};

/**
 * 添加黑名单
 * @param params
 */
export const blackAdd = (params = {}) => defHttp.post({ url: Api.blackAdd, params });

/**
 * 分页获取黑名单列表
 * @param params
 */
export const pageList = (params = {}) => defHttp.get({ url: Api.pageList, params });

/**
 * 删除黑名单
 * @param params
 */
export const blackDelete = (params = {}) => defHttp.post({ url: Api.blackDelete, params });

/**
 * 获取黑名单模板
 * @param params
 */
export const getBlacklistTemplate = (params = {}) =>
  defHttp.get(
    {
      url: Api.getBlacklistTemplate,
      params,
      responseType: 'blob', // 设置响应类型为blob
    },
    {
      isReturnNativeResponse: true, // 返回原始响应以获取headers
    }
  );

/**
 * 读取黑名单
 * @param params
 */
export const batchSftp = (params = {}) => defHttp.get({ url: Api.batchSftp, params });
