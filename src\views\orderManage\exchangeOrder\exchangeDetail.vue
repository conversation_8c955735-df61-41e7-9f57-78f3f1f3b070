<template>
  <BasicModal
    v-bind="$attrs"
    title="兑换明细"
    :defaultFullscreen="false"
    :width="'80%'"
    @register="registerModal"
    :showCancelBtn="false"
    :show-ok-btn="false"
  >
    <h4>兑换权益总数量{{ exchangeTotal }}，兑换成功{{ exchangeSuccess }}，兑换失败{{ exchangeFail }}，兑换应支付总金额{{ exchangePrice }}</h4>
    <a-table :dataSource="exchangeList" :columns="exchangeColumns" :scroll="{ x: 'max-content' }"></a-table>

    <h4 style="margin-top: 16px">支付卡券明细</h4>
    <a-table :dataSource="paymentList" :columns="paymentColumns" :scroll="{ x: 'max-content' }"></a-table>
  </BasicModal>
</template>
<script lang="ts">
  // 单独添加一个script块用于导出
  export default {
    name: 'orderManage-exchangeModal', // 保持与你原来的name一致
  };
</script>
<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { getDetail } from '/@/api/order/exchange-order';
  import { exchangeColumns, paymentColumns } from './exchangeOrder.data';
  const emit = defineEmits(['success', 'register']);
  let stepsList = reactive<any>([]);
  let exchangeList = reactive<any>([]);
  let paymentList = reactive<any>([]);
  let exchangeSuccess = ref(0);
  let exchangeFail = ref(0);
  let exchangeTotal = ref(0);
  let exchangePrice = ref(0);
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    let res = await getDetail({ batchId: data.record.batchId });
    console.log(res, exchangeColumns, paymentColumns, '===>data');
    exchangeList.length = 0;
    paymentList.length = 0;
    let _centerArr = res && res.couponsOrderDetailVOList ? res.couponsOrderDetailVOList : [];
    let _paymentList = _centerArr.map((el, i) => {
      el.index = i + 1;
      return el;
    });
    paymentList.push(..._paymentList);
    let _centerArr2 = res && res.equityConvertOrderDetailVOList ? res.equityConvertOrderDetailVOList : [];
    let _exchangeList = _centerArr2.map((el, i) => {
      el.index = i + 1;
      return el;
    });
    exchangeList.push(..._exchangeList);
    exchangeSuccess.value = res ? res.successCount : 0;
    exchangeFail.value = res ? res.failCount : 0;
    exchangeTotal.value = res ? res.totalCount : 0;
    exchangePrice.value = res ? res.price : 0;
    console.log(closeModal);
  });
</script>
<style scoped>
  .ml8 {
    margin-left: 8px;
  }
  .tips {
    color: #999;
  }
  .custom-desc > div {
    margin-bottom: 8px;
    line-height: 16px;
  }
</style>
