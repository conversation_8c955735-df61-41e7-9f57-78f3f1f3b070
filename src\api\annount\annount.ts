import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';
import axios from "axios";
import { getToken } from '/@/utils/auth';
enum Api {
  annountList = '/biz/announcement/list',
  annountAdd = '/biz/announcement/saveOrUpdate',
  annountWithdraw = '/biz/announcement/withdraw',
  annountDetail = '/biz/announcement/info',
  annountEnable = '/biz/announcement/enable',
  imgUpload = '/biz/announcement/upload',
}

/**
 * 公告列表
 * @param params
 */
export const getAnnountList = (params) => {
  return defHttp.post({ url: Api.annountList, params });
};
/**
 * 公告新增编辑
 * @param params
 */
export const annountAdd = (params = {}) => defHttp.post({ url: Api.annountAdd, params });
/**
 * 公告撤回
 * @param params
 */

export const  annountWithdraw= (params = {}) => defHttp.post({ url: Api.annountWithdraw, params });
/**
 * 公告详情
 * @param params
 */
export const getAnnountDetail = (params = {}) => defHttp.post({ url: Api.annountDetail, params });
/**
 * 公告激活
 * @param params
 */
export const getAnnountEnable = (params = {}) => defHttp.post({ url: Api.annountEnable, params });
// 文件上传 api
export const imganUpload = ({ file }) => {

  const formData = new FormData();
  formData.append('file', file);
  
  console.log(formData,'formData')
  const token = getToken();
  return axios({
    url: '/zqqy-manager'+ Api.imgUpload,
    method: 'POST',
    data: formData,
    headers: {
      'Content-type': 'multipart/form-data',
      Authorization:token,
      'X-Access-Token':token
    },
  });
};