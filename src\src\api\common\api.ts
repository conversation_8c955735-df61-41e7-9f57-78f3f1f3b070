import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';
import { getFileAccessUrl, saveBlob } from '/@/utils/common/compUtils';
const globSetting = useGlobSetting();
enum Api {
  positionList = '/sys/position/list',
  userList = '/sys/user/list',
  roleList = '/sys/role/list',
  queryDepartTreeSync = '/sys/sysDepart/queryDepartTreeSync',
  queryTreeList = '/sys/sysDepart/queryTreeList',
  loadTreeData = '/sys/category/loadTreeData',
  loadDictItem = '/sys/category/loadDictItem/',
  getDictItems = '/sys/dict/getDictItems/',
  getTableList = '/sys/user/queryUserComponentData',
  getCategoryData = '/sys/category/loadAllData',
  getAllSettleList = '/sys/sysDepart/getAllSettleList',
  getRegionTree = '/biz/region/regionTree',
}
/**
 * 上传父路径
 */
export const uploadUrl = `${globSetting.apiUrl}/file/upload`;

/**
 * 下载父路径
 */
export const downloadUrl = `${globSetting.apiUrl}/file/download`;
//获取所属组织
export const getAllSettleListApi = (params = {}) => {
  return defHttp.post({ url: Api.getAllSettleList, params });
};
/**
 * 获取省市县树
 * @param params
 * @returns
 */
export const getRegonTree = (params = { level: 1 }) => {
  return defHttp.post({ url: Api.getRegionTree, params });
};
/**
 * 职务列表
 * @param params
 */
export const getPositionList = (params) => {
  return defHttp.post({ url: Api.positionList, params });
};

/**
 * 用户列表
 * @param params
 */
export const getUserList = (params) => {
  return defHttp.post({ url: Api.userList, params });
};

/**
 * 角色列表
 * @param params
 */
export const getRoleList = (params) => {
  return defHttp.post({ url: Api.roleList, params });
};

/**
 * 异步获取部门树列表
 */
export const queryDepartTreeSync = (params?) => {
  return defHttp.post({ url: Api.queryDepartTreeSync, params });
};
/**
 * 获取部门树列表
 */
export const queryTreeList = (params?) => {
  return defHttp.post({ url: Api.queryTreeList, params });
};

/**
 * 分类字典树控件 加载节点
 */
export const loadTreeData = (params?) => {
  return defHttp.post({ url: Api.loadTreeData, params });
};

/**
 * 根据字典code加载字典text
 */
export const loadDictItem = (params?) => {
  return defHttp.post({ url: Api.loadDictItem, params });
};

/**
 * 根据字典code加载字典text
 */
export const getDictItems = (dictCode) => {
  return defHttp.post({ url: Api.getDictItems + dictCode }, { joinTime: false });
};
/**
 * 部门用户modal选择列表加载list
 */
export const getTableList = (params) => {
  return defHttp.post({ url: Api.getTableList, params });
};
/**
 * 加载全部分类字典数据
 */
export const loadCategoryData = (params) => {
  return defHttp.post({ url: Api.getCategoryData, params });
};
/**
 * 文件上传
 */
export const uploadFile = (params, success) => {
  return defHttp.uploadFile({ url: uploadUrl }, params, { success });
};
/**
 * 下载图片视频或者导出数据
 * @param fileId 文件id
 * @param fileName 文件名
 * @param downloadOrExport 下载或者导出
 * @returns {*}
 */
export const downloadFile = async (fileName, fileId) => {
  const getUrl = getFileAccessUrl(fileId);
  const len = globSetting.apiUrl.length;
  const url = getUrl.startsWith(globSetting.apiUrl) ? getUrl.slice(len) : getUrl;
  const data = await getFileblob(url);
  if (!data || data.size === 0) {
    message.warning('文件下载失败');
    return;
  }
  saveBlob(data, fileName);
};
/**
 * 下载文件 用于excel导出
 * @param url
 * @param parameter
 * @returns {*}
 */
export const exportFile = async (url, parameter = {}, fileName = '下载.xlsx') => {
  const data = await defHttp.post(
    {
      url: url,
      params: parameter,
      responseType: 'blob',
    },
    { isTransformResponse: false }
  );
  if (!data || data.size === 0) {
    message.warning('文件下载失败');
    return;
  }
  saveBlob(data, fileName);
};
/**
 * 获取blob文件流
 */
export const getFileblob = (url, parameter = {}) => {
  return defHttp.get(
    {
      url: url,
      params: parameter,
      responseType: 'blob',
    },
    { isTransformResponse: false, joinTime: false, isReturnNativeResponse: true }
  );
};

/**
 * 【用于评论功能】自定义文件上传-方法
 */
export const uploadMyFile = (url, data) => {
  return defHttp.uploadMyFile(url, data);
};
