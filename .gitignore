node_modules
.DS_Store
.github
#dist
.npmrc
.cache

/dist

tests/server/static
tests/server/static/upload
/zqqy-manager-front
zqqy-manager-front
zqqy-manager-front.zip
node_modules
.DS_Store
.github
dist
dist.tar.gz
.npmrc
.cache
package-lock.json
*.tar.gz
.local
# local env files
.env.local
.env.*.local
.eslintcache

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Editor directories and files
.idea
# .vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
/os_del.cmd
os_del.cmd
/.vscode/
/.history/
/svn clear.bat
