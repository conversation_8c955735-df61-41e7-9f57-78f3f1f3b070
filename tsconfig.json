{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "strictFunctionTypes": false, "strictNullChecks": true, "jsx": "preserve", "baseUrl": ".", "allowJs": true, "sourceMap": true, "esModuleInterop": true, "resolveJsonModule": true, "noUnusedLocals": true, "noUnusedParameters": true, "experimentalDecorators": true, "lib": ["dom", "esnext"], "types": ["vite/client", "jest"], "typeRoots": ["./node_modules/@types/", "./types"], "noImplicitAny": false, "skipLibCheck": true, "paths": {"/@/*": ["src/*"], "/#/*": ["types/*"], "@/*": ["src/*"], "#/*": ["types/*"]}}, "include": ["tests/**/*.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "build/**/*.ts", "build/**/*.d.ts", "mock/**/*.ts", "vite.config.ts"], "exclude": ["node_modules", "tests/server/**/*.ts", "dist", "**/*.js"]}