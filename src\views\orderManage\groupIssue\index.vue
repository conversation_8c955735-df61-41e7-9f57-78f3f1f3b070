<template>
  <PageWrapper>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" v-auth="'order:groupIssue:excelExport'" @click="handleExcel()"> 导出</a-button>
      </template>
      <template #cardStatusStr="{ text }">
        <a-tag :color="text === '已领取' ? 'success' : 'error'">{{ text }}</a-tag>
      </template>
      <template #orderResult="{ text }">
        <a-tag :color="+text === 1 ? 'success' : +text === 2 ? 'error' : ''">{{ +text === 1 ? '已成功' : +text === 2 ? '失败' : '处理中' }}</a-tag>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 验收弹窗 -->
    <!-- <CheckModel @register="registerCheckModal" @success="handleSuccess" /> -->
    <transferModel @register="registerPropertyModal" @success="handleSuccess" />
  </PageWrapper>
</template>

<script lang="ts" name="orderManage-transfer" setup>
  import { reactive, ref } from 'vue';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction, ActionItem, TableImg } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { equityOrdersList, excelExport } from '/@/api/order/group-issue';
  import { columns, searchFormSchema } from './components/issue.data';
  import { useModal } from '/@/components/Modal';
  import { Modal } from 'ant-design-vue';
  import transferModel from './components/transferModel.vue';
  import { exportExcel } from '/@/utils/common/compUtils';
  import { func } from 'vue-types';
  const [registerPropertyModal, { openModal }] = useModal();
  // 列表页面公共参数、方法
  // 列表页面公共参数、方法
  let useSearchForm = reactive({});
  const { tableContext } = useListPage({
    designScope: 'jsUser-list',
    tableProps: {
      api: equityOrdersList,
      rowSelection: {},
      columns: columns,
      size: 'small',
      showIndexColumn: true,
      // showActionColumn: false,
      formConfig: {
        showAdvancedButton: false,
        labelWidth: 100,
        schemas: searchFormSchema,
        actionColOptions: {
          xs: 24, // <576px
          sm: 24, // ≥576px
          md: 24, // ≥768px
          lg: 24, // ≥992px
          xl: 24, // ≥1200px
          xxl: 24,
          style: { textAlign: 'right' },
        },
      }, // 是否使用搜索项
      actionColumn: {
        width: 120,
      },
      beforeFetch: (params) => {
        delete params.column;
        delete params.order;
        let createTimes = params.dateOp && params.dateOp.includes(',') ? params.dateOp.split(',') : '';
        if (createTimes.length > 0) {
          let startDate = createTimes[0];
          let endDate = createTimes[1];
          params.startTime = startDate;
          params.endTime = endDate;
        }
        delete params.dateOp;
        console.log(params, 'params');
      },
    },
  });

  //注册table数据
  const [registerTable, { reload, getForm }] = tableContext;
  let recordData = reactive({});
  function handleDetail(record: Recordable) {
    Object.assign(recordData, record);
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '转赠记录',
        onClick: handleDetail.bind(null, record),
        auth: 'order:groupIssue:transfer',
        ifShow: !!record.transId,
      },
    ];
  }
  function handleExcel() {
    Modal.confirm({
      title: '是否导出集团客户发放订单？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        let { getFieldsValue } = getForm();
        let params = Object.assign({}, getFieldsValue());
        let createTimes = params.dateOp && params.dateOp.includes(',') ? params.dateOp.split(',') : '';
        if (createTimes.length > 0) {
          let startDate = createTimes[0];
          let endDate = createTimes[1];
          params.startTime = startDate;
          params.endTime = endDate;
        }
        delete params.dateOp;
        console.log(params, 'params');
        const res = await excelExport(params);
        exportExcel(res);
      },
    });
  }
  function handleSuccess() {
    reload();
  }
</script>
