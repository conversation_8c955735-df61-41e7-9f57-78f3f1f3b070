<template>
  <BasicModal v-bind="$attrs" :title="'编辑'" :defaultFullscreen="false" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="addForm">
      <template #equityId="{ model, field }">
        <a-select
          :disabled="!!recordId"
          v-model:value="model[field]"
          show-search
          placeholder="请选择"
          style="width: 200px"
          :filter-option="filterOption"
          @focus="handleFocus"
          @blur="handleBlur"
          @change="handleChange"
          option-filter-prop="children"
        >
          <template v-for="item in equityOptions" :key="`${item.equityId}`">
            <a-select-option :value="item.equityId">{{ item.equityName }}</a-select-option>
          </template>
        </a-select>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts">
  // 单独添加一个script块用于导出
  export default {
    name: 'orderManage-transferModel', // 保持与你原来的name一致
  };
</script>
<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formAddSchema } from './fullEquity.data';
  import { fullEquityEdit, fullEquityAdd, getConfigRights } from '/@/api/productconfig/full-equity';
  import { useMessage } from '/@/hooks/web/useMessage';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  const [addForm, formAddBlack] = useForm({
    schemas: formAddSchema,
    showActionButtonGroup: false,
    labelCol: { style: { width: '150px' } },
  });
  let recordId = ref('');
  const equityOptions = reactive<any[]>([]);
  const [registerModal, { closeModal }] = useModalInner(async (data) => {
    console.log(data.record, '传入的参数');
    recordId.value = data.record.id;
    await formAddBlack.resetFields();
    equityOptions.length = 0;
    let res: any = await getConfigRights({});
    equityOptions.push(...res);
    if (data.record.id) {
      recordId.value = data.record.id;
      formAddBlack.setFieldsValue({
        equityId: +data.record.equityId,
        // equityName: data.record.equityName,
        onlineRange: data.record.onlineRange,
        sort: data.record.sort,
      });
    }
  });

  function filterOption(input, option) {
    const item = equityOptions.find((i) => i.equityId === option.value);
    if (!item) return false;

    // 搜索以下字段
    const searchFields = [
      item.equityName,
      //  item.commodityCode, item.equityId.toString()
    ];

    return searchFields.some((field) => field.toLowerCase().includes(input.toLowerCase()));
  }
  function handleFocus() {}
  function handleBlur() {}
  function handleChange(value) {
    console.log('选择的值', value);
  }

  async function handleSubmit() {
    const values = await formAddBlack.validate();
    let params = {
      ...values,
    };
    if (recordId.value) {
      params.id = recordId.value;
      console.log('params', params);
      //编辑
      await fullEquityEdit(params);
    } else {
      params.equityName = equityOptions.find((item) => item.equityId === values.equityId).equityName;
      await fullEquityAdd(params);
    }

    //关闭弹窗
    closeModal();
    await formAddBlack.resetFields();
    createMessage.success('操作成功');
    //刷新列表
    emit('success');
  }
</script>
<style scoped>
  .ml8 {
    margin-left: 8px;
  }
  .tips {
    color: #999;
  }
  .custom-desc > div {
    margin-bottom: 8px;
    line-height: 16px;
  }
</style>
