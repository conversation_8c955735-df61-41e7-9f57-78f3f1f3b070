<template>
  <BasicModal v-bind="$attrs" :title="'C端商品排序模式设置'" :defaultFullscreen="false" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="addForm"> </BasicForm>
  </BasicModal>
</template>
<script lang="ts">
  // 单独添加一个script块用于导出
  export default {
    name: 'orderManage-transferModel', // 保持与你原来的name一致
  };
</script>
<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSortSchema } from './fullEquity.data';
  import { fullEquityQuery, fullEquitySet } from '/@/api/productconfig/full-equity';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { config } from 'dotenv';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  const [addForm, formAddBlack] = useForm({
    schemas: formSortSchema,
    showActionButtonGroup: false,
    labelCol: { style: { width: '150px' } },
  });
  let recordData = reactive({ configId: '' });
  const [registerModal, { closeModal }] = useModalInner(async (data) => {
    console.log(data.record, '传入的参数');
    await formAddBlack.resetFields();
    let res = await fullEquityQuery({});
    if (res) {
      let _centerObj = {
        configId: res.configId || '',
        productClassMode: res.goodsSortMode.productClassMode,
        ...res.goodsSortMode.hotSalesMode,
        ...res.goodsSortMode.newSalesMode,
      };
      Object.assign(recordData, _centerObj);
      formAddBlack.setFieldsValue(recordData);
    }

    console.log(res, recordData, '查询结果');
  });

  async function handleSubmit() {
    const values = await formAddBlack.validate();
    let params: any = {
      goodsSortMode: {
        productClassMode: values.productClassMode,
        hotSalesMode: {
          hotSalesNum: values.hotSalesNum,
          hotSortMode: values.hotSortMode,
          salesTimeRange: values.salesTimeRange,
        },
        newSalesMode: {
          newSalesNum: values.newSalesNum,
          newSalesSortMode: values.newSalesSortMode,
        },
      },
    };
    if (recordData && recordData.configId) {
      params.configId = recordData.configId;
    }
    //调用接口
    await fullEquitySet(params);
    //关闭弹窗
    closeModal();
    await formAddBlack.resetFields();
    createMessage.success('操作成功');
    //刷新列表
    emit('success');
  }
</script>
<style scoped>
  .ml8 {
    margin-left: 8px;
  }
  .tips {
    color: #999;
  }
  .custom-desc > div {
    margin-bottom: 8px;
    line-height: 16px;
  }
</style>
