<template>
  <PageWrapper>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" v-auth="'system:depart:add'" preIcon="ant-design:plus-outlined" @click="handleCreate">新增</a-button>
        <a-button type="primary" preIcon="ic:round-expand" @click="expandAll">展开全部</a-button>
        <a-button type="primary" preIcon="ic:round-compress" @click="collapseAll">折叠全部</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>
    <DepartFormDrawer :rootTreeData="rootTreeData" @success="handleSuccess" @register="registerDrawer" />
  </PageWrapper>
</template>
<script lang="ts" name="system-menu" setup>
  import { onMounted, ref } from 'vue';
  import { ActionItem, BasicTable, TableAction } from '/@/components/Table';
  import DepartFormDrawer from './components/DepartFormDrawer.vue';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useDrawer } from '/@/components/Drawer';
  import { columns, searchFormSchema } from './depart.data';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { queryTreeList } from '/@/api/common/api';
  import { deleteDepart, forbiddenDepart } from '/@/api/sys/depart';
  const showFooter = ref(true);
  const [registerDrawer, { openDrawer }] = useDrawer();
  // 列表页面公共参数、方法
  const rootTreeData = ref([]);
  const { tableContext } = useListPage({
    tableProps: {
      title: '组织列表',
      api: queryTreeList,
      dataSource: rootTreeData.value,
      columns: columns,
      size: 'small',
      pagination: false,
      isTreeTable: true,
      striped: true,
      showTableSetting: true,
      bordered: true,
      showIndexColumn: false,
      tableSetting: { fullScreen: true, redo: false },
      useSearchForm: false, // 是否使用搜索项
      actionColumn: {
        width: 120,
      },
      formConfig: {
        // update-begin--author:liaozhiyang---date:20230803---for：【QQYUN-5873】查询区域lablel默认居左
        labelWidth: 65,
        rowProps: { gutter: 24 },
        // update-end--author:liaozhiyang---date:20230803---for：【QQYUN-5873】查询区域lablel默认居左
        schemas: searchFormSchema,
      },
    },
  });
  //注册table数据
  const [registerTable, { expandAll, collapseAll, setProps, setTableData }] = tableContext;
  /**
   * 新增
   */
  function handleCreate() {
    showFooter.value = true;
    openDrawer(true, {
      isUpdate: false,
    });
  }

  /**
   * 编辑
   */
  function handleEdit(record) {
    showFooter.value = true;
    openDrawer(true, {
      record,
      isUpdate: true,
    });
  }
  /**
   * 添加下级
   */
  function handleAddSub(record) {
    openDrawer(true, {
      record: { parentId: record.id },
      isUpdate: false,
      isChild: true,
    });
  }

  /**
   * 删除
   */
  async function handleDelete(record) {
    await deleteDepart(record.id).then(() => {
      loadRootTreeData();
    });
  }
  //禁用
  async function handleDisable(record) {
    await forbiddenDepart(record.id).then(() => {
      loadRootTreeData();
    });
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    loadRootTreeData();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'system:depart:edit',
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record): ActionItem[] {
    let disableOrEnable = record.status === '0' ? '启用' : '禁用';
    return [
      // {
      //   label: '添加下级',
      //   onClick: handleAddSub.bind(null, record),
      // },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
        auth: 'system:depart:delete',
      },
      {
        label: disableOrEnable,
        onClick: handleDisable.bind(null, record),
        auth: 'system:depart:disabled',
      },
    ];
  }
  /**
   * 获取rootTree数据
   */
  function loadRootTreeData() {
    setProps({ loading: true });
    queryTreeList()
      .then((res) => {
        rootTreeData.value = res;
        setTableData(res);
      })
      .finally(() => {
        setProps({ loading: false });
      });
  }
  onMounted(() => {
    loadRootTreeData();
  });
</script>
