<template>
  <div class="search-select-container">
    <a-space>
      <a-select
        v-model:value="selectedValues"
        mode="multiple"
        style="width: 300px"
        placeholder="请输入搜索内容"
        :filter-option="false"
        :not-found-content="fetching ? undefined : null"
        @search="handleSearch"
        @dropdown-visible-change="handleDropdownVisibleChange"
        show-search
        allow-clear
        :open="dropdownVisible"
        :search-value="searchInputValue"
      >
        <a-select-option v-for="item in options" :key="item.value" :value="item.value">
          {{ item.label }}
        </a-select-option>
        <template #notFoundContent>
          <a-spin v-if="fetching" size="small" />
        </template>
      </a-select>

      <a-button type="primary" @click="handleSearchButtonClick" :loading="fetching"> 搜索 </a-button>
    </a-space>
  </div>
</template>

<script lang="ts" setup>
  import { ref, nextTick } from 'vue';
  import { message } from 'ant-design-vue';

  interface OptionItem {
    value: string;
    label: string;
  }

  const selectedValues = ref<string[]>([]);
  const searchInputValue = ref<string>(''); // 用于绑定搜索输入框的值
  const searchValue = ref<string>(''); // 用于存储实际的搜索值
  const options = ref<OptionItem[]>([]);
  const fetching = ref<boolean>(false);
  const dropdownVisible = ref<boolean>(false);

  const handleSearch = (value: string): void => {
    searchInputValue.value = value; // 更新搜索输入框的值
    searchValue.value = value; // 同时更新实际搜索值
  };

  const handleDropdownVisibleChange = (open: boolean): void => {
    // 只有当不是正在获取数据时才允许关闭
    if (!fetching.value) {
      dropdownVisible.value = open;
    }
  };

  const fetchData = async (query: string): Promise<OptionItem[]> => {
    try {
      fetching.value = true;
      dropdownVisible.value = true; // 确保开始获取数据时下拉框是打开的

      // 模拟API请求
      await new Promise((resolve) => setTimeout(resolve, 800));

      return [
        { value: `${query}_1`, label: `${query} 结果1` },
        { value: `${query}_2`, label: `${query} 结果2` },
        { value: `${query}_3`, label: `${query} 结果3` },
        { value: `${query}_4`, label: `${query} 结果4` },
        { value: `${query}_5`, label: `${query} 结果5` },
      ];
    } catch (error) {
      message.error('获取数据失败');
      return [];
    } finally {
      fetching.value = false;
    }
  };

  const handleSearchButtonClick = async (): Promise<void> => {
    if (!searchValue.value.trim()) {
      message.warning('请输入搜索内容');
      return;
    }

    // 确保下拉框打开
    dropdownVisible.value = true;

    const data = await fetchData(searchValue.value);
    options.value = data;

    // 确保UI更新后保持下拉框状态
    await nextTick();
    dropdownVisible.value = true;

    // 恢复搜索输入框的值
    searchInputValue.value = searchValue.value;
  };
</script>

<style scoped>
  .search-select-container {
    margin: 20px;
  }
</style>
