<template>
  <div class="clearfix">
    <a-upload
      v-model:fileList="uploadFileList"
      :listType="listType"
      accept="image/*"
      :multiple="multiple"
      :action="uploadImgUrl"
      :headers="headers"
      :data="{ biz: bizPath }"
      :beforeUpload="beforeUpload"
      :disabled="disabled"
      @change="handleChange"
      @preview="handlePreview"
    >
      <div v-if="uploadVisible">
        <div v-if="listType === 'picture-card'">
          <LoadingOutlined v-if="loading" />
          <UploadOutlined v-else />
          <div class="ant-upload-text">{{ text }}</div>
        </div>
        <a-button v-if="listType === 'picture'" :disabled="disabled">
          <UploadOutlined />
          {{ text }}
        </a-button>
      </div>
    </a-upload>
    <p class="grey" v-if="promptText">{{ promptText }}</p>
    <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel()">
      <img alt="example" class="w-full" :src="previewImage" />
    </a-modal>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref, computed, watch } from 'vue';
  import { LoadingOutlined, UploadOutlined } from '@ant-design/icons-vue';
  import { useRuleFormItem } from '/@/hooks/component/useFormItem';
  import { propTypes } from '/@/utils/propTypes';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getFileAccessUrl, getHeaders, getRandom } from '/@/utils/common/compUtils';
  import { uploadImgUrl } from '/@/api/common/api';

  const { createMessage } = useMessage();
  export default defineComponent({
    name: 'JImageUpload',
    components: { LoadingOutlined, UploadOutlined },
    inheritAttrs: false,
    props: {
      //绑定值
      value: propTypes.oneOfType([propTypes.string, propTypes.array]),
      //按钮文本
      listType: {
        type: String,
        required: false,
        default: 'picture-card',
      },
      //按钮文本
      text: {
        type: String,
        required: false,
        default: '点击上传',
      },
      //这个属性用于控制文件上传的业务路径
      bizPath: {
        type: String,
        required: false,
        default: 'temp',
      },
      //是否禁用
      disabled: {
        type: Boolean,
        required: false,
        default: false,
      },
      //上传数量
      fileMax: {
        type: Number,
        required: false,
        default: 1,
      },
      fileSize: {
        type: Number,
        default: 20,
      },
      promptText: {
        type: String,
        default: '',
      },
    },
    emits: ['options-change', 'change', 'update:value'],
    setup(props) {
      const emitData = ref<any[]>([]);
      const attrs = useAttrs();
      const [state] = useRuleFormItem(props, 'value', 'change', emitData);
      //获取文件名
      const getFileName = (path) => {
        if (path.lastIndexOf('\\') >= 0) {
          let reg = new RegExp('\\\\', 'g');
          path = path.replace(reg, '/');
        }
        return path.substring(path.lastIndexOf('/') + 1);
      };
      //token
      const headers = getHeaders(uploadImgUrl, null);
      //上传状态
      const loading = ref<boolean>(false);
      //是否是初始化加载
      const initTag = ref<boolean>(true);
      //文件列表
      let uploadFileList = ref<any[]>([]);
      //预览图
      const previewImage = ref<string | undefined>('');
      //预览框状态
      const previewVisible = ref<boolean>(false);

      //计算是否开启多图上传
      const multiple = computed(() => {
        return props['fileMax'] > 1;
      });

      //计算是否可以继续上传
      const uploadVisible = computed(() => {
        return uploadFileList.value.length < props['fileMax'];
      });

      /**
       * 监听value变化
       */
      watch(
        () => props.value,
        (val) => {
          //update-begin---author:liusq ---date:20230601  for：【issues/556】JImageUpload组件value赋初始值没显示图片------------
          if (val && val instanceof Array) {
            val = val.join(',');
          }
          if (initTag.value === true) {
            initFileList(val);
          }
        },
        { immediate: true }
        //update-end---author:liusq ---date:20230601  for：【issues/556】JImageUpload组件value赋初始值没显示图片------------
      );

      /**
       * 初始化文件列表
       * @param {string} 上传文件ID字符串以,分割
       */
      function initFileList(paths) {
        if (!paths || paths.length === 0) {
          uploadFileList.value = [];
          return;
        }
        let files = [];
        let arr = paths.split(',');
        arr.forEach((value) => {
          // let url = getFileAccessUrl(value);
          let url = value;
          //@ts-ignore
          files.push({
            uid: getRandom(10),
            name: getFileName(value),
            status: 'done',
            url: url,
            response: {
              status: 'history',
              message: value,
            },
          });
        });
        uploadFileList.value = files;
      }

      /**
       * 上传前校验
       */
      function beforeUpload(file) {
        let fileType = file.type;
        if (fileType.indexOf('image') < 0) {
          createMessage.info('请上传图片');
          return false;
        }
        console.log(props, '33333333');
        if (file.size / 1024 / 1024 > props.fileSize) {
          createMessage.info(`文件大小不能超过${props.fileSize}M`);
          return false;
        }
      }
      /**
       * 文件上传结果回调
       */
      function handleChange({ file, fileList }) {
        initTag.value = false;
        uploadFileList.value = fileList;
        if (file.status === 'error') {
          createMessage.error(`${file.name} 上传失败.`);
        }
        let fileIds: string[] = [];
        //上传完成
        if (file.status !== 'uploading') {
          fileList.forEach((file) => {
            if (file.status === 'done') {
              //update-begin---author:wangshuai ---date:20221121  for：[issues/248]原生表单内使用图片组件,关闭弹窗图片组件值不会被清空------------
              //initTag.value = true;
              setTimeout(() => (initTag.value = true), 0);
              const response = file.response;

              const id = response.status === 'history' ? response.message : response.result;
              //update-end---author:wangshuai ---date:20221121  for：[issues/248]原生表单内使用图片组件,关闭弹窗图片组件值不会被清空------------
              id && fileIds.push(id as string);
            }
          });
          if (file.status === 'removed') {
            handleDelete(file);
          }
          state.value = fileIds.join(',');
        }
      }
      /**
       * 删除图片
       */
      function handleDelete(_file) {
        //如有需要新增 删除逻辑
      }
      /**
       * 预览图片
       */
      function handlePreview(file) {
        console.log(file, 'file====>');
        previewImage.value = file.url || file.thumbUrl;
        previewVisible.value = true;
      }
      function handleCancel() {
        previewVisible.value = false;
      }

      return {
        state,
        attrs,
        previewImage,
        previewVisible,
        uploadFileList,
        multiple,
        headers,
        loading,
        uploadImgUrl,
        beforeUpload,
        uploadVisible,
        handlePreview,
        handleCancel,
        handleChange,
      };
    },
  });
</script>
<style scoped>
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }
  .grey {
    color: #a6a6a6;
  }
</style>
