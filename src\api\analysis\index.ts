import { defHttp } from '@/utils/http/axios';
import API_URL from '/@/api/url';
//任务分布
export const taskDistributionApi = (params) => {
  return defHttp.post({ url: API_URL.analysis.taskDistribution, params });
};
//任务数据
export const taskDataApi = (params) => {
  return defHttp.post({ url: API_URL.analysis.taskData, params });
};
//MCN数据（卓望用户可见）
export const mcnDataApi = (params) => {
  return defHttp.post({ url: API_URL.analysis.mcnData, params });
};
//达人分布（地域）
export const kolRegionApi = (params) => {
  return defHttp.post({ url: API_URL.analysis.kolRegion, params });
};
//达人数据
export const kolDataApi = (params) => {
  return defHttp.post({ url: API_URL.analysis.kolData, params });
};
//客户分布（地域）
export const customerRegionApi = (params) => {
  return defHttp.post({ url: API_URL.analysis.customerRegion, params });
};
//数据分析-概览
export const basicDataApi = (params) => {
  return defHttp.post({ url: API_URL.analysis.basicData, params });
};
