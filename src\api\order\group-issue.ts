import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';
import { e } from 'unocss';

enum Api {
  equityOrdersList = '/biz/equityOrders/list',
  giveList = '/biz/equityOrders/giveRecords',
  excelExport = '/biz/equityOrders/excelExport',
  // other API endpoints
}
export const equityOrdersList = (params) => {
  return defHttp.post({ url: Api.equityOrdersList, params });
};

export const giveList = (params) => {
  return defHttp.post({ url: Api.giveList, params });
};

export const excelExport = (params = {}) =>
  defHttp.post(
    {
      url: Api.excelExport,
      params,
      responseType: 'blob', // 设置响应类型为blob
    },
    {
      isReturnNativeResponse: true, // 返回原始响应以获取headers
    }
  );
