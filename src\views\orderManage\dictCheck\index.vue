<template>
  <PageWrapper>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <!-- <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleCreate"> 新增</a-button> -->
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 验收弹窗 -->
    <CheckModel @register="registerCheckModal" @success="handleSuccess" />
  </PageWrapper>
</template>
<script lang="ts" name="system-user" setup>
  import { Modal } from 'ant-design-vue';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import CheckModel from './components/checkModel.vue';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { columns, searchFormSchema } from './components/check.data';
  import { getDictCheckList, dictTaskDone } from '/@/api/order/dict-check';

  const { createMessage } = useMessage();
  //model
  const [registerCheckModal, { openModal }] = useModal();
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'dictCheck-list',
    tableProps: {
      api: getDictCheckList,
      rowSelection: {},
      columns: columns,
      size: 'small',
      // showIndexColumn: true,
      formConfig: {
        showAdvancedButton: false,
        labelWidth: 100,
        schemas: searchFormSchema,
        actionColOptions: {
          xs: 24, // <576px
          sm: 24, // ≥576px
          md: 24, // ≥768px
          lg: 24, // ≥992px
          xl: 24, // ≥1200px
          xxl: 24,
          style: { textAlign: 'right' },
        },
      },
      actionColumn: {
        width: 120,
      },
      beforeFetch: (params) => {
        delete params.column;
        delete params.order;
        // return Object.assign({ }, params);
      },
    },
  });

  //注册table数据
  const [registerTable, { reload }] = tableContext;
  // 新增事件
  function handleCreate(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: false,
      showFooter: true,
    });
  }
  // 详情
  async function handleDetail(record: Recordable) {
    console.log(record, 'record');
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  // 成功回调
  function handleSuccess() {
    reload();
  }
  // 终止
  function handleTaskDone(record, status) {
    Modal.confirm({
      title: '终止后不可恢复，是否确认终止?',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        dictTaskDone({ taskId: record.taskId, status: 2 }).then(() => {
          handleSuccess();
        });
      },
    });
  }
  // 操作栏
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '查看',
        onClick: handleDetail.bind(null, record),
        auth: 'order:check:detail',
      },
      {
        label: '验收',
        onClick: handleCreate.bind(null, record),
        ifShow: record.status === 0,
        auth: 'order:check:add',
      },
      {
        label: '终止',
        onClick: handleTaskDone.bind(null, record),
        ifShow: record.status === 0,
        auth: 'order:check:done',
      },
    ];
  }
</script>
